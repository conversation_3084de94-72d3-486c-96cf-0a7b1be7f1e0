"""
Enhanced OCR Table Extraction with Advanced Image Processing and PDF Support
"""

import os
import sys
import pytesseract
from docx import Document
from PIL import Image, ImageEnhance, ImageFilter
from docx.shared import Inches
from docx.enum.table import WD_TABLE_ALIGNMENT
import re
from tkinter import filedialog, messagebox
from tkinter import Tk
import subprocess

# Advanced image processing
try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
    print("✅ OpenCV available - Advanced image processing enabled")
except ImportError:
    OPENCV_AVAILABLE = False
    print("⚠️  OpenCV not available - Install with: pip install opencv-python")

# PDF processing
try:
    import fitz  # PyMuPDF
    from pdf2image import convert_from_path
    PDF_SUPPORT = True
    print("✅ PDF support available")
except ImportError:
    PDF_SUPPORT = False
    print("⚠️  PDF support not available - Install with: pip install PyMuPDF pdf2image")

# LaTeX OCR
try:
    from pix2tex.cli import LatexOCR
    LATEX_OCR_AVAILABLE = True
    print("✅ LaTeX OCR available - Mathematical content enhancement enabled")
except ImportError:
    LATEX_OCR_AVAILABLE = False
    print("⚠️  LaTeX OCR not available - Install with: pip install pix2tex")

# Performance monitoring
try:
    import psutil
    PERFORMANCE_MONITORING = True
except ImportError:
    PERFORMANCE_MONITORING = False

def advanced_image_preprocessing(img):
    """Advanced image preprocessing using OpenCV"""
    if not OPENCV_AVAILABLE:
        return preprocess_image_for_table(img)
    
    # Convert PIL to OpenCV format
    img_array = np.array(img)
    
    # Convert to grayscale if needed
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array
    
    # Advanced preprocessing pipeline
    print("  - Applying advanced image preprocessing...")
    
    # 1. Noise reduction
    denoised = cv2.fastNlMeansDenoising(gray)
    
    # 2. Adaptive histogram equalization for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(denoised)
    
    # 3. Morphological operations to clean up text
    kernel = np.ones((1,1), np.uint8)
    cleaned = cv2.morphologyEx(enhanced, cv2.MORPH_CLOSE, kernel)
    
    # 4. Sharpening filter
    kernel_sharp = np.array([[-1,-1,-1],
                            [-1, 9,-1],
                            [-1,-1,-1]])
    sharpened = cv2.filter2D(cleaned, -1, kernel_sharp)
    
    # 5. Threshold for better text clarity
    _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # Convert back to PIL
    return Image.fromarray(binary)

def preprocess_image_for_table(img):
    """Basic image preprocessing for better table OCR"""
    # Convert to grayscale if needed
    if img.mode != 'L':
        img = img.convert('L')
    
    # Enhance contrast
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.5)
    
    # Enhance sharpness
    enhancer = ImageEnhance.Sharpness(img)
    img = enhancer.enhance(2.0)
    
    # Apply slight blur to reduce noise
    img = img.filter(ImageFilter.MedianFilter(size=3))
    
    return img

def extract_math_with_latex_ocr(img):
    """Extract mathematical expressions using LaTeX OCR"""
    if not LATEX_OCR_AVAILABLE:
        return None
    
    try:
        # Initialize LaTeX OCR model
        model = LatexOCR()
        
        # Convert PIL image to format expected by LaTeX OCR
        latex_result = model(img)
        
        return latex_result
    except Exception as e:
        print(f"LaTeX OCR failed: {e}")
        return None

def is_mathematical_content(text):
    """Detect if text contains mathematical expressions"""
    math_patterns = [
        r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
        r'\d+[xy]',  # Coefficients with variables
        r'[=<>≤≥]',  # Mathematical operators
        r'\d+/\d+',  # Fractions
        r'[αβγδεζηθικλμνξοπρστυφχψω]',  # Greek letters
        r'\^|\_{|}',  # Superscripts/subscripts
        r'\\[a-zA-Z]+',  # LaTeX commands
    ]
    
    return any(re.search(pattern, text) for pattern in math_patterns)

def extract_table_from_ocr_data(data, original_img=None):
    """Extract table structure from OCR data with LaTeX OCR enhancement"""
    try:
        # Get text, coordinates, and confidence
        texts = data['text']
        lefts = data['left']
        tops = data['top']
        widths = data['width']
        heights = data['height']
        confs = data['conf']
        
        # Filter out low confidence and empty text
        valid_data = []
        for i, text in enumerate(texts):
            if text.strip() and confs[i] > 30:  # Only keep confident text
                valid_data.append({
                    'text': text.strip(),
                    'left': lefts[i],
                    'top': tops[i],
                    'right': lefts[i] + widths[i],
                    'bottom': tops[i] + heights[i],
                    'width': widths[i],
                    'height': heights[i]
                })
        
        if not valid_data:
            return None
        
        # Sort by vertical position (top coordinate)
        valid_data.sort(key=lambda x: x['top'])
        
        # Group into rows based on vertical proximity
        rows = []
        current_row = []
        row_threshold = 20  # pixels
        
        for item in valid_data:
            if not current_row:
                current_row.append(item)
            else:
                # Check if this item is on the same row as the current row
                avg_top = sum(x['top'] for x in current_row) / len(current_row)
                if abs(item['top'] - avg_top) <= row_threshold:
                    current_row.append(item)
                else:
                    # Start new row
                    if current_row:
                        rows.append(current_row)
                    current_row = [item]
        
        if current_row:
            rows.append(current_row)
        
        # Sort each row by horizontal position and enhance mathematical content
        table_data = []
        for row in rows:
            row.sort(key=lambda x: x['left'])
            row_texts = []
            
            for item in row:
                text = item['text']
                
                # If this looks like mathematical content and we have LaTeX OCR available
                if is_mathematical_content(text) and LATEX_OCR_AVAILABLE and original_img:
                    try:
                        # Extract the region of this cell from the original image
                        cell_img = original_img.crop((
                            max(0, item['left'] - 5), max(0, item['top'] - 5), 
                            min(original_img.width, item['right'] + 5), 
                            min(original_img.height, item['bottom'] + 5)
                        ))
                        
                        # Try LaTeX OCR on this cell
                        latex_result = extract_math_with_latex_ocr(cell_img)
                        if latex_result and latex_result.strip():
                            text = f"${latex_result}$"  # Wrap in LaTeX math delimiters
                            print(f"  - Enhanced math cell: {text}")
                    except Exception as e:
                        print(f"  - LaTeX OCR failed for cell, using regular OCR: {e}")
                
                row_texts.append(text)
            
            if len(row_texts) > 1:  # Only keep rows with multiple columns
                table_data.append(row_texts)
        
        return table_data if len(table_data) > 1 else None
    except Exception as e:
        print(f"Table extraction error: {e}")
        return None

def convert_pdf_to_images(pdf_path):
    """Convert PDF pages to images for OCR processing"""
    if not PDF_SUPPORT:
        print("PDF support not available")
        return []
    
    try:
        print(f"Converting PDF to images: {pdf_path}")
        images = convert_from_path(pdf_path, dpi=300)  # High DPI for better OCR
        return images
    except Exception as e:
        print(f"PDF conversion failed: {e}")
        return []

def monitor_performance():
    """Monitor system performance during processing"""
    if not PERFORMANCE_MONITORING:
        return

    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    print(f"  - System: CPU {cpu_percent}%, Memory {memory.percent}%")

def parse_text_to_table(text):
    """Try to parse plain text into table format"""
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    if not lines:
        return None

    # Look for common table patterns
    table_data = []

    for line in lines:
        # Try to split by multiple spaces, tabs, or common separators
        # First try splitting by multiple spaces (2 or more)
        cells = re.split(r'\s{2,}|\t', line)

        # If that doesn't work well, try other separators
        if len(cells) < 2:
            cells = re.split(r'\s+', line)

        # Clean up cells
        cells = [cell.strip() for cell in cells if cell.strip()]

        if len(cells) > 1:  # Only add rows with multiple columns
            table_data.append(cells)

    return table_data if len(table_data) > 1 else None

def create_word_table(doc, table_data):
    """Create a Word table from table data"""
    if not table_data:
        return

    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)

    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Fill table with data
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                cell = row.cells[j]
                cell.text = str(cell_data)

                # Make first row bold (headers)
                if i == 0:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True

def process_image_to_docx(image_path, output_path):
    try:
        print(f"🔄 Processing {image_path}...")
        monitor_performance()

        img = Image.open(image_path)

        # Use advanced preprocessing if OpenCV is available
        if OPENCV_AVAILABLE:
            img_processed = advanced_image_preprocessing(img)
            print("  ✅ Advanced OpenCV preprocessing applied")
        else:
            img_processed = preprocess_image_for_table(img)
            print("  ✅ Basic image preprocessing applied")

        # Try multiple OCR configurations for better table detection
        configs = [
            '--psm 6 -c preserve_interword_spaces=1',  # Single uniform block
            '--psm 4 -c preserve_interword_spaces=1',  # Single column of text
            '--psm 3 -c preserve_interword_spaces=1',  # Fully automatic page segmentation
            '--psm 1 -c preserve_interword_spaces=1',  # Automatic page segmentation with OSD
        ]

        best_table_data = None
        best_text = ""

        print("  🔍 Testing multiple OCR configurations...")
        for i, config in enumerate(configs):
            try:
                text = pytesseract.image_to_string(img_processed, config=config)
                data = pytesseract.image_to_data(img_processed, output_type=pytesseract.Output.DICT, config=config)
                table_data = extract_table_from_ocr_data(data, img_processed)

                if table_data and len(table_data) > 1:
                    # Check if this result looks better (more columns, more structured)
                    avg_cols = sum(len(row) for row in table_data) / len(table_data)
                    if best_table_data is None or avg_cols > sum(len(row) for row in best_table_data) / len(best_table_data):
                        best_table_data = table_data
                        best_text = text
                        print(f"    ✅ Config {i+1}: Better structure ({len(table_data)} rows, avg {avg_cols:.1f} cols)")
                        break
            except Exception as e:
                print(f"    ❌ Config {i+1}: Failed ({e})")
                continue

        # Use the best result or fallback
        if not best_table_data:
            text = pytesseract.image_to_string(img_processed, config='--psm 6 -c preserve_interword_spaces=1')
            best_text = text

        print(f"  📊 Found {len(best_table_data) if best_table_data else 0} table rows")

        doc = Document()
        doc.add_heading(f'Enhanced OCR Table: {os.path.basename(image_path)}', level=1)

        # Use the best table data found
        final_table_data = best_table_data
        final_text = best_text if best_table_data else text

        if final_table_data and len(final_table_data) > 1:  # If we have table data with multiple rows
            print("  📝 Creating Word table with enhanced formatting")
            create_word_table(doc, final_table_data)
        else:
            # Fallback: try to parse text into table format
            fallback_table_data = parse_text_to_table(final_text)
            if fallback_table_data and len(fallback_table_data) > 1:
                print("  📝 Creating Word table from text parsing")
                create_word_table(doc, fallback_table_data)
            else:
                # If no table structure detected, add as paragraphs
                print("  📄 No table structure detected, adding as formatted text")
                doc.add_paragraph("Raw extracted text:")
                for line in final_text.splitlines():
                    if line.strip():
                        doc.add_paragraph(line)

        doc.save(output_path)
        print(f"✅ Successfully created {output_path}")
        return True
    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        return False

def process_pdf_to_docx(pdf_path, output_dir):
    """Process PDF file by converting pages to images and extracting tables"""
    if not PDF_SUPPORT:
        print("❌ PDF support not available")
        return False

    try:
        print(f"🔄 Processing PDF: {pdf_path}")
        images = convert_pdf_to_images(pdf_path)

        if not images:
            print("❌ No images extracted from PDF")
            return False

        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
        success_count = 0

        for i, img in enumerate(images):
            page_num = i + 1
            output_path = os.path.join(output_dir, f"{pdf_name}_page_{page_num}.docx")

            print(f"📄 Processing page {page_num}/{len(images)}")

            # Save image temporarily for processing
            temp_img_path = f"temp_page_{page_num}.png"
            img.save(temp_img_path)

            try:
                if process_image_to_docx(temp_img_path, output_path):
                    success_count += 1
            finally:
                # Clean up temporary image
                if os.path.exists(temp_img_path):
                    os.remove(temp_img_path)

        print(f"✅ PDF processing complete: {success_count}/{len(images)} pages successful")
        return success_count > 0

    except Exception as e:
        print(f"❌ Error processing PDF {pdf_path}: {e}")
        return False

# Configure Tesseract path - try common installation locations
import subprocess

def find_tesseract():
    """Try to find tesseract executable"""
    common_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME')),
        'tesseract'  # If it's in PATH
    ]

    for path in common_paths:
        try:
            if path == 'tesseract':
                # Test if tesseract is in PATH
                subprocess.run([path, '--version'], capture_output=True, check=True)
                return path
            elif os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                return path
        except (subprocess.CalledProcessError, FileNotFoundError, OSError):
            continue

    return None

def main():
    print("=" * 80)
    print("🚀 ENHANCED OCR TABLE EXTRACTION WITH ADVANCED FEATURES")
    print("=" * 80)
    print("Features:")
    print("  🖼️  Advanced OpenCV image preprocessing" if OPENCV_AVAILABLE else "  ⚠️  Basic image preprocessing (install opencv-python for advanced)")
    print("  📄 PDF support" if PDF_SUPPORT else "  ⚠️  No PDF support (install PyMuPDF pdf2image)")
    print("  🧮 LaTeX OCR for mathematical content" if LATEX_OCR_AVAILABLE else "  ⚠️  No LaTeX OCR (install pix2tex)")
    print("  📊 Performance monitoring" if PERFORMANCE_MONITORING else "  ⚠️  No performance monitoring (install psutil)")
    print("=" * 80)

    # Try to configure tesseract
    tesseract_path = find_tesseract()
    if not tesseract_path:
        messagebox.showerror("Tesseract Not Found",
                            "Tesseract OCR is not installed or not found in common locations.\n\n"
                            "Please install Tesseract OCR from:\n"
                            "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
                            "After installation, restart this program.")
        return

    print(f"✅ Tesseract found: {tesseract_path}")

    # Ask user to select input folder/files
    Tk().withdraw()
    input_dir = filedialog.askdirectory(title="Select Folder with Images/PDFs")
    if not input_dir:
        messagebox.showerror("Error", "No folder selected. Exiting.")
        return

    # Ask user to select output folder
    output_dir = filedialog.askdirectory(title="Select Output Folder for Word Files")
    if not output_dir:
        messagebox.showerror("Error", "No output folder selected. Exiting.")
        return

    # Process files
    supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    supported_pdf_formats = ['.pdf'] if PDF_SUPPORT else []
    all_supported_formats = supported_image_formats + supported_pdf_formats

    files_to_process = []
    for file in os.listdir(input_dir):
        if any(file.lower().endswith(ext) for ext in all_supported_formats):
            files_to_process.append(os.path.join(input_dir, file))

    if not files_to_process:
        messagebox.showinfo("No Files", f"No supported files found.\nSupported formats: {', '.join(all_supported_formats)}")
        return

    print(f"\n🔍 Found {len(files_to_process)} files to process")

    success_count = 0
    total_files = len(files_to_process)

    for i, file_path in enumerate(files_to_process):
        print(f"\n📁 Processing file {i+1}/{total_files}: {os.path.basename(file_path)}")
        monitor_performance()

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.pdf':
            if process_pdf_to_docx(file_path, output_dir):
                success_count += 1
        else:
            # Image file
            output_filename = f"{os.path.splitext(os.path.basename(file_path))[0]}_enhanced.docx"
            output_path = os.path.join(output_dir, output_filename)

            if process_image_to_docx(file_path, output_path):
                success_count += 1

    print("\n" + "=" * 80)
    print(f"🎉 PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {success_count}/{total_files} files")
    print(f"📁 Output location: {output_dir}")
    print("=" * 80)

    messagebox.showinfo("Processing Complete",
                       f"Successfully processed {success_count}/{total_files} files.\n\n"
                       f"Output saved to: {output_dir}")

if __name__ == "__main__":
    main()
