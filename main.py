import os
import pytesseract
from docx import Document
from PIL import Image, ImageEnhance, ImageFilter
from tkinter import Tk, filedialog, messagebox
from docx.shared import Inches
from docx.enum.table import WD_TABLE_ALIGNMENT

def preprocess_image_for_table(img):
    """Enhance image for better table OCR"""
    # Convert to grayscale if needed
    if img.mode != 'L':
        img = img.convert('L')

    # Enhance contrast
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.5)

    # Enhance sharpness
    enhancer = ImageEnhance.Sharpness(img)
    img = enhancer.enhance(2.0)

    # Apply slight blur to reduce noise
    img = img.filter(ImageFilter.MedianFilter(size=3))

    return img

# Ask user to select a folder of images
Tk().withdraw()
input_dir = filedialog.askdirectory(title="Select Folder with Table Images")
if not input_dir:
    messagebox.showerror("Error", "No folder selected. Exiting.")
    exit()

# Ask user to select output folder
output_dir = filedialog.askdirectory(title="Select Output Folder for Word Files")
if not output_dir:
    messagebox.showerror("Error", "No output folder selected. Exiting.")
    exit()

# Configure Tesseract path - try common installation locations
import subprocess

def find_tesseract():
    """Try to find tesseract executable"""
    common_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME')),
        'tesseract'  # If it's in PATH
    ]

    for path in common_paths:
        try:
            if path == 'tesseract':
                # Test if tesseract is in PATH
                subprocess.run([path, '--version'], capture_output=True, check=True)
                return path
            elif os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                return path
        except (subprocess.CalledProcessError, FileNotFoundError, OSError):
            continue

    return None

# Try to configure tesseract
tesseract_path = find_tesseract()
if not tesseract_path:
    messagebox.showerror("Tesseract Not Found",
                        "Tesseract OCR is not installed or not found in common locations.\n\n"
                        "Please install Tesseract OCR from:\n"
                        "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
                        "After installation, restart this program.")
    exit()

def process_image_to_docx(image_path, output_path):
    try:
        img = Image.open(image_path)

        # Preprocess image for better OCR
        img_processed = preprocess_image_for_table(img)

        # Try multiple OCR configurations for better table detection
        configs = [
            '--psm 6 -c preserve_interword_spaces=1',  # Single uniform block
            '--psm 4 -c preserve_interword_spaces=1',  # Single column of text
            '--psm 3 -c preserve_interword_spaces=1',  # Fully automatic page segmentation
        ]

        best_table_data = None
        best_text = ""

        for config in configs:
            try:
                text = pytesseract.image_to_string(img_processed, config=config)
                data = pytesseract.image_to_data(img_processed, output_type=pytesseract.Output.DICT, config=config)
                table_data = extract_table_from_ocr_data(data)

                if table_data and len(table_data) > 1:
                    # Check if this result looks better (more columns, more structured)
                    avg_cols = sum(len(row) for row in table_data) / len(table_data)
                    if best_table_data is None or avg_cols > sum(len(row) for row in best_table_data) / len(best_table_data):
                        best_table_data = table_data
                        best_text = text
                        break
            except:
                continue

        # Use the best result or fallback
        if not best_table_data:
            text = pytesseract.image_to_string(img_processed, config='--psm 6 -c preserve_interword_spaces=1')
            best_text = text

        # Add some debug information
        print(f"  - Found {len(best_table_data) if best_table_data else 0} table rows")

        doc = Document()
        doc.add_heading(f'Extracted Table: {os.path.basename(image_path)}', level=1)

        # Use the best table data found
        final_table_data = best_table_data
        final_text = best_text if best_table_data else text

        if final_table_data and len(final_table_data) > 1:  # If we have table data with multiple rows
            create_word_table(doc, final_table_data)
        else:
            # Fallback: try to parse text into table format
            fallback_table_data = parse_text_to_table(final_text)
            if fallback_table_data and len(fallback_table_data) > 1:
                create_word_table(doc, fallback_table_data)
            else:
                # If no table structure detected, add as paragraphs
                doc.add_paragraph("Raw extracted text:")
                for line in final_text.splitlines():
                    if line.strip():
                        doc.add_paragraph(line)

        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return False

def extract_table_from_ocr_data(data):
    """Extract table structure from OCR data"""
    try:
        # Group text by line (top coordinate)
        lines = {}
        for i, text in enumerate(data['text']):
            if text.strip():
                top = data['top'][i]
                left = data['left'][i]

                # Group by approximate line (allow some tolerance)
                line_key = round(top / 10) * 10
                if line_key not in lines:
                    lines[line_key] = []
                lines[line_key].append((left, text.strip()))

        # Sort lines by top position and create table
        table_data = []
        for line_key in sorted(lines.keys()):
            # Sort cells in each line by left position
            line_cells = sorted(lines[line_key], key=lambda x: x[0])
            row = [cell[1] for cell in line_cells]
            if row:  # Only add non-empty rows
                table_data.append(row)

        return table_data if len(table_data) > 1 else None
    except:
        return None

def parse_text_to_table(text):
    """Try to parse plain text into table format"""
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    if not lines:
        return None

    # Look for common table patterns
    table_data = []

    for line in lines:
        # Try to split by multiple spaces, tabs, or common separators
        # First try splitting by multiple spaces (2 or more)
        import re
        cells = re.split(r'\s{2,}|\t', line)

        # If that doesn't work well, try other separators
        if len(cells) < 2:
            cells = re.split(r'\s+', line)

        # Clean up cells
        cells = [cell.strip() for cell in cells if cell.strip()]

        if len(cells) > 1:  # Only add rows with multiple columns
            table_data.append(cells)

    return table_data if len(table_data) > 1 else None

def create_word_table(doc, table_data):
    """Create a Word table from table data"""
    if not table_data:
        return

    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)

    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'

    # Fill table
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                row.cells[j].text = str(cell_data)

    # Make first row bold (assuming it's header)
    if table.rows:
        for cell in table.rows[0].cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True

# Loop through image files
supported_ext = ('.png', '.jpg', '.jpeg', '.tif', '.bmp')
files = [f for f in os.listdir(input_dir) if f.lower().endswith(supported_ext)]

if not files:
    messagebox.showinfo("No Images", "No supported image files found in selected folder.")
    exit()

count = 0
for file in files:
    img_path = os.path.join(input_dir, file)
    output_path = os.path.join(output_dir, os.path.splitext(file)[0] + ".docx")
    if process_image_to_docx(img_path, output_path):
        count += 1

messagebox.showinfo("Done", f"Processed {count} files successfully.")
