import os
import pytesseract
from docx import Document
from PIL import Image
from tkinter import Tk, filedialog, messagebox

# Ask user to select a folder of images
Tk().withdraw()
input_dir = filedialog.askdirectory(title="Select Folder with Table Images")
if not input_dir:
    messagebox.showerror("Error", "No folder selected. Exiting.")
    exit()

# Ask user to select output folder
output_dir = filedialog.askdirectory(title="Select Output Folder for Word Files")
if not output_dir:
    messagebox.showerror("Error", "No output folder selected. Exiting.")
    exit()

# Configure Tesseract path - try common installation locations
import subprocess

def find_tesseract():
    """Try to find tesseract executable"""
    common_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME')),
        'tesseract'  # If it's in PATH
    ]

    for path in common_paths:
        try:
            if path == 'tesseract':
                # Test if tesseract is in PATH
                subprocess.run([path, '--version'], capture_output=True, check=True)
                return path
            elif os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                return path
        except (subprocess.CalledProcessError, FileNotFoundError, OSError):
            continue

    return None

# Try to configure tesseract
tesseract_path = find_tesseract()
if not tesseract_path:
    messagebox.showerror("Tesseract Not Found",
                        "Tesseract OCR is not installed or not found in common locations.\n\n"
                        "Please install Tesseract OCR from:\n"
                        "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
                        "After installation, restart this program.")
    exit()

def process_image_to_docx(image_path, output_path):
    try:
        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, config='--psm 6')  # Assume uniform block of text

        doc = Document()
        doc.add_heading(f'Extracted Table: {os.path.basename(image_path)}', level=1)
        for line in text.splitlines():
            if line.strip():
                doc.add_paragraph(line)
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return False

# Loop through image files
supported_ext = ('.png', '.jpg', '.jpeg', '.tif', '.bmp')
files = [f for f in os.listdir(input_dir) if f.lower().endswith(supported_ext)]

if not files:
    messagebox.showinfo("No Images", "No supported image files found in selected folder.")
    exit()

count = 0
for file in files:
    img_path = os.path.join(input_dir, file)
    output_path = os.path.join(output_dir, os.path.splitext(file)[0] + ".docx")
    if process_image_to_docx(img_path, output_path):
        count += 1

messagebox.showinfo("Done", f"Processed {count} files successfully.")
