import os
import pytesseract
from docx import Document
from PIL import Image
from tkinter import Tk, filedialog, messagebox

# Ask user to select a folder of images
Tk().withdraw()
input_dir = filedialog.askdirectory(title="Select Folder with Table Images")
if not input_dir:
    messagebox.showerror("Error", "No folder selected. Exiting.")
    exit()

# Ask user to select output folder
output_dir = filedialog.askdirectory(title="Select Output Folder for Word Files")
if not output_dir:
    messagebox.showerror("Error", "No output folder selected. Exiting.")
    exit()

# Configure Tesseract path - try common installation locations
import subprocess

def find_tesseract():
    """Try to find tesseract executable"""
    common_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME')),
        'tesseract'  # If it's in PATH
    ]

    for path in common_paths:
        try:
            if path == 'tesseract':
                # Test if tesseract is in PATH
                subprocess.run([path, '--version'], capture_output=True, check=True)
                return path
            elif os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                return path
        except (subprocess.CalledProcessError, FileNotFoundError, OSError):
            continue

    return None

# Try to configure tesseract
tesseract_path = find_tesseract()
if not tesseract_path:
    messagebox.showerror("Tesseract Not Found",
                        "Tesseract OCR is not installed or not found in common locations.\n\n"
                        "Please install Tesseract OCR from:\n"
                        "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
                        "After installation, restart this program.")
    exit()

def process_image_to_docx(image_path, output_path):
    try:
        img = Image.open(image_path)

        # Use PSM 6 for uniform block of text, but also try to get table structure
        text = pytesseract.image_to_string(img, config='--psm 6')

        # Try to get table data using pytesseract's data output
        try:
            data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT, config='--psm 6')
            table_data = extract_table_from_ocr_data(data)
        except:
            table_data = None

        doc = Document()
        doc.add_heading(f'Extracted Table: {os.path.basename(image_path)}', level=1)

        if table_data and len(table_data) > 1:  # If we have table data with multiple rows
            create_word_table(doc, table_data)
        else:
            # Fallback: try to parse text into table format
            table_data = parse_text_to_table(text)
            if table_data and len(table_data) > 1:
                create_word_table(doc, table_data)
            else:
                # If no table structure detected, add as paragraphs
                doc.add_paragraph("Raw extracted text:")
                for line in text.splitlines():
                    if line.strip():
                        doc.add_paragraph(line)

        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return False

def extract_table_from_ocr_data(data):
    """Extract table structure from OCR data"""
    try:
        # Group text by line (top coordinate)
        lines = {}
        for i, text in enumerate(data['text']):
            if text.strip():
                top = data['top'][i]
                left = data['left'][i]

                # Group by approximate line (allow some tolerance)
                line_key = round(top / 10) * 10
                if line_key not in lines:
                    lines[line_key] = []
                lines[line_key].append((left, text.strip()))

        # Sort lines by top position and create table
        table_data = []
        for line_key in sorted(lines.keys()):
            # Sort cells in each line by left position
            line_cells = sorted(lines[line_key], key=lambda x: x[0])
            row = [cell[1] for cell in line_cells]
            if row:  # Only add non-empty rows
                table_data.append(row)

        return table_data if len(table_data) > 1 else None
    except:
        return None

def parse_text_to_table(text):
    """Try to parse plain text into table format"""
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    if not lines:
        return None

    # Look for common table patterns
    table_data = []

    for line in lines:
        # Try to split by multiple spaces, tabs, or common separators
        # First try splitting by multiple spaces (2 or more)
        import re
        cells = re.split(r'\s{2,}|\t', line)

        # If that doesn't work well, try other separators
        if len(cells) < 2:
            cells = re.split(r'\s+', line)

        # Clean up cells
        cells = [cell.strip() for cell in cells if cell.strip()]

        if len(cells) > 1:  # Only add rows with multiple columns
            table_data.append(cells)

    return table_data if len(table_data) > 1 else None

def create_word_table(doc, table_data):
    """Create a Word table from table data"""
    if not table_data:
        return

    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)

    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'

    # Fill table
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                row.cells[j].text = str(cell_data)

    # Make first row bold (assuming it's header)
    if table.rows:
        for cell in table.rows[0].cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True

# Loop through image files
supported_ext = ('.png', '.jpg', '.jpeg', '.tif', '.bmp')
files = [f for f in os.listdir(input_dir) if f.lower().endswith(supported_ext)]

if not files:
    messagebox.showinfo("No Images", "No supported image files found in selected folder.")
    exit()

count = 0
for file in files:
    img_path = os.path.join(input_dir, file)
    output_path = os.path.join(output_dir, os.path.splitext(file)[0] + ".docx")
    if process_image_to_docx(img_path, output_path):
        count += 1

messagebox.showinfo("Done", f"Processed {count} files successfully.")
