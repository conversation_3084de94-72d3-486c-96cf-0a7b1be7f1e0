"""
Test script for equation insertion features
"""

import os
from docx import Document
from docx.enum.table import WD_TABLE_ALIGNMENT

# Import our enhanced functions
import sys
sys.path.append('.')

try:
    from main import latex_to_linear, insert_equation_in_cell, is_mathematical_content, create_word_table
    print("✅ Successfully imported equation functions")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    exit(1)

def test_latex_to_linear_conversion():
    """Test LaTeX to Linear conversion"""
    print("\n🔢 TESTING LATEX TO LINEAR CONVERSION")
    print("=" * 60)
    
    test_cases = [
        # Basic expressions
        ("x + 2y = 5", "x + 2y = 5"),
        ("3x - y = 0", "3x - y = 0"),
        
        # Fractions
        ("\\frac{1}{2}", "(1)/(2)"),
        ("1/2", "(1)/(2)"),
        ("\\frac{x+1}{y-1}", "(x+1)/(y-1)"),
        
        # Superscripts and subscripts
        ("x^2", "x^(2)"),
        ("x_1", "x_(1)"),
        ("x^{n+1}", "x^(n+1)"),
        ("a_{i,j}", "a_(i,j)"),
        
        # Greek letters
        ("\\alpha + \\beta = \\gamma", "α + β = γ"),
        ("\\theta = 45°", "θ = 45°"),
        
        # Mathematical operators
        ("x \\leq y", "x ≤ y"),
        ("a \\geq b", "a ≥ b"),
        ("x \\neq 0", "x ≠ 0"),
        
        # Square roots
        ("\\sqrt{x}", "√(x)"),
        ("\\sqrt{x^2 + y^2}", "√(x^(2) + y^(2))"),
        
        # Complex expressions
        ("2x^2 + 3x - 1 = 0", "2x^(2) + 3x - 1 = 0"),
    ]
    
    passed = 0
    for latex_input, expected in test_cases:
        result = latex_to_linear(latex_input)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{latex_input}' → '{result}'")
        if result != expected:
            print(f"      Expected: '{expected}'")
        else:
            passed += 1
    
    accuracy = (passed / len(test_cases)) * 100
    print(f"\n📊 Conversion accuracy: {accuracy:.1f}% ({passed}/{len(test_cases)})")
    return accuracy > 80

def test_mathematical_detection():
    """Test mathematical content detection"""
    print("\n🧮 TESTING MATHEMATICAL CONTENT DETECTION")
    print("=" * 60)
    
    test_cases = [
        ("x + 2y = 5", True),
        ("3x - y = 0", True),
        ("α + β = γ", True),
        ("1/2 + 3/4", True),
        ("Regular text", False),
        ("Name Age City", False),
        ("2x² + 3x - 1 = 0", True),
        ("Hello world", False),
        ("a = b + c", True),
        ("Temperature: 25°C", False),
    ]
    
    passed = 0
    for text, expected in test_cases:
        result = is_mathematical_content(text)
        status = "✅" if result == expected else "❌"
        detection = "MATH" if result else "TEXT"
        print(f"  {status} '{text}' → {detection}")
        if result == expected:
            passed += 1
    
    accuracy = (passed / len(test_cases)) * 100
    print(f"\n📊 Detection accuracy: {accuracy:.1f}% ({passed}/{len(test_cases)})")
    return accuracy > 80

def create_test_document():
    """Create a test document with equations"""
    print("\n📄 CREATING TEST DOCUMENT WITH EQUATIONS")
    print("=" * 60)
    
    # Create document
    doc = Document()
    doc.add_heading('Enhanced OCR with Equation Support - Test Document', level=1)
    
    # Add description
    doc.add_paragraph("This document demonstrates the enhanced equation insertion capabilities:")
    
    # Test data with various mathematical expressions
    test_table_data = [
        ["Equation Type", "Expression", "Description"],
        ["Linear Equation", "$x + 2y = 5$", "Simple linear equation"],
        ["Quadratic", "$2x^2 + 3x - 1 = 0$", "Quadratic equation"],
        ["Fraction", "$\\frac{1}{2} + \\frac{3}{4}$", "Fraction addition"],
        ["Greek Letters", "$\\alpha + \\beta = \\gamma$", "Greek letter equation"],
        ["Inequality", "$x \\leq y$", "Mathematical inequality"],
        ["Square Root", "$\\sqrt{x^2 + y^2}$", "Pythagorean theorem"],
        ["Regular Text", "Name", "Non-mathematical content"],
    ]
    
    print("  📊 Creating table with mixed content...")
    create_word_table(doc, test_table_data)
    
    # Add another section
    doc.add_heading('Mathematical Expressions Detected', level=2)
    doc.add_paragraph("The following expressions were automatically detected as mathematical content:")
    
    # Test individual expressions
    expressions = [
        "x + 2y = 5",
        "3x - y = 0", 
        "α + β = γ",
        "2x² + 3x - 1 = 0"
    ]
    
    for expr in expressions:
        if is_mathematical_content(expr):
            linear = latex_to_linear(expr)
            doc.add_paragraph(f"• Original: {expr}")
            doc.add_paragraph(f"  Linear format: {linear}")
    
    # Save document
    output_path = "equation_test_output.docx"
    doc.save(output_path)
    print(f"  ✅ Test document saved: {output_path}")
    
    return output_path

def run_equation_tests():
    """Run comprehensive equation feature tests"""
    print("🚀 ENHANCED OCR EQUATION FEATURES - COMPREHENSIVE TEST")
    print("=" * 80)
    
    # Run tests
    test_results = {}
    
    test_results['LaTeX Conversion'] = test_latex_to_linear_conversion()
    test_results['Math Detection'] = test_mathematical_detection()
    
    # Create test document
    try:
        output_file = create_test_document()
        test_results['Document Creation'] = True
        print(f"  ✅ Successfully created test document: {output_file}")
    except Exception as e:
        test_results['Document Creation'] = False
        print(f"  ❌ Document creation failed: {e}")
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 EQUATION FEATURE TEST RESULTS")
    print("=" * 80)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
    
    print(f"\n📊 Overall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL EQUATION FEATURES WORKING PERFECTLY!")
        print("\nYour mathematical tables will now have:")
        print("  ✅ Proper equation objects in Word")
        print("  ✅ LaTeX to Linear format conversion")
        print("  ✅ Automatic mathematical content detection")
        print("  ✅ Professional equation formatting")
        print("  ✅ Editable equations in Word")
        
        print(f"\n📁 Check the test document: {output_file}")
        print("   Open it in Word to see the equation objects!")
    else:
        print("\n⚠️  Some equation features need attention.")
        print("   Basic functionality should still work.")
    
    print("=" * 80)

if __name__ == "__main__":
    run_equation_tests()
