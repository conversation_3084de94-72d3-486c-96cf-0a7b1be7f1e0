/**
This file was auto-generated by mupdfwrap.py.
*/

#ifndef MUPDF_CLASSES2_H
#define MUPDF_CLASSES2_H


#include "classes.h"


namespace mupdf
{

	/** Class-aware wrapper for `::fz_aa_level()`.  */
	/**
		Get the number of bits of antialiasing we are
		using (for graphics). Between 0 and 8.
	*/
	FZ_FUNCTION int fz_aa_level();

	/** Class-aware wrapper for `::fz_abs()`.  */
	/**
		Some standard math functions, done as static inlines for speed.
		People with compilers that do not adequately implement inline
		may like to reimplement these using macros.
	*/
	FZ_FUNCTION float fz_abs(float f);

	/** Class-aware wrapper for `::fz_absi()`.  */
	FZ_FUNCTION int fz_absi(int i);

	/** Class-aware wrapper for `::fz_add_layout_char()`.  */
	/**
		Add a new char to the line at the end of the layout block.
	*/
	FZ_FUNCTION void fz_add_layout_char(const FzLayoutBlock& block, float x, float w, const char *p);

	/** Class-aware wrapper for `::fz_add_layout_line()`.  */
	/**
		Add a new line to the end of the layout block.
	*/
	FZ_FUNCTION void fz_add_layout_line(const FzLayoutBlock& block, float x, float y, float h, const char *p);

	/** Class-aware wrapper for `::fz_add_separation()`.  */
	/**
		Add a separation (null terminated name, colorspace)
	*/
	FZ_FUNCTION void fz_add_separation(const FzSeparations& sep, const char *name, const FzColorspace& cs, int cs_channel);

	/** Class-aware wrapper for `::fz_add_separation_equivalents()`.  */
	/**
		Add a separation with equivalents (null terminated name,
		colorspace)
	
		(old, deprecated)
	*/
	FZ_FUNCTION void fz_add_separation_equivalents(const FzSeparations& sep, uint32_t rgba, uint32_t cmyk, const char *name);

	/** Class-aware wrapper for `::fz_adjust_rect_for_stroke()`.  */
	/**
		Given a rectangle (assumed to be the bounding box for a path),
		expand it to allow for the expansion of the bbox that would be
		seen by stroking the path with the given stroke state and
		transform.
	*/
	FZ_FUNCTION FzRect fz_adjust_rect_for_stroke(const FzRect& rect, const FzStrokeState& stroke, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_advance_glyph()`.  */
	/**
		Return the advance for a given glyph.
	
		font: The font to look for the glyph in.
	
		glyph: The glyph to find the advance for.
	
		wmode: 1 for vertical mode, 0 for horizontal.
	
		Returns the advance for the glyph.
	*/
	FZ_FUNCTION float fz_advance_glyph(const FzFont& font, int glyph, int wmode);

	/** Class-aware wrapper for `::fz_aes_crypt_cbc()`.  */
	/**
		AES block processing. Encrypts or Decrypts (according to mode,
		which must match what was initially set up) length bytes (which
		must be a multiple of 16), using (and modifying) the insertion
		vector iv, reading from input, and writing to output.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_aes_crypt_cbc(const FzAes& ctx, int mode, size_t length, unsigned char iv[16], const unsigned char *input, unsigned char *output);

	/** Class-aware wrapper for `::fz_aes_setkey_dec()`.  */
	/**
		AES decryption initialization. Fills in the supplied context
		and prepares for decryption using the given key.
	
		Returns non-zero for error (key size other than 128/192/256).
	
		Never throws an exception.
	*/
	FZ_FUNCTION int fz_aes_setkey_dec(const FzAes& ctx, const unsigned char *key, int keysize);

	/** Class-aware wrapper for `::fz_aes_setkey_enc()`.  */
	/**
		AES encryption initialization. Fills in the supplied context
		and prepares for encryption using the given key.
	
		Returns non-zero for error (key size other than 128/192/256).
	
		Never throws an exception.
	*/
	FZ_FUNCTION int fz_aes_setkey_enc(const FzAes& ctx, const unsigned char *key, int keysize);

	/** Class-aware wrapper for `::fz_alpha_from_gray()`.  */
	FZ_FUNCTION FzPixmap fz_alpha_from_gray(const FzPixmap& gray);

	/** Class-aware wrapper for `::fz_append_base64()`.  */
	/**
		Write a base64 encoded data block, optionally with periodic newlines.
	*/
	FZ_FUNCTION void fz_append_base64(const FzBuffer& out, const unsigned char *data, size_t size, int newline);

	/** Class-aware wrapper for `::fz_append_base64_buffer()`.  */
	/**
		Append a base64 encoded fz_buffer, optionally with periodic newlines.
	*/
	FZ_FUNCTION void fz_append_base64_buffer(const FzBuffer& out, const FzBuffer& data, int newline);

	/** Class-aware wrapper for `::fz_append_bits()`.  */
	FZ_FUNCTION void fz_append_bits(const FzBuffer& buf, int value, int count);

	/** Class-aware wrapper for `::fz_append_bits_pad()`.  */
	FZ_FUNCTION void fz_append_bits_pad(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_append_buffer()`.  */
	/**
		Append the contents of the source buffer onto the end of the
		destination buffer, extending automatically as required.
	
		Ownership of buffers does not change.
	*/
	FZ_FUNCTION void fz_append_buffer(const FzBuffer& destination, const FzBuffer& source);

	/** Class-aware wrapper for `::fz_append_byte()`.  */
	FZ_FUNCTION void fz_append_byte(const FzBuffer& buf, int c);

	/** Class-aware wrapper for `::fz_append_data()`.  */
	/**
		fz_append_*: Append data to a buffer.
	
		The buffer will automatically grow as required.
	*/
	FZ_FUNCTION void fz_append_data(const FzBuffer& buf, const void *data, size_t len);

	/** Class-aware wrapper for `::fz_append_image_as_data_uri()`.  */
	FZ_FUNCTION void fz_append_image_as_data_uri(const FzBuffer& out, const FzImage& image);

	/** Class-aware wrapper for `::fz_append_int16_be()`.  */
	FZ_FUNCTION void fz_append_int16_be(const FzBuffer& buf, int x);

	/** Class-aware wrapper for `::fz_append_int16_le()`.  */
	FZ_FUNCTION void fz_append_int16_le(const FzBuffer& buf, int x);

	/** Class-aware wrapper for `::fz_append_int32_be()`.  */
	FZ_FUNCTION void fz_append_int32_be(const FzBuffer& buf, int x);

	/** Class-aware wrapper for `::fz_append_int32_le()`.  */
	FZ_FUNCTION void fz_append_int32_le(const FzBuffer& buf, int x);

	/** Class-aware wrapper for `::fz_append_json()`.  */
	FZ_FUNCTION void fz_append_json(const FzBuffer& buf, const FzJson& value);

	/** Class-aware wrapper for `::fz_append_pdf_string()`.  */
	/**
		fz_append_pdf_string: Append a string with PDF syntax quotes and
		escapes.
	
		The buffer will automatically grow as required.
	*/
	FZ_FUNCTION void fz_append_pdf_string(const FzBuffer& buffer, const char *text);

	/** Class-aware wrapper for `::fz_append_pixmap_as_data_uri()`.  */
	FZ_FUNCTION void fz_append_pixmap_as_data_uri(const FzBuffer& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_append_rune()`.  */
	FZ_FUNCTION void fz_append_rune(const FzBuffer& buf, int c);

	/** Class-aware wrapper for `::fz_append_string()`.  */
	FZ_FUNCTION void fz_append_string(const FzBuffer& buf, const char *data);

	/** Class-aware wrapper for `::fz_append_vprintf()`.  */
	/**
		fz_append_vprintf: Format and append data to buffer using
		printf-like formatting with varargs (see fz_vsnprintf).
	*/
	FZ_FUNCTION void fz_append_vprintf(const FzBuffer& buffer, const char *fmt, va_list args);

	/** Class-aware wrapper for `::fz_arc4_encrypt()`.  */
	/**
		RC4 block encrypt operation; encrypt src into dst (both of
		length len) updating the RC4 state as we go.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_arc4_encrypt(const FzArc4& state, unsigned char *dest, const unsigned char *src, size_t len);

	/** Class-aware wrapper for `::fz_arc4_final()`.  */
	/**
		RC4 finalization. Zero the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_arc4_final(const FzArc4& state);

	/** Class-aware wrapper for `::fz_arc4_init()`.  */
	/**
		RC4 initialization. Begins an RC4 operation, writing a new
		context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_arc4_init(const FzArc4& state, const unsigned char *key, size_t len);

	/** Class-aware wrapper for `::fz_archive_format()`.  */
	/**
		Return a pointer to a string describing the format of the
		archive.
	
		The lifetime of the string is unspecified (in current
		implementations the string will persist until the archive
		is closed, but this is not guaranteed).
	*/
	FZ_FUNCTION const char *fz_archive_format(const FzArchive& arch);

	/** Class-aware wrapper for `::fz_atof()`.  */
	/**
		Range checking atof
	*/
	FZ_FUNCTION float fz_atof(const char *s);

	/** Class-aware wrapper for `::fz_atoi()`.  */
	/**
		atoi that copes with NULL
	*/
	FZ_FUNCTION int fz_atoi(const char *s);

	/** Class-aware wrapper for `::fz_atoi64()`.  */
	/**
		64bit atoi that copes with NULL
	*/
	FZ_FUNCTION int64_t fz_atoi64(const char *s);

	/** Class-aware wrapper for `::fz_atoz()`.  */
	/**
		size_t atoi that copes with NULL.
	
		NOTE: limited to 63bits. Negative numbers
		are returned as 0.
	*/
	FZ_FUNCTION size_t fz_atoz(const char *s);

	/** Class-aware wrapper for `::fz_authenticate_password()`.  */
	/**
		Test if the given password can decrypt the document.
	
		password: The password string to be checked. Some document
		specifications do not specify any particular text encoding, so
		neither do we.
	
		Returns 0 for failure to authenticate, non-zero for success.
	
		For PDF documents, further information can be given by examining
		the bits in the return code.
	
			Bit 0 => No password required
			Bit 1 => User password authenticated
			Bit 2 => Owner password authenticated
	*/
	FZ_FUNCTION int fz_authenticate_password(const FzDocument& doc, const char *password);

	/** Class-aware wrapper for `::fz_autowarp_pixmap()`.  */
	FZ_FUNCTION FzPixmap fz_autowarp_pixmap(const FzPixmap& src, const FzQuad& points);

	/** Class-aware wrapper for `::fz_available()`.  */
	/**
		Ask how many bytes are available immediately from
		a given stream.
	
		stm: The stream to read from.
	
		max: A hint for the underlying stream; the maximum number of
		bytes that we are sure we will want to read. If you do not know
		this number, give 1.
	
		Returns the number of bytes immediately available between the
		read and write pointers. This number is guaranteed only to be 0
		if we have hit EOF. The number of bytes returned here need have
		no relation to max (could be larger, could be smaller).
	*/
	FZ_FUNCTION size_t fz_available(const FzStream& stm, size_t max);

	/** Class-aware wrapper for `::fz_barcode_type_from_string()`.  */
	/**
		Helper function to search the above list (case insensitively)
		for an exact match. Returns FZ_BARCODE_NONE if no match found.
	*/
	FZ_FUNCTION ::fz_barcode_type fz_barcode_type_from_string(const char *str);

	/** Class-aware wrapper for `::fz_base_colorspace()`.  */
	/**
		Get the 'base' colorspace for a colorspace.
	
		For indexed colorspaces, this is the colorspace the index
		decodes into. For all other colorspaces, it is the colorspace
		itself.
	
		The returned colorspace is 'borrowed' (i.e. no additional
		references are taken or dropped).
	*/
	FZ_FUNCTION FzColorspace fz_base_colorspace(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_basename()`.  */
	/**
		Find the filename component in a path.
	*/
	FZ_FUNCTION const char *fz_basename(const char *path);

	/** Class-aware wrapper for `::fz_begin_group()`.  */
	FZ_FUNCTION void fz_begin_group(const FzDevice& dev, const FzRect& area, const FzColorspace& cs, int isolated, int knockout, int blendmode, float alpha);

	/** Class-aware wrapper for `::fz_begin_layer()`.  */
	FZ_FUNCTION void fz_begin_layer(const FzDevice& dev, const char *layer_name);

	/** Class-aware wrapper for `::fz_begin_mask()`.  */
	FZ_FUNCTION void fz_begin_mask(const FzDevice& dev, const FzRect& area, int luminosity, const FzColorspace& colorspace, const float *bc, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_begin_metatext()`.  */
	FZ_FUNCTION void fz_begin_metatext(const FzDevice& dev, ::fz_metatext meta, const char *text);

	/** Class-aware wrapper for `::fz_begin_page()`.  */
	/**
		Called to start the process of writing a page to
		a document.
	
		mediabox: page size rectangle in points.
	
		Returns a borrowed fz_device to write page contents to. This
		should be kept if required, and only dropped if it was kept.
	*/
	FZ_FUNCTION FzDevice fz_begin_page(const FzDocumentWriter& wri, const FzRect& mediabox);

	/** Class-aware wrapper for `::fz_begin_structure()`.  */
	FZ_FUNCTION void fz_begin_structure(const FzDevice& dev, ::fz_structure standard, const char *raw, int idx);

	/** Class-aware wrapper for `::fz_begin_tile()`.  */
	FZ_FUNCTION void fz_begin_tile(const FzDevice& dev, const FzRect& area, const FzRect& view, float xstep, float ystep, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_begin_tile_id()`.  */
	FZ_FUNCTION int fz_begin_tile_id(const FzDevice& dev, const FzRect& area, const FzRect& view, float xstep, float ystep, const FzMatrix& ctm, int id);

	/** Class-aware wrapper for `::fz_bidi_fragment_text()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_bidi_fragment_text(const uint32_t *text, size_t textlen, ::fz_bidi_fragment_fn *callback, void *arg, int flags)` => ::fz_bidi_direction baseDir
	 */
	/**
		Partitions the given Unicode sequence into one or more
		unidirectional fragments and invokes the given callback
		function for each fragment.
	
		For example, if directionality of text is:
				0123456789
				rrlllrrrrr,
		we'll invoke callback with:
				&text[0], length == 2
				&text[2], length == 3
				&text[5], length == 5
	
		@param[in] text	start of Unicode sequence
		@param[in] textlen   number of Unicodes to analyse
		@param[in] baseDir   direction of paragraph (specify FZ_BIDI_NEUTRAL to force auto-detection)
		@param[in] callback  function to be called for each fragment
		@param[in] arg	data to be passed to the callback function
		@param[in] flags     flags to control operation (see fz_bidi_flags above)
	*/
	FZ_FUNCTION void fz_bidi_fragment_text(const uint32_t *text, size_t textlen, ::fz_bidi_direction *baseDir, ::fz_bidi_fragment_fn *callback, void *arg, int flags);

	/** Class-aware wrapper for `::fz_bitmap_details()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_bitmap_details(::fz_bitmap *bitmap)` => `(int w, int h, int n, int stride)`
	 */
	/**
		Retrieve details of a given bitmap.
	
		bitmap: The bitmap to query.
	
		w: Pointer to storage to retrieve width (or NULL).
	
		h: Pointer to storage to retrieve height (or NULL).
	
		n: Pointer to storage to retrieve number of color components (or
		NULL).
	
		stride: Pointer to storage to retrieve bitmap stride (or NULL).
	*/
	FZ_FUNCTION void fz_bitmap_details(const FzBitmap& bitmap, int *w, int *h, int *n, int *stride);

	/** Class-aware wrapper for `::fz_blendmode_name()`.  */
	/**
		Map from enumeration to blend mode string.
	
		The string is static, with arbitrary lifespan.
	*/
	FZ_FUNCTION const char *fz_blendmode_name(int blendmode);

	/** Class-aware wrapper for `::fz_bound_display_list()`.  */
	/**
		Return the bounding box of the page recorded in a display list.
	*/
	FZ_FUNCTION FzRect fz_bound_display_list(const FzDisplayList& list);

	/** Class-aware wrapper for `::fz_bound_glyph()`.  */
	/**
		Return a bbox for a given glyph in a font.
	
		font: The font to look for the glyph in.
	
		gid: The glyph to bound.
	
		trm: The matrix to apply to the glyph before bounding.
	
		Returns rectangle by value containing the bounds of the given
		glyph.
	*/
	FZ_FUNCTION FzRect fz_bound_glyph(const FzFont& font, int gid, const FzMatrix& trm);

	/** Class-aware wrapper for `::fz_bound_page()`.  */
	/**
		Determine the size of a page at 72 dpi.
	*/
	FZ_FUNCTION FzRect fz_bound_page(const FzPage& page);

	/** Class-aware wrapper for `::fz_bound_page_box()`.  */
	FZ_FUNCTION FzRect fz_bound_page_box(const FzPage& page, ::fz_box_type box);

	/** Class-aware wrapper for `::fz_bound_path()`.  */
	/**
		Return a bounding rectangle for a path.
	
		path: The path to bound.
	
		stroke: If NULL, the bounding rectangle given is for
		the filled path. If non-NULL the bounding rectangle
		given is for the path stroked with the given attributes.
	
		ctm: The matrix to apply to the path during stroking.
	
		r: Pointer to a fz_rect which will be used to hold
		the result.
	
		Returns r, updated to contain the bounding rectangle.
	*/
	FZ_FUNCTION FzRect fz_bound_path(const FzPath& path, const FzStrokeState& stroke, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_bound_shade()`.  */
	/**
		Bound a given shading.
	
		shade: The shade to bound.
	
		ctm: The transform to apply to the shade before bounding.
	
		r: Pointer to storage to put the bounds in.
	
		Returns r, updated to contain the bounds for the shading.
	*/
	FZ_FUNCTION FzRect fz_bound_shade(const FzShade& shade, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_bound_text()`.  */
	/**
		Find the bounds of a given text object.
	
		text: The text object to find the bounds of.
	
		stroke: Pointer to the stroke attributes (for stroked
		text), or NULL (for filled text).
	
		ctm: The matrix in use.
	
		r: pointer to storage for the bounds.
	
		Returns a pointer to r, which is updated to contain the
		bounding box for the text object.
	*/
	FZ_FUNCTION FzRect fz_bound_text(const FzText& text, const FzStrokeState& stroke, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_box_type_from_string()`.  */
	FZ_FUNCTION ::fz_box_type fz_box_type_from_string(const char *name);

	/** Class-aware wrapper for `::fz_brotli_bound()`.  */
	/**
		Returns the upper bound on the
		size of brotli compressed data of length size.
	 */
	FZ_FUNCTION size_t fz_brotli_bound(size_t size);

	/** Class-aware wrapper for `::fz_buffer_extract()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_buffer_extract(::fz_buffer *buf)` => `(size_t, unsigned char *data)`
	 */
	/**
		Take ownership of buffer contents.
	
		Performs the same task as fz_buffer_storage, but ownership of
		the data buffer returns with this call. The buffer is left
		empty.
	
		Note: Bad things may happen if this is called on a buffer with
		multiple references that is being used from multiple threads.
	
		data: Pointer to place to retrieve data pointer.
	
		Returns length of stream.
	*/
	FZ_FUNCTION size_t fz_buffer_extract(const FzBuffer& buf, unsigned char **data);

	/** Class-aware wrapper for `::fz_buffer_storage()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_buffer_storage(::fz_buffer *buf)` => `(size_t, unsigned char *datap)`
	 */
	/**
		Retrieve internal memory of buffer.
	
		datap: Output parameter that will be pointed to the data.
	
		Returns the current size of the data in bytes.
	*/
	FZ_FUNCTION size_t fz_buffer_storage(const FzBuffer& buf, unsigned char **datap);

	/** Class-aware wrapper for `::fz_calculate_font_ascender_descender()`.  */
	/**
		Ensure that a font has its ascender/descender values calculated
		from the actual bbox of the glyphs.
	
		Note, that we combine the declared values from the font (or the
		default values if those are not present) with the actual bbox to
		get the final result. So this can only cause ascender/descender
		to move further apart!
	*/
	FZ_FUNCTION void fz_calculate_font_ascender_descender(const FzFont& font);

	/** Class-aware wrapper for `::fz_calloc()`.  */
	/**
		Allocate array of memory of count entries of size bytes.
		Clears the memory to zero.
	
		Throws exception in the event of failure to allocate.
	*/
	FZ_FUNCTION void *fz_calloc(size_t count, size_t size);

	/** Class-aware wrapper for `::fz_calloc_no_throw()`.  */
	/**
		fz_calloc equivalent that returns NULL rather than throwing
		exceptions.
	*/
	FZ_FUNCTION void *fz_calloc_no_throw(size_t count, size_t size);

	/** Class-aware wrapper for `::fz_caught()`.  */
	/**
		Within an fz_catch() block, retrieve the error code for
		the current exception.
	
		This assumes no intervening use of fz_try/fz_catch.
	*/
	FZ_FUNCTION int fz_caught();

	/** Class-aware wrapper for `::fz_caught_errno()`.  */
	FZ_FUNCTION int fz_caught_errno();

	/** Class-aware wrapper for `::fz_caught_message()`.  */
	/**
		Within an fz_catch() block, retrieve the formatted message
		string for the current exception.
	
		This assumes no intervening use of fz_try/fz_catch.
	*/
	FZ_FUNCTION const char *fz_caught_message();

	/** Class-aware wrapper for `::fz_chartorune()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_chartorune(const char *str)` => `(int, int rune)`
	 */
	/**
		UTF8 decode a single rune from a sequence of chars.
	
		rune: Pointer to an int to assign the decoded 'rune' to.
		(0xFFFD on error).
	
		str: Pointer to a UTF8 encoded string.
	
		Returns the number of bytes consumed.
	*/
	FZ_FUNCTION int fz_chartorune(int *rune, const char *str);

	/** Class-aware wrapper for `::fz_chartorunen()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_chartorunen(const char *str, size_t n)` => `(int, int rune)`
	 */
	/**
		UTF8 decode a single rune from a sequence of chars
		of given length.
	
		rune: Pointer to an int to assign the decoded 'rune' to.
		(0xFFFD on error).
	
		str: Pointer to a UTF8 encoded string.
	
		n: The number of bytes available at str.
	
		Returns the number of bytes consumed.
	*/
	FZ_FUNCTION int fz_chartorunen(int *rune, const char *str, size_t n);

	/** Class-aware wrapper for `::fz_clamp()`.  */
	FZ_FUNCTION float fz_clamp(float x, float min, float max);

	/** Class-aware wrapper for `::fz_clamp64()`.  */
	FZ_FUNCTION int64_t fz_clamp64(int64_t x, int64_t min, int64_t max);

	/** Class-aware wrapper for `::fz_clamp_color()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_clamp_color(::fz_colorspace *cs, const float *in)` => float out
	 */
	/**
		Clamp the samples in a color to the correct ranges for a
		given colorspace.
	*/
	FZ_FUNCTION void fz_clamp_color(const FzColorspace& cs, const float *in, float *out);

	/** Class-aware wrapper for `::fz_clamp_location()`.  */
	/**
		Clamps a location into valid chapter/page range. (First clamps
		the chapter into range, then the page into range).
	*/
	FZ_FUNCTION FzLocation fz_clamp_location(const FzDocument& doc, const FzLocation& loc);

	/** Class-aware wrapper for `::fz_clampd()`.  */
	FZ_FUNCTION double fz_clampd(double x, double min, double max);

	/** Class-aware wrapper for `::fz_clampi()`.  */
	FZ_FUNCTION int fz_clampi(int x, int min, int max);

	/** Class-aware wrapper for `::fz_clampp()`.  */
	FZ_FUNCTION void *fz_clampp(void *x, void *min, void *max);

	/** Class-aware wrapper for `::fz_cleanname()`.  */
	/**
		rewrite path to the shortest string that names the same path.
	
		Eliminates multiple and trailing slashes, interprets "." and
		"..". Overwrites the string in place.
	*/
	FZ_FUNCTION char *fz_cleanname(char *name);

	/** Class-aware wrapper for `::fz_cleanname_strdup()`.  */
	/**
		rewrite path to the shortest string that names the same path.
	
		Eliminates multiple and trailing slashes, interprets "." and
		"..". Allocates a new string that the caller must free.
	*/
	FZ_FUNCTION char *fz_cleanname_strdup(const char *name);

	/** Class-aware wrapper for `::fz_clear_bitmap()`.  */
	/**
		Set the entire bitmap to 0.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION void fz_clear_bitmap(const FzBitmap& bit);

	/** Class-aware wrapper for `::fz_clear_buffer()`.  */
	/**
		Empties the buffer. Storage is not freed, but is held ready
		to be reused as the buffer is refilled.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION void fz_clear_buffer(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_clear_pixmap()`.  */
	/**
		Sets all components (including alpha) of
		all pixels in a pixmap to 0.
	
		pix: The pixmap to clear.
	*/
	FZ_FUNCTION void fz_clear_pixmap(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_clear_pixmap_rect_with_value()`.  */
	/**
		Clears a subrect of a pixmap with the given value.
	
		pix: The pixmap to clear.
	
		value: Values in the range 0 to 255 are valid. Each component
		sample for each pixel in the pixmap will be set to this value,
		while alpha will always be set to 255 (non-transparent).
	
		r: the rectangle.
	*/
	FZ_FUNCTION void fz_clear_pixmap_rect_with_value(const FzPixmap& pix, int value, const FzIrect& r);

	/** Class-aware wrapper for `::fz_clear_pixmap_with_value()`.  */
	/**
		Clears a pixmap with the given value.
	
		pix: The pixmap to clear.
	
		value: Values in the range 0 to 255 are valid. Each component
		sample for each pixel in the pixmap will be set to this value,
		while alpha will always be set to 255 (non-transparent).
	
		This function is horrible, and should be removed from the
		API and replaced with a less magic one.
	*/
	FZ_FUNCTION void fz_clear_pixmap_with_value(const FzPixmap& pix, int value);

	/** Class-aware wrapper for `::fz_clip_image_mask()`.  */
	FZ_FUNCTION void fz_clip_image_mask(const FzDevice& dev, const FzImage& image, const FzMatrix& ctm, const FzRect& scissor);

	/** Class-aware wrapper for `::fz_clip_path()`.  */
	FZ_FUNCTION void fz_clip_path(const FzDevice& dev, const FzPath& path, int even_odd, const FzMatrix& ctm, const FzRect& scissor);

	/** Class-aware wrapper for `::fz_clip_stroke_path()`.  */
	FZ_FUNCTION void fz_clip_stroke_path(const FzDevice& dev, const FzPath& path, const FzStrokeState& stroke, const FzMatrix& ctm, const FzRect& scissor);

	/** Class-aware wrapper for `::fz_clip_stroke_text()`.  */
	FZ_FUNCTION void fz_clip_stroke_text(const FzDevice& dev, const FzText& text, const FzStrokeState& stroke, const FzMatrix& ctm, const FzRect& scissor);

	/** Class-aware wrapper for `::fz_clip_text()`.  */
	FZ_FUNCTION void fz_clip_text(const FzDevice& dev, const FzText& text, const FzMatrix& ctm, const FzRect& scissor);

	/** Class-aware wrapper for `::fz_clone_buffer()`.  */
	/**
		Make a new buffer, containing a copy of the data used in
		the original.
	*/
	FZ_FUNCTION FzBuffer fz_clone_buffer(const FzBuffer& buf);

	/* Class-aware wrapper for `fz_clone_context()`
	is not available because returned wrapper class for `fz_context`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_clone_default_colorspaces()`.  */
	/**
		Returns a reference to a newly cloned default colorspaces
		structure.
	
		The new clone may safely be altered without fear of race
		conditions as the caller is the only reference holder.
	*/
	FZ_FUNCTION FzDefaultColorspaces fz_clone_default_colorspaces(const FzDefaultColorspaces& base);

	/** Class-aware wrapper for `::fz_clone_path()`.  */
	/**
		Clone the data for a path.
	
		This is used in preference to fz_keep_path when a whole
		new copy of a path is required, rather than just a shared
		pointer. This probably indicates that the path is about to
		be modified.
	
		path: path to clone.
	
		Throws exceptions on failure to allocate.
	*/
	FZ_FUNCTION FzPath fz_clone_path(const FzPath& path);

	/** Class-aware wrapper for `::fz_clone_pixmap()`.  */
	/**
		Clone a pixmap, copying the pixels and associated data to new
		storage.
	
		The reference count of 'old' is unchanged.
	*/
	FZ_FUNCTION FzPixmap fz_clone_pixmap(const FzPixmap& old);

	/** Class-aware wrapper for `::fz_clone_pixmap_area_with_different_seps()`.  */
	FZ_FUNCTION FzPixmap fz_clone_pixmap_area_with_different_seps(const FzPixmap& src, FzIrect& bbox, const FzColorspace& dcs, const FzSeparations& seps, const FzColorParams& color_params, const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_clone_separations_for_overprint()`.  */
	/**
		Return a separations object with all the spots in the input
		separations object that are set to composite, reset to be
		enabled. If there ARE no spots in the object, this returns
		NULL. If the object already has all its spots enabled, then
		just returns another handle on the same object.
	*/
	FZ_FUNCTION FzSeparations fz_clone_separations_for_overprint(const FzSeparations& seps);

	/** Class-aware wrapper for `::fz_clone_stroke_state()`.  */
	/**
		Create an identical stroke_state structure and return a
		reference to it.
	
		stroke: The stroke state reference to clone.
	
		Exceptions may be thrown in the event of a failure to
		allocate.
	*/
	FZ_FUNCTION FzStrokeState fz_clone_stroke_state(const FzStrokeState& stroke);

	/** Class-aware wrapper for `::fz_close_band_writer()`.  */
	/**
		Finishes up the output and closes the band writer. After this
		call no more headers or bands may be written.
	*/
	FZ_FUNCTION void fz_close_band_writer(const FzBandWriter& writer);

	/** Class-aware wrapper for `::fz_close_device()`.  */
	/**
		Signal the end of input, and flush any buffered output.
		This is NOT called implicitly on fz_drop_device. This
		may throw exceptions.
	*/
	FZ_FUNCTION void fz_close_device(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_close_document_writer()`.  */
	/**
		Called to end the process of writing
		pages to a document.
	
		This writes any file level trailers required. After this
		completes successfully the file is up to date and complete.
	*/
	FZ_FUNCTION void fz_close_document_writer(const FzDocumentWriter& wri);

	/** Class-aware wrapper for `::fz_close_output()`.  */
	/**
		Flush pending output and close an output stream.
	*/
	FZ_FUNCTION void fz_close_output(const FzOutput& arg_0);

	/** Class-aware wrapper for `::fz_close_zip_writer()`.  */
	/**
		Close the zip file for writing.
	
		This flushes any pending data to the file. This can throw
		exceptions.
	*/
	FZ_FUNCTION void fz_close_zip_writer(const FzZipWriter& zip);

	/** Class-aware wrapper for `::fz_closepath()`.  */
	/**
		Close the current subpath.
	
		path: The path to modify.
	
		Throws exceptions on failure to allocate, attempting to modify
		a packed path, and illegal path closes (i.e. closing a non open
		path).
	*/
	FZ_FUNCTION void fz_closepath(const FzPath& path);

	/** Class-aware wrapper for `::fz_colorspace_colorant()`.  */
	/**
		Retrieve a the name for a colorant.
	
		Returns a pointer with the same lifespan as the colorspace.
	*/
	FZ_FUNCTION const char *fz_colorspace_colorant(const FzColorspace& cs, int n);

	/** Class-aware wrapper for `::fz_colorspace_device_n_has_cmyk()`.  */
	/**
		True if DeviceN color space has cyan magenta yellow or black as
		one of its colorants.
	*/
	FZ_FUNCTION int fz_colorspace_device_n_has_cmyk(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_device_n_has_only_cmyk()`.  */
	/**
		True if DeviceN color space has only colorants from the CMYK set.
	*/
	FZ_FUNCTION int fz_colorspace_device_n_has_only_cmyk(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_digest()`.  */
	/**
		Get checksum of underlying ICC profile.
	*/
	FZ_FUNCTION void fz_colorspace_digest(const FzColorspace& cs, unsigned char digest[16]);

	/** Class-aware wrapper for `::fz_colorspace_is_cmyk()`.  */
	FZ_FUNCTION int fz_colorspace_is_cmyk(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_device()`.  */
	FZ_FUNCTION int fz_colorspace_is_device(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_device_cmyk()`.  */
	FZ_FUNCTION int fz_colorspace_is_device_cmyk(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_device_gray()`.  */
	FZ_FUNCTION int fz_colorspace_is_device_gray(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_device_n()`.  */
	FZ_FUNCTION int fz_colorspace_is_device_n(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_gray()`.  */
	/**
		Tests for particular types of colorspaces
	*/
	FZ_FUNCTION int fz_colorspace_is_gray(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_icc()`.  */
	FZ_FUNCTION int fz_colorspace_is_icc(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_indexed()`.  */
	FZ_FUNCTION int fz_colorspace_is_indexed(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_lab()`.  */
	FZ_FUNCTION int fz_colorspace_is_lab(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_lab_icc()`.  */
	FZ_FUNCTION int fz_colorspace_is_lab_icc(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_rgb()`.  */
	FZ_FUNCTION int fz_colorspace_is_rgb(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_is_subtractive()`.  */
	/**
		True for CMYK, Separation and DeviceN colorspaces.
	*/
	FZ_FUNCTION int fz_colorspace_is_subtractive(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_n()`.  */
	/**
		Query the number of colorants in a colorspace.
	*/
	FZ_FUNCTION int fz_colorspace_n(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_name()`.  */
	/**
		Query the name of a colorspace.
	
		The returned string has the same lifespan as the colorspace
		does. Caller should not free it.
	*/
	FZ_FUNCTION const char *fz_colorspace_name(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_colorspace_name_colorant()`.  */
	/**
		Assign a name for a given colorant in a colorspace.
	
		Used while initially setting up a colorspace. The string is
		copied into local storage, so need not be retained by the
		caller.
	*/
	FZ_FUNCTION void fz_colorspace_name_colorant(const FzColorspace& cs, int n, const char *name);

	/** Class-aware wrapper for `::fz_colorspace_type()`.  */
	/**
		Query the type of colorspace.
	*/
	FZ_FUNCTION enum fz_colorspace_type fz_colorspace_type(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_compare_separations()`.  */
	/**
		Compare 2 separations structures (or NULLs).
	
		Return 0 if identical, non-zero if not identical.
	*/
	FZ_FUNCTION int fz_compare_separations(const FzSeparations& sep1, const FzSeparations& sep2);

	/** Class-aware wrapper for `::fz_compress_brotli()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_compress_brotli(unsigned char *dest, const unsigned char *source, size_t source_length, ::fz_brotli_level level)` => size_t compressed_length
	 */
	/**
		Compress source_length bytes of data starting
		at source, into a buffer of length *destLen, starting at dest.
		*compressed_length will be updated on exit to contain the size
		actually used.
	 */
	FZ_FUNCTION void fz_compress_brotli(unsigned char *dest, size_t *compressed_length, const unsigned char *source, size_t source_length, ::fz_brotli_level level);

	/** Class-aware wrapper for `::fz_compress_ccitt_fax_g3()`.  */
	/**
		Compress bitmap data as CCITT Group 3 1D fax image.
		Creates a stream assuming the default PDF parameters,
		except the number of columns.
	*/
	FZ_FUNCTION FzBuffer fz_compress_ccitt_fax_g3(const unsigned char *data, int columns, int rows, ptrdiff_t stride);

	/** Class-aware wrapper for `::fz_compress_ccitt_fax_g4()`.  */
	/**
		Compress bitmap data as CCITT Group 4 2D fax image.
		Creates a stream assuming the default PDF parameters, except
		K=-1 and the number of columns.
	*/
	FZ_FUNCTION FzBuffer fz_compress_ccitt_fax_g4(const unsigned char *data, int columns, int rows, ptrdiff_t stride);

	/** Class-aware wrapper for `::fz_compressed_buffer_size()`.  */
	/**
		Return the storage size used for a buffer and its data.
		Used in implementing store handling.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION size_t fz_compressed_buffer_size(const FzCompressedBuffer& buffer);

	/** Class-aware wrapper for `::fz_compressed_image_buffer()`.  */
	/**
		Retrieve the underlying compressed data for an image.
	
		Returns a pointer to the underlying data buffer for an image,
		or NULL if this image is not based upon a compressed data
		buffer.
	
		This is not a reference counted structure, so no reference is
		returned. Lifespan is limited to that of the image itself.
	*/
	FZ_FUNCTION FzCompressedBuffer fz_compressed_image_buffer(const FzImage& image);

	/** Class-aware wrapper for `::fz_compressed_image_type()`.  */
	/**
		Return the type of a compressed image.
	
		Any non-compressed image will have the type returned as UNKNOWN.
	*/
	FZ_FUNCTION int fz_compressed_image_type(const FzImage& image);

	/** Class-aware wrapper for `::fz_concat()`.  */
	/**
		Multiply two matrices.
	
		The order of the two matrices are important since matrix
		multiplication is not commutative.
	
		Returns result.
	*/
	FZ_FUNCTION FzMatrix fz_concat(const FzMatrix& left, const FzMatrix& right);

	/** Class-aware wrapper for `::fz_contains_rect()`.  */
	/**
		Test rectangle inclusion.
	
		Return true if a entirely contains b.
	*/
	FZ_FUNCTION int fz_contains_rect(const FzRect& a, const FzRect& b);

	/** Class-aware wrapper for `::fz_convert_color()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_convert_color(::fz_colorspace *ss, const float *sv, ::fz_colorspace *ds, ::fz_colorspace *is, ::fz_color_params params)` => float dv
	 */
	/**
		Convert color values sv from colorspace ss into colorvalues dv
		for colorspace ds, via an optional intervening space is,
		respecting the given color_params.
	*/
	FZ_FUNCTION void fz_convert_color(const FzColorspace& ss, const float *sv, const FzColorspace& ds, float *dv, const FzColorspace& is, const FzColorParams& params);

	/** Class-aware wrapper for `::fz_convert_error()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_convert_error()` => `(const char *, int code)`
	 */
	FZ_FUNCTION const char *fz_convert_error(int *code);

	/** Class-aware wrapper for `::fz_convert_indexed_pixmap_to_base()`.  */
	/**
		Convert pixmap from indexed to base colorspace.
	
		This creates a new bitmap containing the converted pixmap data.
	 */
	FZ_FUNCTION FzPixmap fz_convert_indexed_pixmap_to_base(const FzPixmap& src);

	/** Class-aware wrapper for `::fz_convert_pixmap()`.  */
	/**
		Convert an existing pixmap to a desired
		colorspace. Other properties of the pixmap, such as resolution
		and position are copied to the converted pixmap.
	
		pix: The pixmap to convert.
	
		default_cs: If NULL pix->colorspace is used. It is possible that
		the data may need to be interpreted as one of the color spaces
		in default_cs.
	
		cs_des: Desired colorspace, may be NULL to denote alpha-only.
	
		prf: Proofing color space through which we need to convert.
	
		color_params: Parameters that may be used in conversion (e.g.
		ri).
	
		keep_alpha: If 0 any alpha component is removed, otherwise
		alpha is kept if present in the pixmap.
	*/
	FZ_FUNCTION FzPixmap fz_convert_pixmap(const FzPixmap& pix, const FzColorspace& cs_des, const FzColorspace& prf, const FzDefaultColorspaces& default_cs, const FzColorParams& color_params, int keep_alpha);

	/** Class-aware wrapper for `::fz_convert_separation_colors()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_convert_separation_colors(::fz_colorspace *src_cs, const float *src_color, ::fz_separations *dst_seps, ::fz_colorspace *dst_cs, ::fz_color_params color_params)` => float dst_color
	 */
	/**
		Convert a color given in terms of one colorspace,
		to a color in terms of another colorspace/separations.
	*/
	FZ_FUNCTION void fz_convert_separation_colors(const FzColorspace& src_cs, const float *src_color, const FzSeparations& dst_seps, const FzColorspace& dst_cs, float *dst_color, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_convert_separation_pixmap_to_base()`.  */
	/**
		Convert pixmap from DeviceN/Separation to base colorspace.
	
		This creates a new bitmap containing the converted pixmap data.
	*/
	FZ_FUNCTION FzPixmap fz_convert_separation_pixmap_to_base(const FzPixmap& src);

	/** Class-aware wrapper for `::fz_copy_option()`.  */
	/**
		Copy an option (val) into a destination buffer (dest), of maxlen
		bytes.
	
		Returns the number of bytes (including terminator) that did not
		fit. If val is maxlen or greater bytes in size, it will be left
		unterminated.
	*/
	FZ_FUNCTION size_t fz_copy_option(const char *val, char *dest, size_t maxlen);

	/** Class-aware wrapper for `::fz_copy_pixmap_rect()`.  */
	FZ_FUNCTION void fz_copy_pixmap_rect(const FzPixmap& dest, const FzPixmap& src, const FzIrect& r, const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_copy_rectangle()`.  */
	/**
		Return a newly allocated UTF-8 string with the text for a given
		selection rectangle.
	
		crlf: If true, write "\r\n" style line endings (otherwise "\n"
		only).
	*/
	FZ_FUNCTION char *fz_copy_rectangle(const FzStextPage& page, const FzRect& area, int crlf);

	/** Class-aware wrapper for `::fz_copy_selection()`.  */
	/**
		Return a newly allocated UTF-8 string with the text for a given
		selection.
	
		crlf: If true, write "\r\n" style line endings (otherwise "\n"
		only).
	*/
	FZ_FUNCTION char *fz_copy_selection(const FzStextPage& page, const FzPoint& a, const FzPoint& b, int crlf);

	/** Class-aware wrapper for `::fz_count_active_separations()`.  */
	/**
		Return the number of active separations.
	*/
	FZ_FUNCTION int fz_count_active_separations(const FzSeparations& seps);

	/** Class-aware wrapper for `::fz_count_archive_entries()`.  */
	/**
		Number of entries in archive.
	
		Will always return a value >= 0.
	
		May throw an exception if this type of archive cannot count the
		entries (such as a directory).
	*/
	FZ_FUNCTION int fz_count_archive_entries(const FzArchive& arch);

	/** Class-aware wrapper for `::fz_count_chapter_pages()`.  */
	/**
		Return the number of pages in a chapter.
		May return 0.
	*/
	FZ_FUNCTION int fz_count_chapter_pages(const FzDocument& doc, int chapter);

	/** Class-aware wrapper for `::fz_count_chapters()`.  */
	/**
		Return the number of chapters in the document.
		At least 1.
	*/
	FZ_FUNCTION int fz_count_chapters(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_count_pages()`.  */
	/**
		Return the number of pages in document
	
		May return 0 for documents with no pages.
	*/
	FZ_FUNCTION int fz_count_pages(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_count_separations()`.  */
	FZ_FUNCTION int fz_count_separations(const FzSeparations& sep);

	/** Class-aware wrapper for `::fz_create_link()`.  */
	/**
		Create a new link on a page.
	*/
	FZ_FUNCTION FzLink fz_create_link(const FzPage& page, const FzRect& bbox, const char *uri);

	/** Class-aware wrapper for `::fz_currentpoint()`.  */
	/**
		Return the current point that a path has
		reached or (0,0) if empty.
	
		path: path to return the current point of.
	*/
	FZ_FUNCTION FzPoint fz_currentpoint(const FzPath& path);

	/** Class-aware wrapper for `::fz_curveto()`.  */
	/**
		Append a 'curveto' command to an open path. (For a
		cubic bezier).
	
		path: The path to modify.
	
		x0, y0: The coordinates of the first control point for the
		curve.
	
		x1, y1: The coordinates of the second control point for the
		curve.
	
		x2, y2: The end coordinates for the curve.
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_curveto(const FzPath& path, float x0, float y0, float x1, float y1, float x2, float y2);

	/** Class-aware wrapper for `::fz_curvetov()`.  */
	/**
		Append a 'curvetov' command to an open path. (For a
		cubic bezier with the first control coordinate equal to
		the start point).
	
		path: The path to modify.
	
		x1, y1: The coordinates of the second control point for the
		curve.
	
		x2, y2: The end coordinates for the curve.
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_curvetov(const FzPath& path, float x1, float y1, float x2, float y2);

	/** Class-aware wrapper for `::fz_curvetoy()`.  */
	/**
		Append a 'curvetoy' command to an open path. (For a
		cubic bezier with the second control coordinate equal to
		the end point).
	
		path: The path to modify.
	
		x0, y0: The coordinates of the first control point for the
		curve.
	
		x2, y2: The end coordinates for the curve (and the second
		control coordinate).
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_curvetoy(const FzPath& path, float x0, float y0, float x2, float y2);

	/** Class-aware wrapper for `::fz_debug_store()`.  */
	/**
		Output debugging information for the current state of the store
		to the given output channel.
	*/
	FZ_FUNCTION void fz_debug_store(const FzOutput& out);

	/** Class-aware wrapper for `::fz_debug_xml()`.  */
	/**
		Pretty-print an XML tree to stdout. (Deprecated, use
		fz_output_xml in preference).
	*/
	FZ_FUNCTION void fz_debug_xml(const FzXml& item, int level);

	/** Class-aware wrapper for `::fz_decode_barcode_from_display_list()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decode_barcode_from_display_list(::fz_display_list *list, ::fz_rect subarea, int rotate)` => `(char *, ::fz_barcode_type type)`
	 */
	/**
		Decode a barcode from a display list.
	
		type: NULL, or a pointer to recieve the barcode type decoded.
		list: The display list to render to get the barcode.
		subarea: subarea of the page to decode.
		rotate: 0, 90, 180, or 270.
	
		returns the decoded value.
	*/
	FZ_FUNCTION char *fz_decode_barcode_from_display_list(::fz_barcode_type *type, const FzDisplayList& list, const FzRect& subarea, int rotate);

	/** Class-aware wrapper for `::fz_decode_barcode_from_display_list2()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decode_barcode_from_display_list2(::fz_display_list *list, ::fz_rect subarea, int rotate)` => `(std::string, ::fz_barcode_type type)`
	 */
	/** SWIG-friendly wrapper for fz_decode_barcode_from_display_list(),
	avoiding leak of the returned string. */
	FZ_FUNCTION std::string fz_decode_barcode_from_display_list2(::fz_barcode_type *type, const FzDisplayList& list, const FzRect& subarea, int rotate);

	/** Class-aware wrapper for `::fz_decode_barcode_from_page()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decode_barcode_from_page(::fz_page *page, ::fz_rect subarea, int rotate)` => `(char *, ::fz_barcode_type type)`
	 */
	/**
		Decode a barcode from a page.
	
		type: NULL, or a pointer to recieve the barcode type decoded.
		page: The page to decode.
		subarea: subarea of the page to decode.
		rotate: 0, 90, 180, or 270.
	
		returns the decoded value.
	*/
	FZ_FUNCTION char *fz_decode_barcode_from_page(::fz_barcode_type *type, const FzPage& page, const FzRect& subarea, int rotate);

	/** Class-aware wrapper for `::fz_decode_barcode_from_page2()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decode_barcode_from_page2(::fz_page *page, ::fz_rect subarea, int rotate)` => `(std::string, ::fz_barcode_type type)`
	 */
	/** SWIG-friendly wrapper for fz_decode_barcode_from_page(), avoiding
	leak of the returned string. */
	FZ_FUNCTION std::string fz_decode_barcode_from_page2(::fz_barcode_type *type, const FzPage& page, const FzRect& subarea, int rotate);

	/** Class-aware wrapper for `::fz_decode_barcode_from_pixmap()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decode_barcode_from_pixmap(::fz_pixmap *pix, int rotate)` => `(char *, ::fz_barcode_type type)`
	 */
	/**
		Decode a barcode from a pixmap.
	
		type: NULL, or a pointer to recieve the barcode type decoded.
		pix: The pixmap to decode.
		rotate: 0, 90, 180, or 270.
	
		returns the decoded value as an fz_malloced block. Should
		be fz_free'd by the caller.
	*/
	FZ_FUNCTION char *fz_decode_barcode_from_pixmap(::fz_barcode_type *type, const FzPixmap& pix, int rotate);

	/** Class-aware wrapper for `::fz_decode_barcode_from_pixmap2()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decode_barcode_from_pixmap2(::fz_pixmap *pix, int rotate)` => `(std::string, ::fz_barcode_type type)`
	 */
	/** SWIG-friendly wrapper for fz_decode_barcode_from_pixmap(), avoiding
	leak of the returned string. */
	FZ_FUNCTION std::string fz_decode_barcode_from_pixmap2(::fz_barcode_type *type, const FzPixmap& pix, int rotate);

	/** Class-aware wrapper for `::fz_decode_tile()`.  */
	FZ_FUNCTION void fz_decode_tile(const FzPixmap& pix, const float *decode);

	/** Class-aware wrapper for `::fz_decode_uri()`.  */
	/**
	 * Return a new string representing the unencoded version of the given URI.
	 * This decodes all escape sequences except those that would result in a reserved
	 * character that are part of the URI syntax (; / ? : @ & = + $ , #).
	 */
	FZ_FUNCTION char *fz_decode_uri(const char *s);

	/** Class-aware wrapper for `::fz_decode_uri_component()`.  */
	/**
	 * Return a new string representing the unencoded version of the given URI component.
	 * This decodes all escape sequences!
	 */
	FZ_FUNCTION char *fz_decode_uri_component(const char *s);

	/** Class-aware wrapper for `::fz_decomp_image_from_stream()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_decomp_image_from_stream(::fz_stream *stm, ::fz_compressed_image *image, ::fz_irect *subarea, int indexed, int l2factor)` => `(fz_pixmap *, int l2extra)`
	 */
	/**
		Decode a subarea of a compressed image. l2factor is the amount
		of subsampling inbuilt to the stream (i.e. performed by the
		decoder). If non NULL, l2extra is the extra amount of
		subsampling that should be performed by this routine. This will
		be updated on exit to the amount of subsampling that is still
		required to be done.
	
		Returns a kept reference.
	*/
	FZ_FUNCTION FzPixmap fz_decomp_image_from_stream(const FzStream& stm, const FzCompressedImage& image, FzIrect& subarea, int indexed, int l2factor, int *l2extra);

	/** Class-aware wrapper for `::fz_decouple_type3_font()`.  */
	FZ_FUNCTION void fz_decouple_type3_font(const FzFont& font, void *t3doc);

	/** Class-aware wrapper for `::fz_default_cmyk()`.  */
	FZ_FUNCTION FzColorspace fz_default_cmyk(const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_default_error_callback()`.  */
	/**
		FIXME: Better not to expose fz_default_error_callback, and
		fz_default_warning callback and to allow 'NULL' to be used
		int fz_set_xxxx_callback to mean "defaults".
	
		FIXME: Do we need/want functions like
		fz_error_callback(ctx, message) to allow callers to inject
		stuff into the error/warning streams?
	*/
	/**
		The default error callback. Declared publicly just so that the
		error callback can be set back to this after it has been
		overridden.
	*/
	FZ_FUNCTION void fz_default_error_callback(void *user, const char *message);

	/** Class-aware wrapper for `::fz_default_gray()`.  */
	/**
		Retrieve default colorspaces (typically page local).
	
		If default_cs is non NULL, the default is retrieved from there,
		otherwise the global default is retrieved.
	
		These return borrowed references that should not be dropped,
		unless they are kept first.
	*/
	FZ_FUNCTION FzColorspace fz_default_gray(const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_default_halftone()`.  */
	/**
		Create a 'default' halftone structure
		for the given number of components.
	
		num_comps: The number of components to use.
	
		Returns a simple default halftone. The default halftone uses
		the same halftone tile for each plane, which may not be ideal
		for all purposes.
	*/
	FZ_FUNCTION FzHalftone fz_default_halftone(int num_comps);

	/** Class-aware wrapper for `::fz_default_output_intent()`.  */
	FZ_FUNCTION FzColorspace fz_default_output_intent(const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_default_rgb()`.  */
	FZ_FUNCTION FzColorspace fz_default_rgb(const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_default_warning_callback()`.  */
	/**
		The default warning callback. Declared publicly just so that
		the warning callback can be set back to this after it has been
		overridden.
	*/
	FZ_FUNCTION void fz_default_warning_callback(void *user, const char *message);

	/** Class-aware wrapper for `::fz_defer_reap_end()`.  */
	/**
		Decrement the defer reap count.
	
		If the defer reap count returns to 0, and the store
		has reapable objects in, a reap pass will begin.
	
		Call this at the end of a process during which you
		potentially might drop many reapable objects.
	
		It is vital that every fz_defer_reap_start is matched
		by a fz_defer_reap_end call.
	*/
	FZ_FUNCTION void fz_defer_reap_end();

	/** Class-aware wrapper for `::fz_defer_reap_start()`.  */
	/**
		Increment the defer reap count.
	
		No reap operations will take place (except for those
		triggered by an immediate failed malloc) until the
		defer reap count returns to 0.
	
		Call this at the start of a process during which you
		potentially might drop many reapable objects.
	
		It is vital that every fz_defer_reap_start is matched
		by a fz_defer_reap_end call.
	*/
	FZ_FUNCTION void fz_defer_reap_start();

	/** Class-aware wrapper for `::fz_deflate()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_deflate(unsigned char *dest, const unsigned char *source, size_t source_length, ::fz_deflate_level level)` => size_t compressed_length
	 */
	/**
		Compress source_length bytes of data starting
		at source, into a buffer of length *compressed_length, starting at
		dest. *compressed_length will be updated on exit to contain the size
		actually used.
	 */
	FZ_FUNCTION void fz_deflate(unsigned char *dest, size_t *compressed_length, const unsigned char *source, size_t source_length, ::fz_deflate_level level);

	/** Class-aware wrapper for `::fz_deflate_bound()`.  */
	/**
		Returns the upper bound on the
		size of flated data of length size.
	 */
	FZ_FUNCTION size_t fz_deflate_bound(size_t size);

	/** Class-aware wrapper for `::fz_delete_link()`.  */
	/**
		Delete an existing link on a page.
	*/
	FZ_FUNCTION void fz_delete_link(const FzPage& page, const FzLink& link);

	/** Class-aware wrapper for `::fz_deskew_pixmap()`.  */
	FZ_FUNCTION FzPixmap fz_deskew_pixmap(const FzPixmap& src, double degrees, int border);

	/** Class-aware wrapper for `::fz_detach_xml()`.  */
	/**
		Detach a node from the tree, unlinking it from its parent,
		and setting the document root to the node.
	*/
	FZ_FUNCTION void fz_detach_xml(const FzXml& node);

	/** Class-aware wrapper for `::fz_detect_document()`.  */
	FZ_FUNCTION int fz_detect_document(FzQuad& points, const FzPixmap& src);

	/** Class-aware wrapper for `::fz_detect_skew()`.  */
	FZ_FUNCTION double fz_detect_skew(const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_device_bgr()`.  */
	FZ_FUNCTION FzColorspace fz_device_bgr();

	/** Class-aware wrapper for `::fz_device_cmyk()`.  */
	FZ_FUNCTION FzColorspace fz_device_cmyk();

	/** Class-aware wrapper for `::fz_device_current_scissor()`.  */
	/**
		Find current scissor region as tracked by the device.
	*/
	FZ_FUNCTION FzRect fz_device_current_scissor(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_device_gray()`.  */
	/**
		Retrieve global default colorspaces.
	
		These return borrowed references that should not be dropped,
		unless they are kept first.
	*/
	FZ_FUNCTION FzColorspace fz_device_gray();

	/** Class-aware wrapper for `::fz_device_lab()`.  */
	FZ_FUNCTION FzColorspace fz_device_lab();

	/** Class-aware wrapper for `::fz_device_rgb()`.  */
	FZ_FUNCTION FzColorspace fz_device_rgb();

	/** Class-aware wrapper for `::fz_dirname()`.  */
	/**
		extract the directory component from a path.
	*/
	FZ_FUNCTION void fz_dirname(char *dir, const char *path, size_t dirsize);

	/** Class-aware wrapper for `::fz_disable_device_hints()`.  */
	/**
		Disable (clear) hint bits within the hint bitfield for a device.
	*/
	FZ_FUNCTION void fz_disable_device_hints(const FzDevice& dev, int hints);

	/** Class-aware wrapper for `::fz_disable_icc()`.  */
	/**
		Disable icc profile based operation.
	*/
	FZ_FUNCTION void fz_disable_icc();

	/** Class-aware wrapper for `::fz_display_list_is_empty()`.  */
	/**
		Check for a display list being empty
	
		list: The list to check.
	
		Returns true if empty, false otherwise.
	*/
	FZ_FUNCTION int fz_display_list_is_empty(const FzDisplayList& list);

	/** Class-aware wrapper for `::fz_div255()`.  */
	/**
		Undo alpha premultiplication.
	*/
	FZ_FUNCTION int fz_div255(int c, int a);

	/** Class-aware wrapper for `::fz_do_always()`.  */
	FZ_FUNCTION int fz_do_always();

	/** Class-aware wrapper for `::fz_do_catch()`.  */
	FZ_FUNCTION int fz_do_catch();

	/** Class-aware wrapper for `::fz_do_try()`.  */
	FZ_FUNCTION int fz_do_try();

	/** Class-aware wrapper for `::fz_document_handler_open()`.  */
	/** Helper for calling `fz_document_handler::open` function pointer via
	Swig from Python/C#. */
	FZ_FUNCTION FzDocument fz_document_handler_open(const FzDocumentHandler& handler, const FzStream& stream, const FzStream& accel, const FzArchive& dir, void *recognize_state);

	/** Class-aware wrapper for `::fz_document_handler_recognize()`.  */
	/** Helper for calling a `fz_document_handler::recognize` function
	pointer via Swig from Python/C#. */
	FZ_FUNCTION int fz_document_handler_recognize(const FzDocumentHandler& handler, const char *magic);

	/** Class-aware wrapper for `::fz_document_output_intent()`.  */
	/**
		Find the output intent colorspace if the document has defined
		one.
	
		Returns a borrowed reference that should not be dropped, unless
		it is kept first.
	*/
	FZ_FUNCTION FzColorspace fz_document_output_intent(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_document_supports_accelerator()`.  */
	/**
		Query if the document supports the saving of accelerator data.
	*/
	FZ_FUNCTION int fz_document_supports_accelerator(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_dom_add_attribute()`.  */
	/**
		Add an attribute to an element.
	
		Ownership of att and value remain with the caller.
	*/
	FZ_FUNCTION void fz_dom_add_attribute(const FzXml& elt, const char *att, const char *value);

	/** Class-aware wrapper for `::fz_dom_append_child()`.  */
	/**
		Insert an element as the last child of a parent, unlinking the
		child from its current position if required.
	*/
	FZ_FUNCTION void fz_dom_append_child(const FzXml& parent, const FzXml& child);

	/** Class-aware wrapper for `::fz_dom_attribute()`.  */
	/**
		Retrieve the value of a given attribute from a given element.
	
		Returns a borrowed pointer to the value or NULL if not found.
	*/
	FZ_FUNCTION const char *fz_dom_attribute(const FzXml& elt, const char *att);

	/** Class-aware wrapper for `::fz_dom_body()`.  */
	/**
		Return a borrowed reference for the 'body' element of
		the given DOM.
	*/
	FZ_FUNCTION FzXml fz_dom_body(const FzXml& dom);

	/** Class-aware wrapper for `::fz_dom_clone()`.  */
	/**
		Clone an element (and its children).
	
		A borrowed reference to the clone is returned. The clone is not
		yet linked into the DOM.
	*/
	FZ_FUNCTION FzXml fz_dom_clone(const FzXml& elt);

	/** Class-aware wrapper for `::fz_dom_create_element()`.  */
	/**
		Create an element of a given tag type for the given DOM.
	
		The element is not linked into the DOM yet.
	*/
	FZ_FUNCTION FzXml fz_dom_create_element(const FzXml& dom, const char *tag);

	/** Class-aware wrapper for `::fz_dom_create_text_node()`.  */
	/**
		Create a text node for the given DOM.
	
		The element is not linked into the DOM yet.
	*/
	FZ_FUNCTION FzXml fz_dom_create_text_node(const FzXml& dom, const char *text);

	/** Class-aware wrapper for `::fz_dom_document_element()`.  */
	/**
		Return a borrowed reference for the document (the top
		level element) of the DOM.
	*/
	FZ_FUNCTION FzXml fz_dom_document_element(const FzXml& dom);

	/** Class-aware wrapper for `::fz_dom_find()`.  */
	/**
		Find the first element matching the requirements in a depth first traversal from elt.
	
		The tagname must match tag, unless tag is NULL, when all tag names are considered to match.
	
		If att is NULL, then all tags match.
		Otherwise:
			If match is NULL, then only nodes that have an att attribute match.
			If match is non-NULL, then only nodes that have an att attribute that matches match match.
	
		Returns NULL (if no match found), or a borrowed reference to the first matching element.
	*/
	FZ_FUNCTION FzXml fz_dom_find(const FzXml& elt, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_dom_find_next()`.  */
	/**
		Find the next element matching the requirements.
	*/
	FZ_FUNCTION FzXml fz_dom_find_next(const FzXml& elt, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_dom_first_child()`.  */
	/**
		Return a borrowed reference to the first child of a node,
		or NULL if there isn't one.
	*/
	FZ_FUNCTION FzXml fz_dom_first_child(const FzXml& elt);

	/** Class-aware wrapper for `::fz_dom_get_attribute()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_dom_get_attribute(::fz_xml *elt, int i)` => `(const char *, const char *att)`
	 */
	/**
		Enumerate through the attributes of an element.
	
		Call with i=0,1,2,3... to enumerate attributes.
	
		On return *att and the return value will be NULL if there are not
		that many attributes to read. Otherwise, *att will be filled in
		with a borrowed pointer to the attribute name, and the return
		value will be a borrowed pointer to the value.
	*/
	FZ_FUNCTION const char *fz_dom_get_attribute(const FzXml& elt, int i, const char **att);

	/** Class-aware wrapper for `::fz_dom_insert_after()`.  */
	/**
		Insert an element (new_elt), after another element (node),
		unlinking the new_elt from its current position if required.
	*/
	FZ_FUNCTION void fz_dom_insert_after(const FzXml& node, const FzXml& new_elt);

	/** Class-aware wrapper for `::fz_dom_insert_before()`.  */
	/**
		Insert an element (new_elt), before another element (node),
		unlinking the new_elt from its current position if required.
	*/
	FZ_FUNCTION void fz_dom_insert_before(const FzXml& node, const FzXml& new_elt);

	/** Class-aware wrapper for `::fz_dom_next()`.  */
	/**
		Return a borrowed reference to the next sibling of a node,
		or NULL if there isn't one.
	*/
	FZ_FUNCTION FzXml fz_dom_next(const FzXml& elt);

	/** Class-aware wrapper for `::fz_dom_parent()`.  */
	/**
		Return a borrowed reference to the parent of a node,
		or NULL if there isn't one.
	*/
	FZ_FUNCTION FzXml fz_dom_parent(const FzXml& elt);

	/** Class-aware wrapper for `::fz_dom_previous()`.  */
	/**
		Return a borrowed reference to the previous sibling of a node,
		or NULL if there isn't one.
	*/
	FZ_FUNCTION FzXml fz_dom_previous(const FzXml& elt);

	/** Class-aware wrapper for `::fz_dom_remove()`.  */
	/**
		Remove an element from the DOM. The element can be added back elsewhere
		if required.
	
		No reference counting changes for the element.
	*/
	FZ_FUNCTION void fz_dom_remove(const FzXml& elt);

	/** Class-aware wrapper for `::fz_dom_remove_attribute()`.  */
	/**
		Remove an attribute from an element.
	*/
	FZ_FUNCTION void fz_dom_remove_attribute(const FzXml& elt, const char *att);

	/** Class-aware wrapper for `::fz_draw_story()`.  */
	FZ_FUNCTION void fz_draw_story(const FzStory& story, const FzDevice& dev, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_dump_glyph_cache_stats()`.  */
	/**
		Dump debug statistics for the glyph cache.
	*/
	FZ_FUNCTION void fz_dump_glyph_cache_stats(const FzOutput& out);

	/** Class-aware wrapper for `::fz_duplicate_glyph_names_from_unicode()`.  */
	FZ_FUNCTION const char **fz_duplicate_glyph_names_from_unicode(int unicode);

	/** Class-aware wrapper for `::fz_empty_store()`.  */
	/**
		Evict every item from the store.
	*/
	FZ_FUNCTION void fz_empty_store();

	/** Class-aware wrapper for `::fz_enable_device_hints()`.  */
	/**
		Enable (set) hint bits within the hint bitfield for a device.
	*/
	FZ_FUNCTION void fz_enable_device_hints(const FzDevice& dev, int hints);

	/** Class-aware wrapper for `::fz_enable_icc()`.  */
	/**
		Enable icc profile based operation.
	*/
	FZ_FUNCTION void fz_enable_icc();

	/** Class-aware wrapper for `::fz_encode_character()`.  */
	/**
		Find the glyph id for a given unicode
		character within a font.
	
		font: The font to look for the unicode character in.
	
		unicode: The unicode character to encode.
	
		Returns the glyph id for the given unicode value, or 0 if
		unknown.
	*/
	FZ_FUNCTION int fz_encode_character(const FzFont& font, int unicode);

	/** Class-aware wrapper for `::fz_encode_character_by_glyph_name()`.  */
	/**
		Encode character.
	
		Either by direct lookup of glyphname within a font, or, failing
		that, by mapping glyphname to unicode and thence to the glyph
		index within the given font.
	
		Returns zero for type3 fonts.
	*/
	FZ_FUNCTION int fz_encode_character_by_glyph_name(const FzFont& font, const char *glyphname);

	/** Class-aware wrapper for `::fz_encode_character_sc()`.  */
	/**
		Encode character, preferring small-caps variant if available.
	
		font: The font to look for the unicode character in.
	
		unicode: The unicode character to encode.
	
		Returns the glyph id for the given unicode value, or 0 if
		unknown.
	*/
	FZ_FUNCTION int fz_encode_character_sc(const FzFont& font, int unicode);

	/** Class-aware wrapper for `::fz_encode_character_with_fallback()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_encode_character_with_fallback(::fz_font *font, int unicode, int script, int language, ::fz_font **out_font)` => `(int)`
	 */
	/**
		Find the glyph id for
		a given unicode character within a font, falling back to
		an alternative if not found.
	
		font: The font to look for the unicode character in.
	
		unicode: The unicode character to encode.
	
		script: The script in use.
	
		language: The language in use.
	
		out_font: The font handle in which the given glyph represents
		the requested unicode character. The caller does not own the
		reference it is passed, so should call fz_keep_font if it is
		not simply to be used immediately.
	
		Returns the glyph id for the given unicode value in the supplied
		font (and sets *out_font to font) if it is present. Otherwise
		an alternative fallback font (based on script/language) is
		searched for. If the glyph is found therein, *out_font is set
		to this reference, and the glyph reference is returned. If it
		cannot be found anywhere, the function returns 0.
	*/
	FZ_FUNCTION int fz_encode_character_with_fallback(const FzFont& font, int unicode, int script, int language, FzFont& out_font);

	/** Class-aware wrapper for `::fz_encode_uri()`.  */
	/**
	 * Return a new string representing the provided string encoded as a URI.
	 */
	FZ_FUNCTION char *fz_encode_uri(const char *s);

	/** Class-aware wrapper for `::fz_encode_uri_component()`.  */
	/**
	 * Return a new string representing the provided string encoded as an URI component.
	 * This also encodes the special reserved characters (; / ? : @ & = + $ , #).
	 */
	FZ_FUNCTION char *fz_encode_uri_component(const char *s);

	/** Class-aware wrapper for `::fz_encode_uri_pathname()`.  */
	/**
	 * Return a new string representing the provided string encoded as an URI path name.
	 * This also encodes the special reserved characters except /.
	 */
	FZ_FUNCTION char *fz_encode_uri_pathname(const char *s);

	/** Class-aware wrapper for `::fz_end_group()`.  */
	FZ_FUNCTION void fz_end_group(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_end_layer()`.  */
	FZ_FUNCTION void fz_end_layer(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_end_mask()`.  */
	FZ_FUNCTION void fz_end_mask(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_end_mask_tr()`.  */
	FZ_FUNCTION void fz_end_mask_tr(const FzDevice& dev, const FzFunction& fn);

	/** Class-aware wrapper for `::fz_end_metatext()`.  */
	FZ_FUNCTION void fz_end_metatext(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_end_page()`.  */
	/**
		Called to end the process of writing a page to a
		document.
	*/
	FZ_FUNCTION void fz_end_page(const FzDocumentWriter& wri);

	/** Class-aware wrapper for `::fz_end_structure()`.  */
	FZ_FUNCTION void fz_end_structure(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_end_throw_on_repair()`.  */
	FZ_FUNCTION void fz_end_throw_on_repair();

	/** Class-aware wrapper for `::fz_end_tile()`.  */
	FZ_FUNCTION void fz_end_tile(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_enumerate_font_cmap()`.  */
	/**
		Enumerate a cmap using a callback.
	*/
	FZ_FUNCTION void fz_enumerate_font_cmap(const FzFont& font, ::fz_cmap_callback *cb, void *opaque);

	/** Class-aware wrapper for `::fz_enumerate_font_cmap2()`.  */
	/** SWIG-friendly wrapper for fz_enumerate_font_cmap(). */
	FZ_FUNCTION std::vector<fz_font_ucs_gid> fz_enumerate_font_cmap2(const FzFont& font);

	/** Class-aware wrapper for `::fz_error_callback()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_error_callback()` => `(fz_error_cb *, void *user)`
	 */
	/**
		Retrieve the currently set error callback, or NULL if none
		has been set. Optionally, if user is non-NULL, the user pointer
		given when the warning callback was set is also passed back to
		the caller.
	*/
	FZ_FUNCTION ::fz_error_cb *fz_error_callback(void **user);

	/** Class-aware wrapper for `::fz_eval_function()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_eval_function(::fz_function *func, const float *in, int inlen, int outlen)` => float out
	 */
	FZ_FUNCTION void fz_eval_function(const FzFunction& func, const float *in, int inlen, float *out, int outlen);

	/** Class-aware wrapper for `::fz_expand_irect()`.  */
	FZ_FUNCTION FzIrect fz_expand_irect(const FzIrect& a, int expand);

	/** Class-aware wrapper for `::fz_expand_rect()`.  */
	/**
		Expand a bbox by a given amount in all directions.
	*/
	FZ_FUNCTION FzRect fz_expand_rect(const FzRect& b, float expand);

	/** Class-aware wrapper for `::fz_extract_ttf_from_ttc()`.  */
	FZ_FUNCTION FzBuffer fz_extract_ttf_from_ttc(const FzFont& font);

	/** Class-aware wrapper for `::fz_file_exists()`.  */
	/**
		Return true if the named file exists and is readable.
	*/
	FZ_FUNCTION int fz_file_exists(const char *path);

	/** Class-aware wrapper for `::fz_fill_image()`.  */
	FZ_FUNCTION void fz_fill_image(const FzDevice& dev, const FzImage& image, const FzMatrix& ctm, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_fill_image_mask()`.  */
	FZ_FUNCTION void fz_fill_image_mask(const FzDevice& dev, const FzImage& image, const FzMatrix& ctm, const FzColorspace& colorspace, const float *color, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_fill_path()`.  */
	/**
		Device calls; graphics primitives and containers.
	*/
	FZ_FUNCTION void fz_fill_path(const FzDevice& dev, const FzPath& path, int even_odd, const FzMatrix& ctm, const FzColorspace& colorspace, const float *color, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_fill_pixmap_from_display_list()`.  */
	FZ_FUNCTION FzPixmap fz_fill_pixmap_from_display_list(const FzDisplayList& list, const FzMatrix& ctm, const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_fill_pixmap_with_color()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_fill_pixmap_with_color(::fz_pixmap *pix, ::fz_colorspace *colorspace, ::fz_color_params color_params)` => float color
	 */
	/**
		Fill pixmap with solid color.
	*/
	FZ_FUNCTION void fz_fill_pixmap_with_color(const FzPixmap& pix, const FzColorspace& colorspace, float *color, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_fill_shade()`.  */
	FZ_FUNCTION void fz_fill_shade(const FzDevice& dev, const FzShade& shade, const FzMatrix& ctm, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_fill_text()`.  */
	FZ_FUNCTION void fz_fill_text(const FzDevice& dev, const FzText& text, const FzMatrix& ctm, const FzColorspace& colorspace, const float *color, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_filter_store()`.  */
	/**
		Filter every element in the store with a matching type with the
		given function.
	
		If the function returns 1 for an element, drop the element.
	*/
	FZ_FUNCTION void fz_filter_store(::fz_store_filter_fn *fn, void *arg, const FzStoreType& type);

	/** Class-aware wrapper for `::fz_find_item()`.  */
	/**
		Find an item within the store.
	
		drop: The function used to free the value (to ensure we get a
		value of the correct type).
	
		key: The key used to index the item.
	
		type: Functions used to manipulate the key.
	
		Returns NULL for not found, otherwise returns a pointer to the
		value indexed by key to which a reference has been taken.
	*/
	FZ_FUNCTION void *fz_find_item(::fz_store_drop_fn *drop, void *key, const FzStoreType& type);

	/** Class-aware wrapper for `::fz_find_table_within_bounds()`.  */
	/**
		Interpret the bounded contents of a given stext page as
		a table.
	
		The page contents will be rewritten to contain a Table
		structure with the identified content in it.
	
		This uses the same logic as for fz_table_hunt, without the
		actual hunting. fz_table_hunt hunts to find possible bounds
		for multiple tables on the page; this routine just finds a
		single table contained within the given rectangle.
	
		Returns the stext_block list that contains the content of
		the table.
	*/
	FZ_FUNCTION FzStextBlock fz_find_table_within_bounds(const FzStextPage& page, const FzRect& bounds);

	/** Class-aware wrapper for `::fz_flush_output()`.  */
	/**
		Flush unwritten data.
	*/
	FZ_FUNCTION void fz_flush_output(const FzOutput& out);

	/** Class-aware wrapper for `::fz_flush_warnings()`.  */
	/**
		Flush any repeated warnings.
	
		Repeated warnings are buffered, counted and eventually printed
		along with the number of repetitions. Call fz_flush_warnings
		to force printing of the latest buffered warning and the
		number of repetitions, for example to make sure that all
		warnings are printed before exiting an application.
	*/
	FZ_FUNCTION void fz_flush_warnings();

	/** Class-aware wrapper for `::fz_font_ascender()`.  */
	/**
		Retrieve font ascender in ems.
	*/
	FZ_FUNCTION float fz_font_ascender(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_bbox()`.  */
	/**
		Retrieve the font bbox.
	
		font: The font to query.
	
		Returns the font bbox by value; it is valid only if
		fz_font_flags(font)->invalid_bbox is zero.
	*/
	FZ_FUNCTION FzRect fz_font_bbox(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_descender()`.  */
	/**
		Retrieve font descender in ems.
	*/
	FZ_FUNCTION float fz_font_descender(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_digest()`.  */
	/**
		Retrieve the MD5 digest for the font's data.
	*/
	FZ_FUNCTION void fz_font_digest(const FzFont& font, unsigned char digest[16]);

	/* Class-aware wrapper for `fz_font_flags()`
	is not available because returned wrapper class for `fz_font_flags_t`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_font_ft_face()`.  */
	/**
		Retrieve the FT_Face handle
		for the font.
	
		font: The font to query
	
		Returns the FT_Face handle for the font, or NULL
		if not a freetype handled font. (Cast to void *
		to avoid nasty header exposure).
	*/
	FZ_FUNCTION void *fz_font_ft_face(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_is_bold()`.  */
	/**
		Query whether the font flags say that this font is bold.
	*/
	FZ_FUNCTION int fz_font_is_bold(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_is_italic()`.  */
	/**
		Query whether the font flags say that this font is italic.
	*/
	FZ_FUNCTION int fz_font_is_italic(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_is_monospaced()`.  */
	/**
		Query whether the font flags say that this font is monospaced.
	*/
	FZ_FUNCTION int fz_font_is_monospaced(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_is_serif()`.  */
	/**
		Query whether the font flags say that this font is serif.
	*/
	FZ_FUNCTION int fz_font_is_serif(const FzFont& font);

	/** Class-aware wrapper for `::fz_font_name()`.  */
	/**
		Retrieve a pointer to the name of the font.
	
		font: The font to query.
	
		Returns a pointer to an internal copy of the font name.
		Will never be NULL, but may be the empty string.
	*/
	FZ_FUNCTION const char *fz_font_name(const FzFont& font);

	/* Class-aware wrapper for `fz_font_shaper_data()`
	is not available because returned wrapper class for `fz_shaper_data_t`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_font_t3_procs()`.  */
	/**
		Retrieve the Type3 procs
		for a font.
	
		font: The font to query
	
		Returns the t3_procs pointer. Will be NULL for a
		non type-3 font.
	*/
	FZ_FUNCTION ::fz_buffer **fz_font_t3_procs(const FzFont& font);

	/** Class-aware wrapper for `::fz_format_double()`.  */
	/** Swig-friendly and typesafe way to do fz_snprintf(fmt, value). `fmt`
	must end with one of 'efg' otherwise we throw an exception. */
	FZ_FUNCTION std::string fz_format_double(const char *fmt, double value);

	/** Class-aware wrapper for `::fz_format_link_uri()`.  */
	/**
		Format an internal link to a page number, location, and possible viewing parameters,
		suitable for use with fz_create_link.
	
		Returns a newly allocated string that the caller must free.
	*/
	FZ_FUNCTION char *fz_format_link_uri(const FzDocument& doc, const FzLinkDest& dest);

	/** Class-aware wrapper for `::fz_format_output_path()`.  */
	/**
		create output file name using a template.
	
		If the path contains %[0-9]*d, the first such pattern will be
		replaced with the page number. If the template does not contain
		such a pattern, the page number will be inserted before the
		filename extension. If the template does not have a filename
		extension, the page number will be added to the end.
	*/
	FZ_FUNCTION void fz_format_output_path(char *path, size_t size, const char *fmt, int page);

	/** Class-aware wrapper for `::fz_format_string()`.  */
	/**
		Our customised 'printf'-like string formatter.
		Takes %c, %d, %s, %u, %x, %X as usual.
		The only modifiers supported are:
		1) zero-padding ints (e.g. %02d, %03u, %04x, etc).
		2) ' to indicate that ' should be inserted into
			integers as thousands separators.
		3) , to indicate that , should be inserted into
			integers as thousands separators.
		4) _ to indicate that , should be inserted into
			integers as thousands separators.
		Posix chooses the thousand separator in a locale
		specific way - we do not. We always apply it every
		3 characters for the positive part of integers, so
		other styles, such as Indian (123,456,78) are not
		supported.
	
		%g output in "as short as possible hopefully lossless
		non-exponent" form, see fz_ftoa for specifics.
		%f and %e output as usual.
		%C outputs a utf8 encoded int.
		%M outputs a fz_matrix*.
		%R outputs a fz_rect*.
		%P outputs a fz_point*.
		%n outputs a PDF name (with appropriate escaping).
		%q and %( output escaped strings in C/PDF syntax.
		%l{d,u,x,X} indicates that the values are int64_t.
		%z{d,u,x,X} indicates that the value is a size_t.
		%< outputs a quoted (utf8) string (for XML).
		%> outputs a hex string for a zero terminated string of bytes.
	
		user: An opaque pointer that is passed to the emit function.
	
		emit: A function pointer called to emit output bytes as the
		string is being formatted.
	*/
	FZ_FUNCTION void fz_format_string(void *user, void (*emit)(::fz_context *, void *, int ), const char *fmt, va_list args);

	/** Class-aware wrapper for `::fz_free()`.  */
	/**
		Free a previously allocated block of memory.
	
		fz_free(ctx, NULL) does nothing.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION void fz_free(void *p);

	/** Class-aware wrapper for `::fz_free_aligned()`.  */
	/**
		fz_free equivalent, for blocks allocated via fz_malloc_aligned.
	*/
	FZ_FUNCTION void fz_free_aligned(void *p);

	/** Class-aware wrapper for `::fz_ft_lock()`.  */
	FZ_FUNCTION void fz_ft_lock();

	/** Class-aware wrapper for `::fz_ft_lock_held()`.  */
	FZ_FUNCTION int fz_ft_lock_held();

	/** Class-aware wrapper for `::fz_ft_unlock()`.  */
	FZ_FUNCTION void fz_ft_unlock();

	/** Class-aware wrapper for `::fz_function_size()`.  */
	FZ_FUNCTION size_t fz_function_size(const FzFunction& func);

	/** Class-aware wrapper for `::fz_gamma_pixmap()`.  */
	/**
		Apply gamma correction to a pixmap. All components
		of all pixels are modified (except alpha, which is unchanged).
	
		gamma: The gamma value to apply; 1.0 for no change.
	*/
	FZ_FUNCTION void fz_gamma_pixmap(const FzPixmap& pix, float gamma);

	/** Class-aware wrapper for `::fz_generate_transition()`.  */
	/**
		Generate a frame of a transition.
	
		tpix: Target pixmap
		opix: Old pixmap
		npix: New pixmap
		time: Position within the transition (0 to 256)
		trans: Transition details
	
		Returns 1 if successfully generated a frame.
	
		Note: Pixmaps must include alpha.
	*/
	FZ_FUNCTION int fz_generate_transition(const FzPixmap& tpix, const FzPixmap& opix, const FzPixmap& npix, int time, FzTransition& trans);

	/** Class-aware wrapper for `::fz_get_glyph_name()`.  */
	/**
		Find the name of a glyph
	
		font: The font to look for the glyph in.
	
		glyph: The glyph id to look for.
	
		buf: Pointer to a buffer for the name to be inserted into.
	
		size: The size of the buffer.
	
		If a font contains a name table, then the name of the glyph
		will be returned in the supplied buffer. Otherwise a name
		is synthesised. The name will be truncated to fit in
		the buffer.
	*/
	FZ_FUNCTION void fz_get_glyph_name(const FzFont& font, int glyph, char *buf, int size);

	/** Class-aware wrapper for `::fz_get_glyph_name2()`.  */
	/**
	C++ alternative to fz_get_glyph_name() that returns information in a std::string.
	*/
	FZ_FUNCTION std::string fz_get_glyph_name2(const FzFont& font, int glyph);

	/** Class-aware wrapper for `::fz_get_pixmap_from_image()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_get_pixmap_from_image(::fz_image *image, const ::fz_irect *subarea, ::fz_matrix *ctm)` => `(fz_pixmap *, int w, int h)`
	 */
	/**
		Called to get a handle to a pixmap from an image.
	
		image: The image to retrieve a pixmap from.
	
		subarea: The subarea of the image that we actually care about
		(or NULL to indicate the whole image).
	
		ctm: Optional, unless subarea is given. If given, then on
		entry this is the transform that will be applied to the complete
		image. It should be updated on exit to the transform to apply to
		the given subarea of the image. This is used to calculate the
		desired width/height for subsampling.
	
		w: If non-NULL, a pointer to an int to be updated on exit to the
		width (in pixels) that the scaled output will cover.
	
		h: If non-NULL, a pointer to an int to be updated on exit to the
		height (in pixels) that the scaled output will cover.
	
		Returns a non NULL kept pixmap pointer. May throw exceptions.
	*/
	FZ_FUNCTION FzPixmap fz_get_pixmap_from_image(const FzImage& image, FzIrect& subarea, FzMatrix& ctm, int *w, int *h);

	/** Class-aware wrapper for `::fz_get_unscaled_pixmap_from_image()`.  */
	/**
		Calls fz_get_pixmap_from_image() with ctm, subarea, w and h all set to NULL.
	*/
	FZ_FUNCTION FzPixmap fz_get_unscaled_pixmap_from_image(const FzImage& image);

	/** Class-aware wrapper for `::fz_getopt()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_getopt(int nargc, const char *ostr)` => `(int, char *nargv)`
	 */
	/**
		Identical to fz_getopt_long, but with a NULL longopts field, signifying no long
		options.
	*/
	FZ_FUNCTION int fz_getopt(int nargc, char **nargv, const char *ostr);

	/** Class-aware wrapper for `::fz_getopt_long()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_getopt_long(int nargc, const char *ostr, const ::fz_getopt_long_options *longopts)` => `(int, char *nargv)`
	 */
	/**
		Simple functions/variables for use in tools.
	
		ostr = option string. Comprises single letter options, followed by : if there
		is an argument to the option.
	
		longopts: NULL (indicating no long options), or a pointer to an array of
		longoptions, terminated by an entry with option == NULL.
	
		In the event of matching a single char option, this function will normally
		return the char. The exception to this is when the option requires an
		argument and none is supplied; in this case we return ':'.
	
		In the event of matching a long option, this function returns 0, with fz_optlong
		set to point to the matching option.
	
		A long option entry may be followed with : to indicate there is an argument to the
		option. If the need for an argument is specified in this way, and no argument is
		given, an error will be displayed and argument processing will stop. If an argument
		is given, and the long option record contains a non-null flag pointer, then the code
		will decode the argument and fill in that flag pointer. Specifically,
		case-insensitive matches to 'yes', 'no', 'true' and 'false' will cause a value of 0
		or 1 as appropriate to be written; failing this the arg will be interpreted as a
		decimal integer using atoi.
	
		A long option entry may be followed by an list of options (e.g. myoption=foo|bar|baz)
		and the option will be passed to fz_opt_from_list. The return value of that will be
		placed in fz_optitem. If the return value of that function is -1, then an error will
		be displayed and argument processing will stop.
	
		In the event of reaching the end of the arg list or '--', this function returns EOF.
	
		In the event of failing to match anything, an error is printed, and we return '?'.
	
		If an argument is expected for the option, then fz_optarg will be returned pointing
		at the start of the argument. Examples of supported argument formats: '-r500', '-r 500',
		'--resolution 500', '--resolution=500'.
	*/
	FZ_FUNCTION int fz_getopt_long(int nargc, char **nargv, const char *ostr, const FzGetoptLongOptions& longopts);

	/** Class-aware wrapper for `::fz_glyph_bbox()`.  */
	/**
		Return the bounding box of the glyph in pixels.
	*/
	FZ_FUNCTION FzIrect fz_glyph_bbox(const FzGlyph& glyph);

	/** Class-aware wrapper for `::fz_glyph_bbox_no_ctx()`.  */
	FZ_FUNCTION FzIrect fz_glyph_bbox_no_ctx(const FzGlyph& src);

	/** Class-aware wrapper for `::fz_glyph_cacheable()`.  */
	/**
		Determine if a given glyph in a font
		is cacheable. Certain glyphs in a type 3 font cannot safely
		be cached, as their appearance depends on the enclosing
		graphic state.
	
		font: The font to look for the glyph in.
	
		gif: The glyph to query.
	
		Returns non-zero if cacheable, 0 if not.
	*/
	FZ_FUNCTION int fz_glyph_cacheable(const FzFont& font, int gid);

	/** Class-aware wrapper for `::fz_glyph_height()`.  */
	/**
		Return the height of the glyph in pixels.
	*/
	FZ_FUNCTION int fz_glyph_height(const FzGlyph& glyph);

	/** Class-aware wrapper for `::fz_glyph_name_from_unicode_sc()`.  */
	FZ_FUNCTION const char *fz_glyph_name_from_unicode_sc(int unicode);

	/** Class-aware wrapper for `::fz_glyph_width()`.  */
	/**
		Return the width of the glyph in pixels.
	*/
	FZ_FUNCTION int fz_glyph_width(const FzGlyph& glyph);

	/** Class-aware wrapper for `::fz_graphics_aa_level()`.  */
	/**
		Get the number of bits of antialiasing we are
		using for graphics. Between 0 and 8.
	*/
	FZ_FUNCTION int fz_graphics_aa_level();

	/** Class-aware wrapper for `::fz_graphics_min_line_width()`.  */
	/**
		Get the minimum line width to be
		used for stroked lines.
	
		min_line_width: The minimum line width to use (in pixels).
	*/
	FZ_FUNCTION float fz_graphics_min_line_width();

	/** Class-aware wrapper for `::fz_gridfit_matrix()`.  */
	/**
		Grid fit a matrix.
	
		as_tiled = 0 => adjust the matrix so that the image of the unit
		square completely covers any pixel that was touched by the
		image of the unit square under the original matrix.
	
		as_tiled = 1 => adjust the matrix so that the corners of the
		image of the unit square align with the closest integer corner
		of the image of the unit square under the original matrix.
	*/
	FZ_FUNCTION FzMatrix fz_gridfit_matrix(int as_tiled, const FzMatrix& m);

	/** Class-aware wrapper for `::fz_grisu()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_grisu(float f, char *s)` => `(int, int exp)`
	 */
	FZ_FUNCTION int fz_grisu(float f, char *s, int *exp);

	/** Class-aware wrapper for `::fz_grow_buffer()`.  */
	/**
		Make some space within a buffer (i.e. ensure that
		capacity > size).
	*/
	FZ_FUNCTION void fz_grow_buffer(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_has_archive_entry()`.  */
	/**
		Check if entry by given name exists.
	
		If named entry does not exist 0 will be returned, if it does
		exist 1 is returned.
	
		name: Entry name to look for, this must be an exact match to
		the entry name in the archive.
	*/
	FZ_FUNCTION int fz_has_archive_entry(const FzArchive& arch, const char *name);

	/** Class-aware wrapper for `::fz_has_option()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_has_option(const char *opts, const char *key)` => `(int, const char *val)`
	 */
	/**
		Look for a given option (key) in the opts string. Return 1 if
		it has it, and update *val to point to the value within opts.
	*/
	FZ_FUNCTION int fz_has_option(const char *opts, const char *key, const char **val);

	/** Class-aware wrapper for `::fz_has_permission()`.  */
	/**
		Check permission flags on document.
	*/
	FZ_FUNCTION int fz_has_permission(const FzDocument& doc, ::fz_permission p);

	/** Class-aware wrapper for `::fz_hash_filter()`.  */
	/**
		Iterate over the entries in a hash table, removing all the ones where callback returns true.
		Does NOT free the value of the entry, so the caller is expected to take care of this.
	*/
	FZ_FUNCTION void fz_hash_filter(const FzHashTable& table, void *state, ::fz_hash_table_filter_fn *callback);

	/** Class-aware wrapper for `::fz_hash_find()`.  */
	/**
		Search for a matching hash within the table, and return the
		associated value.
	*/
	FZ_FUNCTION void *fz_hash_find(const FzHashTable& table, const void *key);

	/** Class-aware wrapper for `::fz_hash_for_each()`.  */
	/**
		Iterate over the entries in a hash table.
	*/
	FZ_FUNCTION void fz_hash_for_each(const FzHashTable& table, void *state, ::fz_hash_table_for_each_fn *callback);

	/** Class-aware wrapper for `::fz_hash_insert()`.  */
	/**
		Insert a new key/value pair into the hash table.
	
		If an existing entry with the same key is found, no change is
		made to the hash table, and a pointer to the existing value is
		returned.
	
		If no existing entry with the same key is found, ownership of
		val passes in, key is copied, and NULL is returned.
	*/
	FZ_FUNCTION void *fz_hash_insert(const FzHashTable& table, const void *key, void *val);

	/** Class-aware wrapper for `::fz_hash_remove()`.  */
	/**
		Remove the entry for a given key.
	
		The value is NOT freed, so the caller is expected to take care
		of this.
	*/
	FZ_FUNCTION void fz_hash_remove(const FzHashTable& table, const void *key);

	/** Class-aware wrapper for `::fz_hb_lock()`.  */
	/**
		Lock against Harfbuzz being called
		simultaneously in several threads. This reuses
		FZ_LOCK_FREETYPE.
	*/
	FZ_FUNCTION void fz_hb_lock();

	/** Class-aware wrapper for `::fz_hb_unlock()`.  */
	/**
		Unlock after a Harfbuzz call. This reuses
		FZ_LOCK_FREETYPE.
	*/
	FZ_FUNCTION void fz_hb_unlock();

	/** Class-aware wrapper for `::fz_highlight_selection()`.  */
	/**
		Return a list of quads to highlight lines inside the selection
		points.
	*/
	FZ_FUNCTION int fz_highlight_selection(const FzStextPage& page, const FzPoint& a, const FzPoint& b, FzQuad& quads, int max_quads);

	/** Class-aware wrapper for `::fz_highlight_selection2()`.  */
	/**
	C++ alternative to fz_highlight_selection() that returns quads in a
	std::vector.
	*/
	FZ_FUNCTION std::vector<fz_quad> fz_highlight_selection2(const FzStextPage& page, const FzPoint& a, const FzPoint& b, int max_quads);

	/** Class-aware wrapper for `::fz_ignore_error()`.  */
	FZ_FUNCTION void fz_ignore_error();

	/** Class-aware wrapper for `::fz_ignore_text()`.  */
	FZ_FUNCTION void fz_ignore_text(const FzDevice& dev, const FzText& text, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_image_orientation()`.  */
	/**
		Request the natural orientation of an image.
	
		This is for images (such as JPEG) that can contain internal
		specifications of rotation/flips. This is ignored by all the
		internal decode/rendering routines, but can be used by callers
		(such as the image document handler) to respect such
		specifications.
	
		The values used by MuPDF are as follows, with the equivalent
		Exif specifications given for information:
	
		0: Undefined
		1: 0 degree ccw rotation. (Exif = 1)
		2: 90 degree ccw rotation. (Exif = 8)
		3: 180 degree ccw rotation. (Exif = 3)
		4: 270 degree ccw rotation. (Exif = 6)
		5: flip on X. (Exif = 2)
		6: flip on X, then rotate ccw by 90 degrees. (Exif = 5)
		7: flip on X, then rotate ccw by 180 degrees. (Exif = 4)
		8: flip on X, then rotate ccw by 270 degrees. (Exif = 7)
	*/
	FZ_FUNCTION uint8_t fz_image_orientation(const FzImage& image);

	/** Class-aware wrapper for `::fz_image_orientation_matrix()`.  */
	FZ_FUNCTION FzMatrix fz_image_orientation_matrix(const FzImage& image);

	/** Class-aware wrapper for `::fz_image_resolution()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_image_resolution(::fz_image *image)` => `(int xres, int yres)`
	 */
	/**
		Request the natural resolution
		of an image.
	
		xres, yres: Pointers to ints to be updated with the
		natural resolution of an image (or a sensible default
		if not encoded).
	*/
	FZ_FUNCTION void fz_image_resolution(const FzImage& image, int *xres, int *yres);

	/** Class-aware wrapper for `::fz_image_size()`.  */
	/**
		Return the size of the storage used by an image.
	*/
	FZ_FUNCTION size_t fz_image_size(const FzImage& im);

	/** Class-aware wrapper for `::fz_image_type_name()`.  */
	/**
		Map from FZ_IMAGE_* value to string.
	
		The returned string is static and therefore must not be freed.
	*/
	FZ_FUNCTION const char *fz_image_type_name(int type);

	/** Class-aware wrapper for `::fz_include_point_in_rect()`.  */
	/**
		Expand a bbox to include a given point.
		To create a rectangle that encompasses a sequence of points, the
		rectangle must first be set to be the empty rectangle at one of
		the points before including the others.
	*/
	FZ_FUNCTION FzRect fz_include_point_in_rect(const FzRect& r, const FzPoint& p);

	/** Class-aware wrapper for `::fz_init_text_decoder()`.  */
	FZ_FUNCTION void fz_init_text_decoder(const FzTextDecoder& dec, const char *encoding);

	/** Class-aware wrapper for `::fz_install_load_system_font_funcs()`.  */
	/**
		Install functions to allow MuPDF to request fonts from the
		system.
	
		Only one set of hooks can be in use at a time.
	*/
	FZ_FUNCTION void fz_install_load_system_font_funcs(::fz_load_system_font_fn *f, ::fz_load_system_cjk_font_fn *f_cjk, ::fz_load_system_fallback_font_fn *f_fallback);

	/** Class-aware wrapper for `::fz_install_load_system_font_funcs2()`.  */
	/**
	Alternative to fz_install_load_system_font_funcs() that takes args in a
	struct, to allow use from Python/C# via Swig Directors.
	*/
	FZ_FUNCTION void fz_install_load_system_font_funcs2(FzInstallLoadSystemFontFuncsArgs& args);

	/** Class-aware wrapper for `::fz_int2_heap_debug()`.  */
	FZ_FUNCTION void fz_int2_heap_debug(const FzInt2Heap& heap);

	/** Class-aware wrapper for `::fz_int2_heap_dump()`.  */
	FZ_FUNCTION void fz_int2_heap_dump(const FzOutput& out, const FzInt2Heap& heap);

	/** Class-aware wrapper for `::fz_int2_heap_insert()`.  */
	FZ_FUNCTION void fz_int2_heap_insert(const FzInt2Heap& heap, const FzInt2& v);

	/** Class-aware wrapper for `::fz_int2_heap_sort()`.  */
	FZ_FUNCTION void fz_int2_heap_sort(const FzInt2Heap& heap);

	/** Class-aware wrapper for `::fz_int2_heap_uniq()`.  */
	FZ_FUNCTION void fz_int2_heap_uniq(const FzInt2Heap& heap);

	/** Class-aware wrapper for `::fz_int_heap_debug()`.  */
	FZ_FUNCTION void fz_int_heap_debug(const FzIntHeap& heap);

	/** Class-aware wrapper for `::fz_int_heap_dump()`.  */
	FZ_FUNCTION void fz_int_heap_dump(const FzOutput& out, const FzIntHeap& heap);

	/** Class-aware wrapper for `::fz_int_heap_insert()`.  */
	FZ_FUNCTION void fz_int_heap_insert(const FzIntHeap& heap, int v);

	/** Class-aware wrapper for `::fz_int_heap_sort()`.  */
	FZ_FUNCTION void fz_int_heap_sort(const FzIntHeap& heap);

	/** Class-aware wrapper for `::fz_int_heap_uniq()`.  */
	FZ_FUNCTION void fz_int_heap_uniq(const FzIntHeap& heap);

	/** Class-aware wrapper for `::fz_intersect_irect()`.  */
	/**
		Compute intersection of two bounding boxes.
	
		Similar to fz_intersect_rect but operates on two bounding
		boxes instead of two rectangles.
	*/
	FZ_FUNCTION FzIrect fz_intersect_irect(const FzIrect& a, const FzIrect& b);

	/** Class-aware wrapper for `::fz_intersect_rect()`.  */
	/**
		Compute intersection of two rectangles.
	
		Given two rectangles, update the first to be the smallest
		axis-aligned rectangle that covers the area covered by both
		given rectangles. If either rectangle is empty then the
		intersection is also empty. If either rectangle is infinite
		then the intersection is simply the non-infinite rectangle.
		Should both rectangles be infinite, then the intersection is
		also infinite.
	*/
	FZ_FUNCTION FzRect fz_intersect_rect(const FzRect& a, const FzRect& b);

	/** Class-aware wrapper for `::fz_intptr_heap_debug()`.  */
	FZ_FUNCTION void fz_intptr_heap_debug(const FzIntptrHeap& heap);

	/** Class-aware wrapper for `::fz_intptr_heap_dump()`.  */
	FZ_FUNCTION void fz_intptr_heap_dump(const FzOutput& out, const FzIntptrHeap& heap);

	/** Class-aware wrapper for `::fz_intptr_heap_insert()`.  */
	FZ_FUNCTION void fz_intptr_heap_insert(const FzIntptrHeap& heap, const FzIntptr& v);

	/** Class-aware wrapper for `::fz_intptr_heap_sort()`.  */
	FZ_FUNCTION void fz_intptr_heap_sort(const FzIntptrHeap& heap);

	/** Class-aware wrapper for `::fz_intptr_heap_uniq()`.  */
	FZ_FUNCTION void fz_intptr_heap_uniq(const FzIntptrHeap& heap);

	/** Class-aware wrapper for `::fz_invert_bitmap()`.  */
	/**
		Invert bitmap.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION void fz_invert_bitmap(const FzBitmap& bmp);

	/** Class-aware wrapper for `::fz_invert_matrix()`.  */
	/**
		Create an inverse matrix.
	
		matrix: Matrix to invert. A degenerate matrix, where the
		determinant is equal to zero, can not be inverted and the
		original matrix is returned instead.
	
		Returns inverse.
	*/
	FZ_FUNCTION FzMatrix fz_invert_matrix(const FzMatrix& matrix);

	/** Class-aware wrapper for `::fz_invert_pixmap()`.  */
	/**
		Invert all the pixels in a pixmap. All components (process and
		spots) of all pixels are inverted (except alpha, which is
		unchanged).
	*/
	FZ_FUNCTION void fz_invert_pixmap(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_invert_pixmap_alpha()`.  */
	/**
		Invert the alpha of all the pixels in a pixmap.
	*/
	FZ_FUNCTION void fz_invert_pixmap_alpha(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_invert_pixmap_luminance()`.  */
	/**
		Transform the pixels in a pixmap so that luminance of each
		pixel is inverted, and the chrominance remains unchanged (as
		much as accuracy allows).
	
		All components of all pixels are inverted (except alpha, which
		is unchanged). Only supports Grey and RGB bitmaps.
	*/
	FZ_FUNCTION void fz_invert_pixmap_luminance(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_invert_pixmap_raw()`.  */
	/**
		Invert all the pixels in a non-premultiplied pixmap in a
		very naive manner.
	*/
	FZ_FUNCTION void fz_invert_pixmap_raw(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_invert_pixmap_rect()`.  */
	/**
		Invert all the pixels in a given rectangle of a (premultiplied)
		pixmap. All components of all pixels in the rectangle are
		inverted (except alpha, which is unchanged).
	*/
	FZ_FUNCTION void fz_invert_pixmap_rect(const FzPixmap& image, const FzIrect& rect);

	/** Class-aware wrapper for `::fz_irect_from_rect()`.  */
	/**
		Convert a rect into the minimal bounding box
		that covers the rectangle.
	
		Coordinates in a bounding box are integers, so rounding of the
		rects coordinates takes place. The top left corner is rounded
		upwards and left while the bottom right corner is rounded
		downwards and to the right.
	*/
	FZ_FUNCTION FzIrect fz_irect_from_rect(const FzRect& rect);

	/** Class-aware wrapper for `::fz_irect_height()`.  */
	/**
		Return the height of an irect. Invalid irects return 0.
	*/
	FZ_FUNCTION int fz_irect_height(const FzIrect& r);

	/** Class-aware wrapper for `::fz_irect_width()`.  */
	/**
		Return the width of an irect. Invalid irects return 0.
	*/
	FZ_FUNCTION unsigned int fz_irect_width(const FzIrect& r);

	/** Class-aware wrapper for `::fz_is_cfb_archive()`.  */
	/**
		Detect if stream object is a cfb archive.
	
		Assumes that the stream object is seekable.
	*/
	FZ_FUNCTION int fz_is_cfb_archive(const FzStream& file);

	/** Class-aware wrapper for `::fz_is_directory()`.  */
	/**
		Determine if a given path is a directory.
	
		In the case of the path not existing, or having no access
		we will return 0.
	*/
	FZ_FUNCTION int fz_is_directory(const char *path);

	/** Class-aware wrapper for `::fz_is_document_reflowable()`.  */
	/**
		Is the document reflowable.
	
		Returns 1 to indicate reflowable documents, otherwise 0.
	*/
	FZ_FUNCTION int fz_is_document_reflowable(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_is_empty_irect()`.  */
	FZ_FUNCTION int fz_is_empty_irect(const FzIrect& r);

	/** Class-aware wrapper for `::fz_is_empty_quad()`.  */
	/**
		Is a quad empty?
	*/
	FZ_FUNCTION int fz_is_empty_quad(const FzQuad& q);

	/** Class-aware wrapper for `::fz_is_empty_rect()`.  */
	/**
		Check if rectangle is empty.
	
		An empty rectangle is defined as one whose area is zero.
		All invalid rectangles are empty.
	*/
	FZ_FUNCTION int fz_is_empty_rect(const FzRect& r);

	/** Class-aware wrapper for `::fz_is_eof()`.  */
	/**
		Query if the stream has reached EOF (during normal bytewise
		reading).
	
		See fz_is_eof_bits for the equivalent function for bitwise
		reading.
	*/
	FZ_FUNCTION int fz_is_eof(const FzStream& stm);

	/** Class-aware wrapper for `::fz_is_eof_bits()`.  */
	/**
		Query if the stream has reached EOF (during bitwise
		reading).
	
		See fz_is_eof for the equivalent function for bytewise
		reading.
	*/
	FZ_FUNCTION int fz_is_eof_bits(const FzStream& stm);

	/** Class-aware wrapper for `::fz_is_external_link()`.  */
	/**
		Query whether a link is external to a document (determined by
		uri containing a ':', intended to match with '://' which
		separates the scheme from the scheme specific parts in URIs).
	*/
	FZ_FUNCTION int fz_is_external_link(const char *uri);

	/** Class-aware wrapper for `::fz_is_identity()`.  */
	FZ_FUNCTION int fz_is_identity(const FzMatrix& m);

	/** Class-aware wrapper for `::fz_is_infinite_irect()`.  */
	/**
		Check if an integer rectangle
		is infinite.
	*/
	FZ_FUNCTION int fz_is_infinite_irect(const FzIrect& r);

	/** Class-aware wrapper for `::fz_is_infinite_quad()`.  */
	/**
		Is a quad infinite?
	*/
	FZ_FUNCTION int fz_is_infinite_quad(const FzQuad& q);

	/** Class-aware wrapper for `::fz_is_infinite_rect()`.  */
	/**
		Check if rectangle is infinite.
	*/
	FZ_FUNCTION int fz_is_infinite_rect(const FzRect& r);

	/** Class-aware wrapper for `::fz_is_irect_inside_irect()`.  */
	/**
		Inclusion test for irects.
	
		rects are assumed to be both open or both closed.
	
		No invalid rect can include any other rect.
		No invalid rect can be included by any rect.
		Empty (point) rects can include themselves.
		Empty (line) rects can include many (subline) rects.
	*/
	FZ_FUNCTION int fz_is_irect_inside_irect(const FzIrect& inner, const FzIrect& outer);

	/** Class-aware wrapper for `::fz_is_libarchive_archive()`.  */
	/**
		Detect if stream object is an archive supported by libarchive.
	
		Assumes that the stream object is seekable.
	*/
	FZ_FUNCTION int fz_is_libarchive_archive(const FzStream& file);

	/** Class-aware wrapper for `::fz_is_lossy_image()`.  */
	FZ_FUNCTION int fz_is_lossy_image(const FzImage& image);

	/** Class-aware wrapper for `::fz_is_page_range()`.  */
	/**
		Check and parse string into page ranges:
			/,?(-?\d+|N)(-(-?\d+|N))?/
	*/
	FZ_FUNCTION int fz_is_page_range(const char *s);

	/** Class-aware wrapper for `::fz_is_pixmap_monochrome()`.  */
	/**
		Check if the pixmap is a 1-channel image containing samples with
		only values 0 and 255
	*/
	FZ_FUNCTION int fz_is_pixmap_monochrome(const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_is_point_inside_irect()`.  */
	/**
		Inclusion test for irects. (Rect is assumed to be open, i.e.
		top right corner is not included).
	*/
	FZ_FUNCTION int fz_is_point_inside_irect(int x, int y, const FzIrect& r);

	/** Class-aware wrapper for `::fz_is_point_inside_quad()`.  */
	/**
		Inclusion test for quads.
	*/
	FZ_FUNCTION int fz_is_point_inside_quad(const FzPoint& p, const FzQuad& q);

	/** Class-aware wrapper for `::fz_is_point_inside_rect()`.  */
	/**
		Inclusion test for rects. (Rect is assumed to be open, i.e.
		top right corner is not included).
	*/
	FZ_FUNCTION int fz_is_point_inside_rect(const FzPoint& p, const FzRect& r);

	/** Class-aware wrapper for `::fz_is_pow2()`.  */
	FZ_FUNCTION int fz_is_pow2(int a);

	/** Class-aware wrapper for `::fz_is_quad_inside_quad()`.  */
	/**
		Inclusion test for quad in quad.
	
		This may break down if quads are not 'well formed'.
	*/
	FZ_FUNCTION int fz_is_quad_inside_quad(const FzQuad& needle, const FzQuad& haystack);

	/** Class-aware wrapper for `::fz_is_quad_intersecting_quad()`.  */
	/**
		Intersection test for quads.
	
		This may break down if quads are not 'well formed'.
	*/
	FZ_FUNCTION int fz_is_quad_intersecting_quad(const FzQuad& a, const FzQuad& b);

	/** Class-aware wrapper for `::fz_is_rect_inside_rect()`.  */
	/**
		Inclusion test for rects.
	
		rects are assumed to be both open or both closed.
	
		No invalid rect can include any other rect.
		No invalid rect can be included by any rect.
		Empty (point) rects can include themselves.
		Empty (line) rects can include many (subline) rects.
	*/
	FZ_FUNCTION int fz_is_rect_inside_rect(const FzRect& inner, const FzRect& outer);

	/** Class-aware wrapper for `::fz_is_rectilinear()`.  */
	/**
		Check if a transformation is rectilinear.
	
		Rectilinear means that no shearing is present and that any
		rotations present are a multiple of 90 degrees. Usually this
		is used to make sure that axis-aligned rectangles before the
		transformation are still axis-aligned rectangles afterwards.
	*/
	FZ_FUNCTION int fz_is_rectilinear(const FzMatrix& m);

	/** Class-aware wrapper for `::fz_is_tar_archive()`.  */
	/**
		Detect if stream object is a tar archive.
	
		Assumes that the stream object is seekable.
	*/
	FZ_FUNCTION int fz_is_tar_archive(const FzStream& file);

	/** Class-aware wrapper for `::fz_is_valid_blend_colorspace()`.  */
	/**
		Check to see that a colorspace is appropriate to be used as
		a blending space (i.e. only grey, rgb or cmyk).
	*/
	FZ_FUNCTION int fz_is_valid_blend_colorspace(const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_is_valid_irect()`.  */
	/**
		Check if an integer rectangle is valid.
	*/
	FZ_FUNCTION int fz_is_valid_irect(const FzIrect& r);

	/** Class-aware wrapper for `::fz_is_valid_quad()`.  */
	/**
		Is a quad valid?
	*/
	FZ_FUNCTION int fz_is_valid_quad(const FzQuad& q);

	/** Class-aware wrapper for `::fz_is_valid_rect()`.  */
	/**
		Check if rectangle is valid.
	*/
	FZ_FUNCTION int fz_is_valid_rect(const FzRect& r);

	/** Class-aware wrapper for `::fz_is_valid_xml_char()`.  */
	FZ_FUNCTION int fz_is_valid_xml_char(int c);

	/** Class-aware wrapper for `::fz_is_valid_xml_string()`.  */
	FZ_FUNCTION int fz_is_valid_xml_string(const char *s);

	/** Class-aware wrapper for `::fz_is_zip_archive()`.  */
	/**
		Detect if stream object is a zip archive.
	
		Assumes that the stream object is seekable.
	*/
	FZ_FUNCTION int fz_is_zip_archive(const FzStream& file);

	/** Class-aware wrapper for `::fz_iso8859_1_from_unicode()`.  */
	FZ_FUNCTION int fz_iso8859_1_from_unicode(int u);

	/** Class-aware wrapper for `::fz_iso8859_7_from_unicode()`.  */
	FZ_FUNCTION int fz_iso8859_7_from_unicode(int u);

	/** Class-aware wrapper for `::fz_jbig2_globals_data()`.  */
	/**
		Return buffer containing jbig2 globals data stream.
	*/
	FZ_FUNCTION FzBuffer fz_jbig2_globals_data(const FzJbig2Globals& globals);

	/* Class-aware wrapper for `fz_json_array_get()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_json_array_length()`.  */
	FZ_FUNCTION int fz_json_array_length(const FzJson& array);

	/** Class-aware wrapper for `::fz_json_array_push()`.  */
	FZ_FUNCTION void fz_json_array_push(const FzPool& pool, const FzJson& array, const FzJson& item);

	/** Class-aware wrapper for `::fz_json_is_array()`.  */
	FZ_FUNCTION int fz_json_is_array(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_is_boolean()`.  */
	FZ_FUNCTION int fz_json_is_boolean(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_is_null()`.  */
	FZ_FUNCTION int fz_json_is_null(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_is_number()`.  */
	FZ_FUNCTION int fz_json_is_number(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_is_object()`.  */
	FZ_FUNCTION int fz_json_is_object(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_is_string()`.  */
	FZ_FUNCTION int fz_json_is_string(const FzJson& json);

	/* Class-aware wrapper for `fz_json_new_array()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/* Class-aware wrapper for `fz_json_new_boolean()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/* Class-aware wrapper for `fz_json_new_null()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/* Class-aware wrapper for `fz_json_new_number()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/* Class-aware wrapper for `fz_json_new_object()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/* Class-aware wrapper for `fz_json_new_string()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/* Class-aware wrapper for `fz_json_object_get()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_json_object_set()`.  */
	FZ_FUNCTION void fz_json_object_set(const FzPool& pool, const FzJson& object, const char *key, const FzJson& item);

	/** Class-aware wrapper for `::fz_json_to_boolean()`.  */
	FZ_FUNCTION int fz_json_to_boolean(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_to_number()`.  */
	FZ_FUNCTION double fz_json_to_number(const FzJson& json);

	/** Class-aware wrapper for `::fz_json_to_string()`.  */
	FZ_FUNCTION const char *fz_json_to_string(const FzJson& json);

	/** Class-aware wrapper for `::fz_koi8u_from_unicode()`.  */
	FZ_FUNCTION int fz_koi8u_from_unicode(int u);

	/** Class-aware wrapper for `::fz_last_page()`.  */
	/**
		Function to get the location for the last page in the document.
		Using this can be far more efficient in some cases than calling
		fz_count_pages and using the page number.
	*/
	FZ_FUNCTION FzLocation fz_last_page(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_layout_document()`.  */
	/**
		Layout reflowable document types.
	
		w, h: Page size in points.
		em: Default font size in points.
	*/
	FZ_FUNCTION void fz_layout_document(const FzDocument& doc, float w, float h, float em);

	/** Class-aware wrapper for `::fz_linecap_from_string()`.  */
	FZ_FUNCTION ::fz_linecap fz_linecap_from_string(const char *s);

	/** Class-aware wrapper for `::fz_linejoin_from_string()`.  */
	FZ_FUNCTION ::fz_linejoin fz_linejoin_from_string(const char *s);

	/** Class-aware wrapper for `::fz_lineto()`.  */
	/**
		Append a 'lineto' command to an open path.
	
		path: The path to modify.
	
		x, y: The coordinate to line to.
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_lineto(const FzPath& path, float x, float y);

	/** Class-aware wrapper for `::fz_list_archive_entry()`.  */
	/**
		Get listed name of entry position idx.
	
		idx: Must be a value >= 0 < return value from
		fz_count_archive_entries. If not in range NULL will be
		returned.
	
		May throw an exception if this type of archive cannot list the
		entries (such as a directory).
	*/
	FZ_FUNCTION const char *fz_list_archive_entry(const FzArchive& arch, int idx);

	/** Class-aware wrapper for `::fz_load_bmp_subimage()`.  */
	FZ_FUNCTION FzPixmap fz_load_bmp_subimage(const unsigned char *buf, size_t len, int subimage);

	/** Class-aware wrapper for `::fz_load_bmp_subimage_count()`.  */
	FZ_FUNCTION int fz_load_bmp_subimage_count(const unsigned char *buf, size_t len);

	/** Class-aware wrapper for `::fz_load_chapter_page()`.  */
	/**
		Load a page.
	
		After fz_load_page is it possible to retrieve the size of the
		page using fz_bound_page, or to render the page using
		fz_run_page_*. Free the page by calling fz_drop_page.
	
		chapter: chapter number, 0 is the first chapter of the document.
		number: page number, 0 is the first page of the chapter.
	*/
	FZ_FUNCTION FzPage fz_load_chapter_page(const FzDocument& doc, int chapter, int page);

	/** Class-aware wrapper for `::fz_load_fallback_font()`.  */
	/**
		Try to load a fallback font for the
		given combination of font attributes. Whether a font is
		present or not will depend on the configuration in which
		MuPDF is built.
	
		script: The script desired (e.g. UCDN_SCRIPT_KATAKANA).
	
		language: The language desired (e.g. FZ_LANG_ja).
	
		serif: 1 if serif desired, 0 otherwise.
	
		bold: 1 if bold desired, 0 otherwise.
	
		italic: 1 if italic desired, 0 otherwise.
	
		Returns a new font handle, or NULL if not available.
	*/
	FZ_FUNCTION FzFont fz_load_fallback_font(int script, int language, int serif, int bold, int italic);

	/** Class-aware wrapper for `::fz_load_jbig2_globals()`.  */
	/**
		Create a jbig2 globals record from a buffer.
	
		Immutable once created.
	*/
	FZ_FUNCTION FzJbig2Globals fz_load_jbig2_globals(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_load_jbig2_subimage()`.  */
	FZ_FUNCTION FzPixmap fz_load_jbig2_subimage(const unsigned char *buf, size_t len, int subimage);

	/** Class-aware wrapper for `::fz_load_jbig2_subimage_count()`.  */
	FZ_FUNCTION int fz_load_jbig2_subimage_count(const unsigned char *buf, size_t len);

	/** Class-aware wrapper for `::fz_load_jpx()`.  */
	/**
		Exposed for PDF.
	*/
	FZ_FUNCTION FzPixmap fz_load_jpx(const unsigned char *data, size_t size, const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_load_links()`.  */
	/**
		Load the list of links for a page.
	
		Returns a linked list of all the links on the page, each with
		its clickable region and link destination. Each link is
		reference counted so drop and free the list of links by
		calling fz_drop_link on the pointer return from fz_load_links.
	
		page: Page obtained from fz_load_page.
	*/
	FZ_FUNCTION FzLink fz_load_links(const FzPage& page);

	/** Class-aware wrapper for `::fz_load_outline()`.  */
	/**
		Load the hierarchical document outline.
	
		Should be freed by fz_drop_outline.
	*/
	FZ_FUNCTION FzOutline fz_load_outline(const FzDocument& doc);

	/** Class-aware wrapper for `::fz_load_outline_from_iterator()`.  */
	/**
		Routine to implement the old Structure based API from an iterator.
	*/
	FZ_FUNCTION FzOutline fz_load_outline_from_iterator(const FzOutlineIterator& iter);

	/** Class-aware wrapper for `::fz_load_page()`.  */
	/**
		Load a given page number from a document. This may be much less
		efficient than loading by location (chapter+page) for some
		document types.
	*/
	FZ_FUNCTION FzPage fz_load_page(const FzDocument& doc, int number);

	/** Class-aware wrapper for `::fz_load_pnm_subimage()`.  */
	FZ_FUNCTION FzPixmap fz_load_pnm_subimage(const unsigned char *buf, size_t len, int subimage);

	/** Class-aware wrapper for `::fz_load_pnm_subimage_count()`.  */
	FZ_FUNCTION int fz_load_pnm_subimage_count(const unsigned char *buf, size_t len);

	/** Class-aware wrapper for `::fz_load_system_cjk_font()`.  */
	/**
		Attempt to load a given font from
		the system.
	
		name: The name of the desired font.
	
		ordering: The ordering to load the font from (e.g. FZ_ADOBE_KOREA)
	
		serif: 1 if serif desired, 0 otherwise.
	
		Returns a new font handle, or NULL if no matching font was found
		(or on error).
	*/
	FZ_FUNCTION FzFont fz_load_system_cjk_font(const char *name, int ordering, int serif);

	/** Class-aware wrapper for `::fz_load_system_font()`.  */
	/**
		Attempt to load a given font from the system.
	
		name: The name of the desired font.
	
		bold: 1 if bold desired, 0 otherwise.
	
		italic: 1 if italic desired, 0 otherwise.
	
		needs_exact_metrics: 1 if an exact metrical match is required,
		0 otherwise.
	
		Returns a new font handle, or NULL if no matching font was found
		(or on error).
	*/
	FZ_FUNCTION FzFont fz_load_system_font(const char *name, int bold, int italic, int needs_exact_metrics);

	/** Class-aware wrapper for `::fz_load_tiff_subimage()`.  */
	FZ_FUNCTION FzPixmap fz_load_tiff_subimage(const unsigned char *buf, size_t len, int subimage);

	/** Class-aware wrapper for `::fz_load_tiff_subimage_count()`.  */
	/**
		Exposed for CBZ.
	*/
	FZ_FUNCTION int fz_load_tiff_subimage_count(const unsigned char *buf, size_t len);

	/** Class-aware wrapper for `::fz_load_user_css()`.  */
	/**
		Set the user stylesheet by loading the source from a file.
		If the file is missing, do nothing.
	*/
	FZ_FUNCTION void fz_load_user_css(const char *filename);

	/** Class-aware wrapper for `::fz_location_from_page_number()`.  */
	/**
		Converts from page number to chapter+page. This may cause many
		chapters to be laid out in order to calculate the number of
		pages within those chapters.
	*/
	FZ_FUNCTION FzLocation fz_location_from_page_number(const FzDocument& doc, int number);

	/** Class-aware wrapper for `::fz_lock()`.  */
	/**
		Lock one of the user supplied mutexes.
	*/
	FZ_FUNCTION void fz_lock(int lock);

	/** Class-aware wrapper for `::fz_log_error()`.  */
	/**
		Log a (preformatted) string to the registered
		error stream (stderr by default).
	*/
	FZ_FUNCTION void fz_log_error(const char *str);

	/** Class-aware wrapper for `::fz_lookup_base14_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_base14_font(const char *name)` => `(const unsigned char *, int len)`
	 */
	/**
		Search the builtin base14 fonts for a match.
		Whether a given font is present or not will depend on the
		configuration in which MuPDF is built.
	
		name: The name of the font desired.
	
		len: Pointer to a place to receive the length of the discovered
		font buffer.
	
		Returns a pointer to the font file data, or NULL if not present.
	*/
	FZ_FUNCTION const unsigned char *fz_lookup_base14_font(const char *name, int *len);

	/** Class-aware wrapper for `::fz_lookup_blendmode()`.  */
	/**
		Map from (case sensitive) blend mode string to enumeration.
	*/
	FZ_FUNCTION int fz_lookup_blendmode(const char *name);

	/** Class-aware wrapper for `::fz_lookup_bookmark()`.  */
	/**
		Find a bookmark and return its page number.
	*/
	FZ_FUNCTION FzLocation fz_lookup_bookmark(const FzDocument& doc, ::fz_bookmark mark);

	/** Class-aware wrapper for `::fz_lookup_builtin_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_builtin_font(const char *name, int bold, int italic)` => `(const unsigned char *, int len)`
	 */
	/**
		Search the builtin fonts for a match.
		Whether a given font is present or not will depend on the
		configuration in which MuPDF is built.
	
		name: The name of the font desired.
	
		bold: 1 if bold desired, 0 otherwise.
	
		italic: 1 if italic desired, 0 otherwise.
	
		len: Pointer to a place to receive the length of the discovered
		font buffer.
	
		Returns a pointer to the font file data, or NULL if not present.
	*/
	FZ_FUNCTION const unsigned char *fz_lookup_builtin_font(const char *name, int bold, int italic, int *len);

	/** Class-aware wrapper for `::fz_lookup_cjk_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_cjk_font(int ordering)` => `(const unsigned char *, int len, int index)`
	 */
	/**
		Search the builtin cjk fonts for a match.
		Whether a font is present or not will depend on the
		configuration in which MuPDF is built.
	
		ordering: The desired ordering of the font (e.g. FZ_ADOBE_KOREA).
	
		len: Pointer to a place to receive the length of the discovered
		font buffer.
	
		Returns a pointer to the font file data, or NULL if not present.
	*/
	FZ_FUNCTION const unsigned char *fz_lookup_cjk_font(int ordering, int *len, int *index);

	/** Class-aware wrapper for `::fz_lookup_cjk_font_by_language()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_cjk_font_by_language(const char *lang)` => `(const unsigned char *, int len, int subfont)`
	 */
	/**
		Search the builtin cjk fonts for a match for a given language.
		Whether a font is present or not will depend on the
		configuration in which MuPDF is built.
	
		lang: Pointer to a (case sensitive) language string (e.g.
		"ja", "ko", "zh-Hant" etc).
	
		len: Pointer to a place to receive the length of the discovered
		font buffer.
	
		subfont: Pointer to a place to store the subfont index of the
		discovered font.
	
		Returns a pointer to the font file data, or NULL if not present.
	*/
	FZ_FUNCTION const unsigned char *fz_lookup_cjk_font_by_language(const char *lang, int *len, int *subfont);

	/** Class-aware wrapper for `::fz_lookup_cjk_ordering_by_language()`.  */
	/**
		Return the matching FZ_ADOBE_* ordering
		for the given language tag, such as "zh-Hant", "zh-Hans", "ja", or "ko".
	*/
	FZ_FUNCTION int fz_lookup_cjk_ordering_by_language(const char *name);

	/** Class-aware wrapper for `::fz_lookup_image_type()`.  */
	/**
		Map from (case sensitive) image type string to FZ_IMAGE_*
		type value.
	*/
	FZ_FUNCTION int fz_lookup_image_type(const char *type);

	/** Class-aware wrapper for `::fz_lookup_metadata()`.  */
	/**
		Retrieve document meta data strings.
	
		doc: The document to query.
	
		key: Which meta data key to retrieve...
	
		Basic information:
			'format'	-- Document format and version.
			'encryption'	-- Description of the encryption used.
	
		From the document information dictionary:
			'info:Title'
			'info:Author'
			'info:Subject'
			'info:Keywords'
			'info:Creator'
			'info:Producer'
			'info:CreationDate'
			'info:ModDate'
	
		buf: The buffer to hold the results (a nul-terminated UTF-8
		string).
	
		size: Size of 'buf'.
	
		Returns the number of bytes need to store the string plus terminator
		(will be larger than 'size' if the output was truncated), or -1 if the
		key is not recognized or found.
	*/
	FZ_FUNCTION int fz_lookup_metadata(const FzDocument& doc, const char *key, char *buf, size_t size);

	/** Class-aware wrapper for `::fz_lookup_metadata2()`.  */
	/**
	C++ alternative to `fz_lookup_metadata()` that returns a `std::string`
	or calls `fz_throw()` if not found.
	*/
	FZ_FUNCTION std::string fz_lookup_metadata2(const FzDocument& doc, const char *key);

	/** Class-aware wrapper for `::fz_lookup_noto_boxes_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_boxes_font()` => `(const unsigned char *, int len)`
	 */
	FZ_FUNCTION const unsigned char *fz_lookup_noto_boxes_font(int *len);

	/** Class-aware wrapper for `::fz_lookup_noto_emoji_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_emoji_font()` => `(const unsigned char *, int len)`
	 */
	FZ_FUNCTION const unsigned char *fz_lookup_noto_emoji_font(int *len);

	/** Class-aware wrapper for `::fz_lookup_noto_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_font(int script, int lang)` => `(const unsigned char *, int len, int subfont)`
	 */
	/**
		Search the builtin noto fonts for a match.
		Whether a font is present or not will depend on the
		configuration in which MuPDF is built.
	
		script: The script desired (e.g. UCDN_SCRIPT_KATAKANA).
	
		lang: The language desired (e.g. FZ_LANG_ja).
	
		len: Pointer to a place to receive the length of the discovered
		font buffer.
	
		Returns a pointer to the font file data, or NULL if not present.
	*/
	FZ_FUNCTION const unsigned char *fz_lookup_noto_font(int script, int lang, int *len, int *subfont);

	/** Class-aware wrapper for `::fz_lookup_noto_math_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_math_font()` => `(const unsigned char *, int len)`
	 */
	/**
		Search the builtin noto fonts specific symbol fonts.
		Whether a font is present or not will depend on the
		configuration in which MuPDF is built.
	*/
	FZ_FUNCTION const unsigned char *fz_lookup_noto_math_font(int *len);

	/** Class-aware wrapper for `::fz_lookup_noto_music_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_music_font()` => `(const unsigned char *, int len)`
	 */
	FZ_FUNCTION const unsigned char *fz_lookup_noto_music_font(int *len);

	/** Class-aware wrapper for `::fz_lookup_noto_stem_from_script()`.  */
	/**
		Look up the Noto font file name for a given script.
		From the returned font stem, you can look for Noto fonts on the system in the form:
			Noto(Sans|Serif)${STEM}-Regular.(otf|ttf)
	*/
	FZ_FUNCTION const char *fz_lookup_noto_stem_from_script(int script, int language);

	/** Class-aware wrapper for `::fz_lookup_noto_symbol1_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_symbol1_font()` => `(const unsigned char *, int len)`
	 */
	FZ_FUNCTION const unsigned char *fz_lookup_noto_symbol1_font(int *len);

	/** Class-aware wrapper for `::fz_lookup_noto_symbol2_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_lookup_noto_symbol2_font()` => `(const unsigned char *, int len)`
	 */
	FZ_FUNCTION const unsigned char *fz_lookup_noto_symbol2_font(int *len);

	/** Class-aware wrapper for `::fz_lookup_rendering_intent()`.  */
	/**
		Map from (case sensitive) rendering intent string to enumeration
		value.
	*/
	FZ_FUNCTION int fz_lookup_rendering_intent(const char *name);

	/** Class-aware wrapper for `::fz_lookup_script_name()`.  */
	FZ_FUNCTION const char *fz_lookup_script_name(int script, int language);

	/** Class-aware wrapper for `::fz_make_bookmark()`.  */
	/**
		Create a bookmark for the given page, which can be used to find
		the same location after the document has been laid out with
		different parameters.
	*/
	FZ_FUNCTION ::fz_bookmark fz_make_bookmark(const FzDocument& doc, const FzLocation& loc);

	/** Class-aware wrapper for `::fz_make_irect()`.  */
	FZ_FUNCTION FzIrect fz_make_irect(int x0, int y0, int x1, int y1);

	/* Class-aware wrapper for `fz_make_link_dest_none()`
	is not available because returned wrapper class for `fz_link_dest`
	is non-copyable. */

	/* Class-aware wrapper for `fz_make_link_dest_xyz()`
	is not available because returned wrapper class for `fz_link_dest`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_make_location()`.  */
	/**
		Simple constructor for fz_locations.
	*/
	FZ_FUNCTION FzLocation fz_make_location(int chapter, int page);

	/** Class-aware wrapper for `::fz_make_matrix()`.  */
	FZ_FUNCTION FzMatrix fz_make_matrix(float a, float b, float c, float d, float e, float f);

	/** Class-aware wrapper for `::fz_make_point()`.  */
	FZ_FUNCTION FzPoint fz_make_point(float x, float y);

	/** Class-aware wrapper for `::fz_make_quad()`.  */
	/**
		Inline convenience construction function.
	*/
	FZ_FUNCTION FzQuad fz_make_quad(float ul_x, float ul_y, float ur_x, float ur_y, float ll_x, float ll_y, float lr_x, float lr_y);

	/** Class-aware wrapper for `::fz_make_rect()`.  */
	FZ_FUNCTION FzRect fz_make_rect(float x0, float y0, float x1, float y1);

	/** Class-aware wrapper for `::fz_malloc()`.  */
	/**
		Allocate uninitialized memory of a given size.
		Does NOT clear the memory!
	
		May return NULL for size = 0.
	
		Throws exception in the event of failure to allocate.
	*/
	FZ_FUNCTION void *fz_malloc(size_t size);

	/** Class-aware wrapper for `::fz_malloc_aligned()`.  */
	/**
		fz_malloc equivalent, except that the block is guaranteed aligned.
		Block must be freed later using fz_free_aligned.
	*/
	FZ_FUNCTION void *fz_malloc_aligned(size_t size, int align);

	/** Class-aware wrapper for `::fz_malloc_no_throw()`.  */
	/**
		fz_malloc equivalent that returns NULL rather than throwing
		exceptions.
	*/
	FZ_FUNCTION void *fz_malloc_no_throw(size_t size);

	/** Class-aware wrapper for `::fz_matrix_expansion()`.  */
	/**
		Calculate average scaling factor of matrix.
	*/
	FZ_FUNCTION float fz_matrix_expansion(const FzMatrix& m);

	/** Class-aware wrapper for `::fz_matrix_max_expansion()`.  */
	/**
		Find the largest expansion performed by this matrix.
		(i.e. max(abs(m.a),abs(m.b),abs(m.c),abs(m.d))
	*/
	FZ_FUNCTION float fz_matrix_max_expansion(const FzMatrix& m);

	/** Class-aware wrapper for `::fz_max()`.  */
	FZ_FUNCTION float fz_max(float a, float b);

	/** Class-aware wrapper for `::fz_maxi()`.  */
	FZ_FUNCTION int fz_maxi(int a, int b);

	/** Class-aware wrapper for `::fz_maxi64()`.  */
	FZ_FUNCTION int64_t fz_maxi64(int64_t a, int64_t b);

	/** Class-aware wrapper for `::fz_maxz()`.  */
	FZ_FUNCTION size_t fz_maxz(size_t a, size_t b);

	/** Class-aware wrapper for `::fz_md5_buffer()`.  */
	/**
		Create an MD5 digest from buffer contents.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION void fz_md5_buffer(const FzBuffer& buffer, unsigned char digest[16]);

	/** Class-aware wrapper for `::fz_md5_final()`.  */
	/**
		MD5 finalization. Ends an MD5 message-digest operation, writing
		the message digest and zeroizing the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_md5_final(FzMd5& state, unsigned char digest[16]);

	/** Class-aware wrapper for `::fz_md5_final2()`.  */
	/**
	C++ alternative to fz_md5_final() that returns the digest by value.
	*/
	FZ_FUNCTION std::vector<unsigned char> fz_md5_final2(FzMd5& md5);

	/** Class-aware wrapper for `::fz_md5_init()`.  */
	/**
		MD5 initialization. Begins an MD5 operation, writing a new
		context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_md5_init(FzMd5& state);

	/** Class-aware wrapper for `::fz_md5_pixmap()`.  */
	FZ_FUNCTION void fz_md5_pixmap(const FzPixmap& pixmap, unsigned char digest[16]);

	/** Class-aware wrapper for `::fz_md5_pixmap2()`.  */
	/**
	C++ alternative to `fz_md5_pixmap()` that returns the digest by value.
	*/
	FZ_FUNCTION std::vector<unsigned char> fz_md5_pixmap2(const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_md5_update()`.  */
	/**
		MD5 block update operation. Continues an MD5 message-digest
		operation, processing another message block, and updating the
		context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_md5_update(FzMd5& state, const unsigned char *input, size_t inlen);

	/** Class-aware wrapper for `::fz_md5_update_int64()`.  */
	/**
		MD5 block update operation. Continues an MD5 message-digest
		operation, processing an int64, and updating the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_md5_update_int64(FzMd5& state, int64_t i);

	/** Class-aware wrapper for `::fz_measure_string()`.  */
	/**
		Measure the advance width of a UTF8 string should it be added to a text object.
	
		This uses the same layout algorithms as fz_show_string, and can be used
		to calculate text alignment adjustments.
	*/
	FZ_FUNCTION FzMatrix fz_measure_string(const FzFont& user_font, const FzMatrix& trm, const char *s, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language language);

	/** Class-aware wrapper for `::fz_memmem()`.  */
	/**
		Find the start of the first occurrence of the substring needle in haystack.
	*/
	FZ_FUNCTION void *fz_memmem(const void *haystack, size_t haystacklen, const void *needle, size_t needlelen);

	/** Class-aware wrapper for `::fz_memrnd()`.  */
	/**
		Fill block with len bytes of pseudo-randomness.
	*/
	FZ_FUNCTION void fz_memrnd(uint8_t *block, int len);

	/** Class-aware wrapper for `::fz_min()`.  */
	FZ_FUNCTION float fz_min(float a, float b);

	/** Class-aware wrapper for `::fz_mini()`.  */
	FZ_FUNCTION int fz_mini(int a, int b);

	/** Class-aware wrapper for `::fz_mini64()`.  */
	FZ_FUNCTION int64_t fz_mini64(int64_t a, int64_t b);

	/** Class-aware wrapper for `::fz_minz()`.  */
	FZ_FUNCTION size_t fz_minz(size_t a, size_t b);

	/** Class-aware wrapper for `::fz_mkdir()`.  */
	FZ_FUNCTION int fz_mkdir(char *path);

	/** Class-aware wrapper for `::fz_morph_error()`.  */
	/**
		Called within a catch block this modifies the current
		exception's code. If it's of type 'fromcode' it is
		modified to 'tocode'. Typically used for 'downgrading'
		exception severity.
	*/
	FZ_FUNCTION void fz_morph_error(int fromcode, int tocode);

	/** Class-aware wrapper for `::fz_mount_multi_archive()`.  */
	/**
		Add an archive to the set of archives handled by a multi
		archive.
	
		If path is NULL, then the archive contents will appear at the
		top level, otherwise, the archives contents will appear prefixed
		by path.
	*/
	FZ_FUNCTION void fz_mount_multi_archive(const FzArchive& arch_, const FzArchive& sub, const char *path);

	/** Class-aware wrapper for `::fz_moveto()`.  */
	/**
		Append a 'moveto' command to a path.
		This 'opens' a path.
	
		path: The path to modify.
	
		x, y: The coordinate to move to.
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_moveto(const FzPath& path, float x, float y);

	/** Class-aware wrapper for `::fz_mul255()`.  */
	/**
		Multiply scaled two integers in the 0..255 range
	*/
	FZ_FUNCTION int fz_mul255(int a, int b);

	/** Class-aware wrapper for `::fz_needs_password()`.  */
	/**
		Check if a document is encrypted with a
		non-blank password.
	*/
	FZ_FUNCTION int fz_needs_password(const FzDocument& doc);

	/* Class-aware wrapper for `fz_new_arc4_output()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_archive_of_size()`.  */
	FZ_FUNCTION FzArchive fz_new_archive_of_size(const FzStream& file, int size);

	/* Class-aware wrapper for `fz_new_ascii85_output()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_asciihex_output()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_band_writer_of_size()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_barcode_image()`.  */
	/**
		Create an fz_image from a barcode definition.
	
		type: The type of barcode to create.
		value: The value of the barcode.
		size: The size of the barcode.
		ec_level: error correction level 0-8.
		quiet: whether to include quiet zones (0 or 1).
		hrt: whether to include human readable text below the barcode (0 or 1).
	
		returns a created fz_image.
	*/
	FZ_FUNCTION FzImage fz_new_barcode_image(::fz_barcode_type type, const char *value, int size, int ec_level, int quiet, int hrt);

	/** Class-aware wrapper for `::fz_new_barcode_pixmap()`.  */
	/**
		Create an fz_pixmap from a barcode definition.
	
		type: The type of barcode to create.
		value: The value of the barcode.
		size: The size of the barcode.
		ec_level: error correction level 0-8.
		quiet: whether to include quiet zones (0 or 1).
		hrt: whether to include human readable text below the barcode (0 or 1).
	
		returns a created fz_pixmap.
	*/
	FZ_FUNCTION FzPixmap fz_new_barcode_pixmap(::fz_barcode_type type, const char *value, int size, int ec_level, int quiet, int hrt);

	/** Class-aware wrapper for `::fz_new_base14_font()`.  */
	/**
		Create a new font from one of the built-in fonts.
	*/
	FZ_FUNCTION FzFont fz_new_base14_font(const char *name);

	/** Class-aware wrapper for `::fz_new_bbox_device()`.  */
	/**
		Create a device to compute the bounding
		box of all marks on a page.
	
		The returned bounding box will be the union of all bounding
		boxes of all objects on a page.
	*/
	FZ_FUNCTION FzDevice fz_new_bbox_device(FzRect& rectp);

	/** Class-aware wrapper for `::fz_new_bitmap()`.  */
	/**
		Create a new bitmap.
	
		w, h: Width and Height for the bitmap
	
		n: Number of color components (assumed to be a divisor of 8)
	
		xres, yres: X and Y resolutions (in pixels per inch).
	
		Returns pointer to created bitmap structure. The bitmap
		data is uninitialised.
	*/
	FZ_FUNCTION FzBitmap fz_new_bitmap(int w, int h, int n, int xres, int yres);

	/** Class-aware wrapper for `::fz_new_bitmap_from_image()`.  */
	/**
		Make a bitmap from a pixmap and a halftone.
	
		img: The image to generate from. Currently must be a single
		color component with no alpha.
	
		ht: The halftone to use. NULL implies the default halftone.
	
		Returns the resultant bitmap. Throws exceptions in the case of
		failure to allocate.
	*/
	FZ_FUNCTION FzBitmap fz_new_bitmap_from_image(const FzImage& img, const FzHalftone& ht);

	/** Class-aware wrapper for `::fz_new_bitmap_from_pixmap()`.  */
	/**
		Make a bitmap from a pixmap and a halftone.
	
		pix: The pixmap to generate from. Currently must be a single
		color component with no alpha.
	
		ht: The halftone to use. NULL implies the default halftone.
	
		Returns the resultant bitmap. Throws exceptions in the case of
		failure to allocate.
	*/
	FZ_FUNCTION FzBitmap fz_new_bitmap_from_pixmap(const FzPixmap& pix, const FzHalftone& ht);

	/** Class-aware wrapper for `::fz_new_bitmap_from_pixmap_band()`.  */
	/**
		Make a bitmap from a pixmap and a
		halftone, allowing for the position of the pixmap within an
		overall banded rendering.
	
		pix: The pixmap to generate from. Currently must be a single
		color component with no alpha.
	
		ht: The halftone to use. NULL implies the default halftone.
	
		band_start: Vertical offset within the overall banded rendering
		(in pixels)
	
		Returns the resultant bitmap. Throws exceptions in the case of
		failure to allocate.
	*/
	FZ_FUNCTION FzBitmap fz_new_bitmap_from_pixmap_band(const FzPixmap& pix, const FzHalftone& ht, int band_start);

	/** Class-aware wrapper for `::fz_new_brotli_data()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_brotli_data(const unsigned char *source, size_t source_length, ::fz_brotli_level level)` => `(unsigned char *, size_t compressed_length)`
	 */
	/**
		Compress source_length bytes of data starting
		at source, into a new memory block malloced for that purpose.
		*compressed_length is updated on exit to contain the size used.
		Ownership of the block is returned from this function, and the
		caller is therefore responsible for freeing it. The block may be
		considerably larger than is actually required. The caller is
		free to fz_realloc it down if it wants to.
	*/
	FZ_FUNCTION unsigned char *fz_new_brotli_data(size_t *compressed_length, const unsigned char *source, size_t source_length, ::fz_brotli_level level);

	/** Class-aware wrapper for `::fz_new_brotli_data_from_buffer()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_brotli_data_from_buffer(::fz_buffer *buffer, ::fz_brotli_level level)` => `(unsigned char *, size_t compressed_length)`
	 */
	/**
		Compress the contents of a fz_buffer into a
		new block malloced for that purpose. *compressed_length is
		updated on exit to contain the size used. Ownership of the block
		is returned from this function, and the caller is therefore
		responsible for freeing it. The block may be considerably larger
		than is actually required. The caller is free to fz_realloc it
		down if it wants to.
	*/
	FZ_FUNCTION unsigned char *fz_new_brotli_data_from_buffer(size_t *compressed_length, const FzBuffer& buffer, ::fz_brotli_level level);

	/** Class-aware wrapper for `::fz_new_buffer()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer(size_t capacity);

	/** Class-aware wrapper for `::fz_new_buffer_from_base64()`.  */
	/**
		Create a new buffer with data decoded from a base64 input string.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_base64(const char *data, size_t size);

	/** Class-aware wrapper for `::fz_new_buffer_from_copied_data()`.  */
	/**
		Create a new buffer containing a copy of the passed data.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_copied_data(const unsigned char *data, size_t size);

	/** Class-aware wrapper for `::fz_new_buffer_from_data()`.  */
	/**
		Create a new buffer with existing data.
	
		data: Pointer to existing data.
		size: Size of existing data.
	
		Takes ownership of data. Does not make a copy. Calls fz_free on
		the data when the buffer is deallocated. Do not use 'data' after
		passing to this function.
	
		Returns pointer to new buffer. Throws exception on allocation
		failure.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_data(unsigned char *data, size_t size);

	/** Class-aware wrapper for `::fz_new_buffer_from_display_list()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_display_list(const FzDisplayList& list, FzStextOptions& options);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_jpeg()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_jpeg(const FzImage& image, const FzColorParams& color_params, int quality, int invert_cmyk);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_jpx()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_jpx(const FzImage& image, const FzColorParams& color_params, int quality);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_pam()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_pam(const FzImage& image, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_pbm()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_pbm(const FzImage& image, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_pkm()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_pkm(const FzImage& image, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_png()`.  */
	/**
		Re-encode a given image as a PNG into a buffer.
	
		Ownership of the buffer is returned.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_png(const FzImage& image, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_pnm()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_pnm(const FzImage& image, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_image_as_psd()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_image_as_psd(const FzImage& image, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_page()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_page(const FzPage& page, FzStextOptions& options);

	/** Class-aware wrapper for `::fz_new_buffer_from_page_number()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_page_number(const FzDocument& doc, int number, FzStextOptions& options);

	/** Class-aware wrapper for `::fz_new_buffer_from_page_with_format()`.  */
	/**
		Returns an fz_buffer containing a page after conversion to specified format.
	
		page: The page to convert.
		format, options: Passed to fz_new_document_writer_with_output() internally.
		transform, cookie: Passed to fz_run_page() internally.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_page_with_format(const FzPage& page, const char *format, const char *options, const FzMatrix& transform, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_jpeg()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_jpeg(const FzPixmap& pixmap, const FzColorParams& color_params, int quality, int invert_cmyk);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_jpx()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_jpx(const FzPixmap& pix, const FzColorParams& color_params, int quality);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_pam()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_pam(const FzPixmap& pixmap, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_pbm()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_pbm(const FzPixmap& pixmap, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_pkm()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_pkm(const FzPixmap& pixmap, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_png()`.  */
	/**
		Re-encode a given pixmap as a PNG into a buffer.
	
		Ownership of the buffer is returned.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_png(const FzPixmap& pixmap, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_pnm()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_pnm(const FzPixmap& pixmap, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_pixmap_as_psd()`.  */
	FZ_FUNCTION FzBuffer fz_new_buffer_from_pixmap_as_psd(const FzPixmap& pix, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_new_buffer_from_shared_data()`.  */
	/**
		Like fz_new_buffer, but does not take ownership.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_shared_data(const unsigned char *data, size_t size);

	/** Class-aware wrapper for `::fz_new_buffer_from_stext_page()`.  */
	/**
		Convert structured text into plain text.
	*/
	FZ_FUNCTION FzBuffer fz_new_buffer_from_stext_page(const FzStextPage& text);

	/** Class-aware wrapper for `::fz_new_builtin_font()`.  */
	FZ_FUNCTION FzFont fz_new_builtin_font(const char *name, int is_bold, int is_italic);

	/** Class-aware wrapper for `::fz_new_cal_gray_colorspace()`.  */
	/**
		Create a calibrated gray colorspace.
	
		The returned reference should be dropped when it is finished
		with.
	
		Colorspaces are immutable once created.
	*/
	FZ_FUNCTION FzColorspace fz_new_cal_gray_colorspace(float wp[3], float bp[3], float gamma);

	/** Class-aware wrapper for `::fz_new_cal_rgb_colorspace()`.  */
	/**
		Create a calibrated rgb colorspace.
	
		The returned reference should be dropped when it is finished
		with.
	
		Colorspaces are immutable once created.
	*/
	FZ_FUNCTION FzColorspace fz_new_cal_rgb_colorspace(float wp[3], float bp[3], float gamma[3], float matrix[9]);

	/* Class-aware wrapper for `fz_new_cbz_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_cbz_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_cjk_font()`.  */
	FZ_FUNCTION FzFont fz_new_cjk_font(int ordering);

	/* Class-aware wrapper for `fz_new_color_pcl_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_colorspace()`.  */
	/**
		Creates a new colorspace instance and returns a reference.
	
		No internal checking is done that the colorspace type (e.g.
		CMYK) matches with the flags (e.g. FZ_COLORSPACE_HAS_CMYK) or
		colorant count (n) or name.
	
		The reference should be dropped when it is finished with.
	
		Colorspaces are immutable once created (with the exception of
		setting up colorant names for separation spaces).
	*/
	FZ_FUNCTION FzColorspace fz_new_colorspace(enum fz_colorspace_type type, int flags, int n, const char *name);

	/** Class-aware wrapper for `::fz_new_compressed_buffer()`.  */
	/**
		Create a new, UNKNOWN format, compressed_buffer.
	*/
	FZ_FUNCTION FzCompressedBuffer fz_new_compressed_buffer();

	/* Class-aware wrapper for `fz_new_context_imp()`
	is not available because returned wrapper class for `fz_context`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_csv_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_csv_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_default_colorspaces()`.  */
	/**
		Create a new default colorspace structure with values inherited
		from the context, and return a reference to it.
	
		These can be overridden using fz_set_default_xxxx.
	
		These should not be overridden while more than one caller has
		the reference for fear of race conditions.
	
		The caller should drop this reference once finished with it.
	*/
	FZ_FUNCTION FzDefaultColorspaces fz_new_default_colorspaces();

	/* Class-aware wrapper for `fz_new_deflate_output()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_deflated_data()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_deflated_data(const unsigned char *source, size_t source_length, ::fz_deflate_level level)` => `(unsigned char *, size_t compressed_length)`
	 */
	/**
		Compress source_length bytes of data starting
		at source, into a new memory block malloced for that purpose.
		*compressed_length is updated on exit to contain the size used.
		Ownership of the block is returned from this function, and the
		caller is therefore responsible for freeing it. The block may be
		considerably larger than is actually required. The caller is
		free to fz_realloc it down if it wants to.
	*/
	FZ_FUNCTION unsigned char *fz_new_deflated_data(size_t *compressed_length, const unsigned char *source, size_t source_length, ::fz_deflate_level level);

	/** Class-aware wrapper for `::fz_new_deflated_data_from_buffer()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_deflated_data_from_buffer(::fz_buffer *buffer, ::fz_deflate_level level)` => `(unsigned char *, size_t compressed_length)`
	 */
	/**
		Compress the contents of a fz_buffer into a
		new block malloced for that purpose. *compressed_length is
		updated on exit to contain the size used. Ownership of the block
		is returned from this function, and the caller is therefore
		responsible for freeing it. The block may be considerably larger
		than is actually required. The caller is free to fz_realloc it
		down if it wants to.
	*/
	FZ_FUNCTION unsigned char *fz_new_deflated_data_from_buffer(size_t *compressed_length, const FzBuffer& buffer, ::fz_deflate_level level);

	/** Class-aware wrapper for `::fz_new_device_of_size()`.  */
	/**
		Devices are created by calls to device implementations, for
		instance: foo_new_device(). These will be implemented by calling
		fz_new_derived_device(ctx, foo_device) where foo_device is a
		structure "derived from" fz_device, for instance
		typedef struct { fz_device base;  ...extras...} foo_device;
	*/
	FZ_FUNCTION FzDevice fz_new_device_of_size(int size);

	/** Class-aware wrapper for `::fz_new_display_list()`.  */
	/**
		Create an empty display list.
	
		A display list contains drawing commands (text, images, etc.).
		Use fz_new_list_device for populating the list.
	
		mediabox: Bounds of the page (in points) represented by the
		display list.
	*/
	FZ_FUNCTION FzDisplayList fz_new_display_list(const FzRect& mediabox);

	/** Class-aware wrapper for `::fz_new_display_list_from_page()`.  */
	/**
		Create a display list.
	
		Ownership of the display list is returned to the caller.
	*/
	FZ_FUNCTION FzDisplayList fz_new_display_list_from_page(const FzPage& page);

	/** Class-aware wrapper for `::fz_new_display_list_from_page_contents()`.  */
	/**
		Create a display list from page contents (no annotations).
	
		Ownership of the display list is returned to the caller.
	*/
	FZ_FUNCTION FzDisplayList fz_new_display_list_from_page_contents(const FzPage& page);

	/** Class-aware wrapper for `::fz_new_display_list_from_page_number()`.  */
	FZ_FUNCTION FzDisplayList fz_new_display_list_from_page_number(const FzDocument& doc, int number);

	/** Class-aware wrapper for `::fz_new_display_list_from_svg()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_display_list_from_svg(::fz_buffer *buf, const char *base_uri, ::fz_archive *dir)` => `(fz_display_list *, float w, float h)`
	 */
	/**
		Parse an SVG document into a display-list.
	*/
	FZ_FUNCTION FzDisplayList fz_new_display_list_from_svg(const FzBuffer& buf, const char *base_uri, const FzArchive& dir, float *w, float *h);

	/** Class-aware wrapper for `::fz_new_display_list_from_svg_xml()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_display_list_from_svg_xml(::fz_xml_doc *xmldoc, ::fz_xml *xml, const char *base_uri, ::fz_archive *dir)` => `(fz_display_list *, float w, float h)`
	 */
	/**
		Parse an SVG document into a display-list.
	*/
	FZ_FUNCTION FzDisplayList fz_new_display_list_from_svg_xml(const FzXml& xmldoc, const FzXml& xml, const char *base_uri, const FzArchive& dir, float *w, float *h);

	/** Class-aware wrapper for `::fz_new_document_of_size()`.  */
	/**
		New documents are typically created by calls like
		foo_new_document(fz_context *ctx, ...). These work by
		deriving a new document type from fz_document, for instance:
		typedef struct { fz_document base; ...extras... } foo_document;
		These are allocated by calling
		fz_new_derived_document(ctx, foo_document)
	*/
	FZ_FUNCTION void *fz_new_document_of_size(int size);

	/* Class-aware wrapper for `fz_new_document_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_document_writer_of_size()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_document_writer_with_buffer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_document_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_docx_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_docx_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_dom()`.  */
	/**
		Make new xml dom root element.
	*/
	FZ_FUNCTION FzXml fz_new_dom(const char *tag);

	/** Class-aware wrapper for `::fz_new_dom_node()`.  */
	/**
		Create a new dom node.
	
		This will NOT be linked in yet.
	*/
	FZ_FUNCTION FzXml fz_new_dom_node(const FzXml& dom, const char *tag);

	/** Class-aware wrapper for `::fz_new_dom_text_node()`.  */
	/**
		Create a new dom text node.
	
		This will NOT be linked in yet.
	*/
	FZ_FUNCTION FzXml fz_new_dom_text_node(const FzXml& dom, const char *text);

	/** Class-aware wrapper for `::fz_new_draw_device()`.  */
	/**
		Create a device to draw on a pixmap.
	
		dest: Target pixmap for the draw device. See fz_new_pixmap*
		for how to obtain a pixmap. The pixmap is not cleared by the
		draw device, see fz_clear_pixmap* for how to clear it prior to
		calling fz_new_draw_device. Free the device by calling
		fz_drop_device.
	
		transform: Transform from user space in points to device space
		in pixels.
	*/
	FZ_FUNCTION FzDevice fz_new_draw_device(const FzMatrix& transform, const FzPixmap& dest);

	/** Class-aware wrapper for `::fz_new_draw_device_type3()`.  */
	FZ_FUNCTION FzDevice fz_new_draw_device_type3(const FzMatrix& transform, const FzPixmap& dest);

	/** Class-aware wrapper for `::fz_new_draw_device_with_bbox()`.  */
	/**
		Create a device to draw on a pixmap.
	
		dest: Target pixmap for the draw device. See fz_new_pixmap*
		for how to obtain a pixmap. The pixmap is not cleared by the
		draw device, see fz_clear_pixmap* for how to clear it prior to
		calling fz_new_draw_device. Free the device by calling
		fz_drop_device.
	
		transform: Transform from user space in points to device space
		in pixels.
	
		clip: Bounding box to restrict any marking operations of the
		draw device.
	*/
	FZ_FUNCTION FzDevice fz_new_draw_device_with_bbox(const FzMatrix& transform, const FzPixmap& dest, FzIrect& clip);

	/** Class-aware wrapper for `::fz_new_draw_device_with_bbox_proof()`.  */
	/**
		Create a device to draw on a pixmap.
	
		dest: Target pixmap for the draw device. See fz_new_pixmap*
		for how to obtain a pixmap. The pixmap is not cleared by the
		draw device, see fz_clear_pixmap* for how to clear it prior to
		calling fz_new_draw_device. Free the device by calling
		fz_drop_device.
	
		transform: Transform from user space in points to device space
		in pixels.
	
		clip: Bounding box to restrict any marking operations of the
		draw device.
	
		proof_cs: Color space to render to prior to mapping to color
		space defined by pixmap.
	*/
	FZ_FUNCTION FzDevice fz_new_draw_device_with_bbox_proof(const FzMatrix& transform, const FzPixmap& dest, FzIrect& clip, const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_new_draw_device_with_options()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_draw_device_with_options(const ::fz_draw_options *options, ::fz_rect mediabox, ::fz_pixmap **pixmap)` => `(fz_device *)`
	 */
	/**
		Create a new pixmap and draw device, using the specified options.
	
		options: Options to configure the draw device, and choose the
		resolution and colorspace.
	
		mediabox: The bounds of the page in points.
	
		pixmap: An out parameter containing the newly created pixmap.
	*/
	FZ_FUNCTION FzDevice fz_new_draw_device_with_options(FzDrawOptions& options, const FzRect& mediabox, FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_new_draw_device_with_proof()`.  */
	/**
		Create a device to draw on a pixmap.
	
		dest: Target pixmap for the draw device. See fz_new_pixmap*
		for how to obtain a pixmap. The pixmap is not cleared by the
		draw device, see fz_clear_pixmap* for how to clear it prior to
		calling fz_new_draw_device. Free the device by calling
		fz_drop_device.
	
		transform: Transform from user space in points to device space
		in pixels.
	
		proof_cs: Intermediate color space to map though when mapping to
		color space defined by pixmap.
	*/
	FZ_FUNCTION FzDevice fz_new_draw_device_with_proof(const FzMatrix& transform, const FzPixmap& dest, const FzColorspace& proof_cs);

	/** Class-aware wrapper for `::fz_new_font_from_buffer()`.  */
	/**
		Create a new font from a font file in a fz_buffer.
	
		Fonts created in this way, will be eligible for embedding by default.
	
		name: Name of font (leave NULL to use name from font).
	
		buffer: Buffer to load from.
	
		index: Which font from the file to load (0 for default).
	
		use_glyph_box: 1 if we should use the glyph bbox, 0 otherwise.
	
		Returns new font handle, or throws exception on error.
	*/
	FZ_FUNCTION FzFont fz_new_font_from_buffer(const char *name, const FzBuffer& buffer, int index, int use_glyph_bbox);

	/** Class-aware wrapper for `::fz_new_font_from_file()`.  */
	/**
		Create a new font from a font file.
	
		Fonts created in this way, will be eligible for embedding by default.
	
		name: Name of font (leave NULL to use name from font).
	
		path: File path to load from.
	
		index: Which font from the file to load (0 for default).
	
		use_glyph_box: 1 if we should use the glyph bbox, 0 otherwise.
	
		Returns new font handle, or throws exception on error.
	*/
	FZ_FUNCTION FzFont fz_new_font_from_file(const char *name, const char *path, int index, int use_glyph_bbox);

	/** Class-aware wrapper for `::fz_new_font_from_memory()`.  */
	/**
		Create a new font from a font file in memory.
	
		Fonts created in this way, will be eligible for embedding by default.
	
		name: Name of font (leave NULL to use name from font).
	
		data: Pointer to the font file data.
	
		len: Length of the font file data.
	
		index: Which font from the file to load (0 for default).
	
		use_glyph_box: 1 if we should use the glyph bbox, 0 otherwise.
	
		Returns new font handle, or throws exception on error.
	*/
	FZ_FUNCTION FzFont fz_new_font_from_memory(const char *name, const unsigned char *data, int len, int index, int use_glyph_bbox);

	/** Class-aware wrapper for `::fz_new_function_of_size()`.  */
	FZ_FUNCTION FzFunction fz_new_function_of_size(int size, size_t size2, int m, int n, ::fz_function_eval_fn *eval, ::fz_store_drop_fn *drop);

	/* Class-aware wrapper for `fz_new_hash_table()`
	is not available because returned wrapper class for `fz_hash_table`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_icc_colorspace()`.  */
	/**
		Create a colorspace from an ICC profile supplied in buf.
	
		Limited checking is done to ensure that the colorspace type is
		appropriate for the supplied ICC profile.
	
		An additional reference is taken to buf, which will be dropped
		on destruction. Ownership is NOT passed in.
	
		The returned reference should be dropped when it is finished
		with.
	
		Colorspaces are immutable once created.
	*/
	FZ_FUNCTION FzColorspace fz_new_icc_colorspace(enum fz_colorspace_type type, int flags, const char *name, const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_new_image_from_buffer()`.  */
	/**
		Create a new image from a
		buffer of data, inferring its type from the format
		of the data.
	*/
	FZ_FUNCTION FzImage fz_new_image_from_buffer(const FzBuffer& buffer);

	/** Class-aware wrapper for `::fz_new_image_from_compressed_buffer()`.  */
	/**
		Create an image based on
		the data in the supplied compressed buffer.
	
		w,h: Width and height of the created image.
	
		bpc: Bits per component.
	
		colorspace: The colorspace (determines the number of components,
		and any color conversions required while decoding).
	
		xres, yres: The X and Y resolutions respectively.
	
		interpolate: 1 if interpolation should be used when decoding
		this image, 0 otherwise.
	
		imagemask: 1 if this is an imagemask (i.e. transparency bitmap
		mask), 0 otherwise.
	
		decode: NULL, or a pointer to to a decode array. The default
		decode array is [0 1] (repeated n times, for n color components).
	
		colorkey: NULL, or a pointer to a colorkey array. The default
		colorkey array is [0 255] (repeated n times, for n color
		components).
	
		buffer: Buffer of compressed data and compression parameters.
		Ownership of this reference is passed in.
	
		mask: NULL, or another image to use as a mask for this one.
		A new reference is taken to this image. Supplying a masked
		image as a mask to another image is illegal!
	*/
	FZ_FUNCTION FzImage fz_new_image_from_compressed_buffer(int w, int h, int bpc, const FzColorspace& colorspace, int xres, int yres, int interpolate, int imagemask, const float *decode, const int *colorkey, const FzCompressedBuffer& buffer, const FzImage& mask);

	/** Class-aware wrapper for `::fz_new_image_from_compressed_buffer2()`.  */
	/** Swig-friendly wrapper for fz_new_image_from_compressed_buffer(),
	uses specified `decode` and `colorkey` if they are not null (in which
	case we assert that they have size `2*fz_colorspace_n(colorspace)`). */
	FZ_FUNCTION FzImage fz_new_image_from_compressed_buffer2(int w, int h, int bpc, const FzColorspace& colorspace, int xres, int yres, int interpolate, int imagemask, const std::vector<float> &decode, const std::vector<int> &colorkey, const FzCompressedBuffer& buffer, const FzImage& mask);

	/** Class-aware wrapper for `::fz_new_image_from_display_list()`.  */
	/**
		Create a new image from a display list.
	
		w, h: The conceptual width/height of the image.
	
		transform: The matrix that needs to be applied to the given
		list to make it render to the unit square.
	
		list: The display list.
	*/
	FZ_FUNCTION FzImage fz_new_image_from_display_list(float w, float h, const FzDisplayList& list);

	/** Class-aware wrapper for `::fz_new_image_from_file()`.  */
	/**
		Create a new image from the contents
		of a file, inferring its type from the format of the
		data.
	*/
	FZ_FUNCTION FzImage fz_new_image_from_file(const char *path);

	/** Class-aware wrapper for `::fz_new_image_from_pixmap()`.  */
	/**
		Create an image from the given
		pixmap.
	
		pixmap: The pixmap to base the image upon. A new reference
		to this is taken.
	
		mask: NULL, or another image to use as a mask for this one.
		A new reference is taken to this image. Supplying a masked
		image as a mask to another image is illegal!
	*/
	FZ_FUNCTION FzImage fz_new_image_from_pixmap(const FzPixmap& pixmap, const FzImage& mask);

	/** Class-aware wrapper for `::fz_new_image_from_svg()`.  */
	/**
		Create a scalable image from an SVG document.
	*/
	FZ_FUNCTION FzImage fz_new_image_from_svg(const FzBuffer& buf, const char *base_uri, const FzArchive& dir);

	/** Class-aware wrapper for `::fz_new_image_from_svg_xml()`.  */
	/**
		Create a scalable image from an SVG document.
	*/
	FZ_FUNCTION FzImage fz_new_image_from_svg_xml(const FzXml& xmldoc, const FzXml& xml, const char *base_uri, const FzArchive& dir);

	/** Class-aware wrapper for `::fz_new_image_of_size()`.  */
	/**
		Internal function to make a new fz_image structure
		for a derived class.
	
		w,h: Width and height of the created image.
	
		bpc: Bits per component.
	
		colorspace: The colorspace (determines the number of components,
		and any color conversions required while decoding).
	
		xres, yres: The X and Y resolutions respectively.
	
		interpolate: 1 if interpolation should be used when decoding
		this image, 0 otherwise.
	
		imagemask: 1 if this is an imagemask (i.e. transparent), 0
		otherwise.
	
		decode: NULL, or a pointer to to a decode array. The default
		decode array is [0 1] (repeated n times, for n color components).
	
		colorkey: NULL, or a pointer to a colorkey array. The default
		colorkey array is [0 255] (repeated n times, for n color
		components).
	
		mask: NULL, or another image to use as a mask for this one.
		A new reference is taken to this image. Supplying a masked
		image as a mask to another image is illegal!
	
		size: The size of the required allocated structure (the size of
		the derived structure).
	
		get: The function to be called to obtain a decoded pixmap.
	
		get_size: The function to be called to return the storage size
		used by this image.
	
		drop: The function to be called to dispose of this image once
		the last reference is dropped.
	
		Returns a pointer to an allocated structure of the required size,
		with the first sizeof(fz_image) bytes initialised as appropriate
		given the supplied parameters, and the other bytes set to zero.
	*/
	FZ_FUNCTION FzImage fz_new_image_of_size(int w, int h, int bpc, const FzColorspace& colorspace, int xres, int yres, int interpolate, int imagemask, const float *decode, const int *colorkey, const FzImage& mask, size_t size, ::fz_image_get_pixmap_fn *get_pixmap, ::fz_image_get_size_fn *get_size, ::fz_drop_image_fn *drop);

	/** Class-aware wrapper for `::fz_new_indexed_colorspace()`.  */
	/**
		Create an indexed colorspace.
	
		The supplied lookup table is high palette entries long. Each
		entry is n bytes long, where n is given by the number of
		colorants in the base colorspace, one byte per colorant.
	
		Ownership of lookup is passed it; it will be freed on
		destruction, so must be heap allocated.
	
		The colorspace will keep an additional reference to the base
		colorspace that will be dropped on destruction.
	
		The returned reference should be dropped when it is finished
		with.
	
		Colorspaces are immutable once created.
	*/
	FZ_FUNCTION FzColorspace fz_new_indexed_colorspace(const FzColorspace& base, int high, unsigned char *lookup);

	/* Class-aware wrapper for `fz_new_jpeg_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_layout()`
	is not available because returned wrapper class for `fz_layout_block`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_link_of_size()`.  */
	/**
		Create a new link record.
	
		next is set to NULL with the expectation that the caller will
		handle the linked list setup. Internal function.
	
		Different document types will be implemented by deriving from
		fz_link. This macro allocates such derived structures, and
		initialises the base sections.
	*/
	FZ_FUNCTION FzLink fz_new_link_of_size(int size, const FzRect& rect, const char *uri);

	/** Class-aware wrapper for `::fz_new_list_device()`.  */
	/**
		Create a rendering device for a display list.
	
		When the device is rendering a page it will populate the
		display list with drawing commands (text, images, etc.). The
		display list can later be reused to render a page many times
		without having to re-interpret the page from the document file
		for each rendering. Once the device is no longer needed, free
		it with fz_drop_device.
	
		list: A display list that the list device takes a reference to.
	*/
	FZ_FUNCTION FzDevice fz_new_list_device(const FzDisplayList& list);

	/* Class-aware wrapper for `fz_new_log_for_module()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_mono_pcl_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_mono_pwg_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_multi_archive()`.  */
	/**
		Create a new multi archive (initially empty).
	*/
	FZ_FUNCTION FzArchive fz_new_multi_archive();

	/** Class-aware wrapper for `::fz_new_ocr_device()`.  */
	/**
		Create a device to OCR the text on the page.
	
		Renders the page internally to a bitmap that is then OCRd. Text
		is then forwarded onto the target device.
	
		target: The target device to receive the OCRd text.
	
		ctm: The transform to apply to the mediabox to get the size for
		the rendered page image. Also used to calculate the resolution
		for the page image. In general, this will be the same as the CTM
		that you pass to fz_run_page (or fz_run_display_list) to feed
		this device.
	
		mediabox: The mediabox (in points). Combined with the CTM to get
		the bounds of the pixmap used internally for the rendered page
		image.
	
		with_list: If with_list is false, then all non-text operations
		are forwarded instantly to the target device. This results in
		the target device seeing all NON-text operations, followed by
		all the text operations (derived from OCR).
	
		If with_list is true, then all the marking operations are
		collated into a display list which is then replayed to the
		target device at the end.
	
		language: NULL (for "eng"), or a pointer to a string to describe
		the languages/scripts that should be used for OCR (e.g.
		"eng,ara").
	
		datadir: NULL (for ""), or a pointer to a path string otherwise
		provided to Tesseract in the TESSDATA_PREFIX environment variable.
	
		progress: NULL, or function to be called periodically to indicate
		progress. Return 0 to continue, or 1 to cancel. progress_arg is
		returned as the void *. The int is a value between 0 and 100 to
		indicate progress.
	
		progress_arg: A void * value to be parrotted back to the progress
		function.
	*/
	FZ_FUNCTION FzDevice fz_new_ocr_device(const FzDevice& target, const FzMatrix& ctm, const FzRect& mediabox, int with_list, const char *language, const char *datadir, int (*progress)(::fz_context *, void *, int ), void *progress_arg);

	/* Class-aware wrapper for `fz_new_odt_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_odt_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_outline()`.  */
	/**
		Create a new outline entry with zeroed fields for the caller
		to fill in.
	*/
	FZ_FUNCTION FzOutline fz_new_outline();

	/* Class-aware wrapper for `fz_new_outline_iterator()`
	is not available because returned wrapper class for `fz_outline_iterator`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_outline_iterator_of_size()`
	is not available because returned wrapper class for `fz_outline_iterator`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_output()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_output_with_buffer()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_output_with_file_ptr()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_output_with_path()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_page_of_size()`.  */
	/**
		Different document types will be implemented by deriving from
		fz_page. This macro allocates such derived structures, and
		initialises the base sections.
	*/
	FZ_FUNCTION FzPage fz_new_page_of_size(int size, const FzDocument& doc);

	/* Class-aware wrapper for `fz_new_pam_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pam_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_path()`.  */
	/**
		Create a new (empty) path structure.
	*/
	FZ_FUNCTION FzPath fz_new_path();

	/* Class-aware wrapper for `fz_new_pbm_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pbm_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pcl_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pcl_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pclm_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pclm_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pclm_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_pdf_document_from_fz_document()`.  */
	FZ_FUNCTION PdfDocument fz_new_pdf_document_from_fz_document(const FzDocument& ptr);

	/* Class-aware wrapper for `fz_new_pdf_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pdf_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pdfocr_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pdfocr_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pdfocr_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pgm_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_pixmap()`.  */
	/**
		Create a new pixmap, with its origin at (0,0)
	
		cs: The colorspace to use for the pixmap, or NULL for an alpha
		plane/mask.
	
		w: The width of the pixmap (in pixels)
	
		h: The height of the pixmap (in pixels)
	
		seps: Details of separations.
	
		alpha: 0 for no alpha, 1 for alpha.
	
		Returns a pointer to the new pixmap. Throws exception on failure
		to allocate.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap(const FzColorspace& cs, int w, int h, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_alpha_channel()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_alpha_channel(const FzPixmap& src);

	/** Class-aware wrapper for `::fz_new_pixmap_from_color_and_mask()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_color_and_mask(const FzPixmap& color, const FzPixmap& mask);

	/** Class-aware wrapper for `::fz_new_pixmap_from_display_list()`.  */
	/**
		Render the page to a pixmap using the transform and colorspace.
	
		Ownership of the pixmap is returned to the caller.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_display_list(const FzDisplayList& list, const FzMatrix& ctm, const FzColorspace& cs, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_display_list_with_separations()`.  */
	/**
		Render the page contents with control over spot colors.
	
		Ownership of the pixmap is returned to the caller.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_display_list_with_separations(const FzDisplayList& list, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_page()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_page(const FzPage& page, const FzMatrix& ctm, const FzColorspace& cs, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_page_contents()`.  */
	/**
		Render the page contents without annotations.
	
		Ownership of the pixmap is returned to the caller.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_page_contents(const FzPage& page, const FzMatrix& ctm, const FzColorspace& cs, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_page_contents_with_separations()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_page_contents_with_separations(const FzPage& page, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_page_number()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_page_number(const FzDocument& doc, int number, const FzMatrix& ctm, const FzColorspace& cs, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_page_number_with_separations()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_page_number_with_separations(const FzDocument& doc, int number, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_page_with_separations()`.  */
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_page_with_separations(const FzPage& page, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_from_pixmap()`.  */
	/**
		Create a new pixmap that represents a subarea of the specified
		pixmap. A reference is taken to this pixmap that will be dropped
		on destruction.
	
		The supplied rectangle must be wholly contained within the
		original pixmap.
	
		Returns a pointer to the new pixmap. Throws exception on failure
		to allocate.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_from_pixmap(const FzPixmap& pixmap, FzIrect& rect);

	/** Class-aware wrapper for `::fz_new_pixmap_with_bbox()`.  */
	/**
		Create a pixmap of a given size, location and pixel format.
	
		The bounding box specifies the size of the created pixmap and
		where it will be located. The colorspace determines the number
		of components per pixel. Alpha is always present. Pixmaps are
		reference counted, so drop references using fz_drop_pixmap.
	
		colorspace: Colorspace format used for the created pixmap. The
		pixmap will keep a reference to the colorspace.
	
		bbox: Bounding box specifying location/size of created pixmap.
	
		seps: Details of separations.
	
		alpha: 0 for no alpha, 1 for alpha.
	
		Returns a pointer to the new pixmap. Throws exception on failure
		to allocate.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_with_bbox(const FzColorspace& colorspace, const FzIrect& bbox, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::fz_new_pixmap_with_bbox_and_data()`.  */
	/**
		Create a pixmap of a given size, location and pixel format,
		using the supplied data block.
	
		The bounding box specifies the size of the created pixmap and
		where it will be located. The colorspace determines the number
		of components per pixel. Alpha is always present. Pixmaps are
		reference counted, so drop references using fz_drop_pixmap.
	
		colorspace: Colorspace format used for the created pixmap. The
		pixmap will keep a reference to the colorspace.
	
		rect: Bounding box specifying location/size of created pixmap.
	
		seps: Details of separations.
	
		alpha: Number of alpha planes (0 or 1).
	
		samples: The data block to keep the samples in.
	
		Returns a pointer to the new pixmap. Throws exception on failure
		to allocate.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_with_bbox_and_data(const FzColorspace& colorspace, const FzIrect& rect, const FzSeparations& seps, int alpha, unsigned char *samples);

	/** Class-aware wrapper for `::fz_new_pixmap_with_data()`.  */
	/**
		Create a new pixmap, with its origin at
		(0,0) using the supplied data block.
	
		cs: The colorspace to use for the pixmap, or NULL for an alpha
		plane/mask.
	
		w: The width of the pixmap (in pixels)
	
		h: The height of the pixmap (in pixels)
	
		seps: Details of separations.
	
		alpha: 0 for no alpha, 1 for alpha.
	
		stride: The byte offset from the pixel data in a row to the
		pixel data in the next row.
	
		samples: The data block to keep the samples in.
	
		Returns a pointer to the new pixmap. Throws exception on failure to
		allocate.
	*/
	FZ_FUNCTION FzPixmap fz_new_pixmap_with_data(const FzColorspace& colorspace, int w, int h, const FzSeparations& seps, int alpha, int stride, unsigned char *samples);

	/* Class-aware wrapper for `fz_new_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pkm_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pkm_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_png_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_png_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pnm_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pnm_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pool()`
	is not available because returned wrapper class for `fz_pool`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_ppm_pixmap_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_ps_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_ps_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_ps_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_psd_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pwg_band_writer()`
	is not available because returned wrapper class for `fz_band_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pwg_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_pwg_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_rle_output()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_separations()`.  */
	/**
		Create a new separations structure (initially empty)
	*/
	FZ_FUNCTION FzSeparations fz_new_separations(int controllable);

	/** Class-aware wrapper for `::fz_new_stext_device()`.  */
	/**
		Create a device to extract the text on a page.
	
		Gather the text on a page into blocks and lines.
	
		The reading order is taken from the order the text is drawn in
		the source file, so may not be accurate.
	
		page: The text page to which content should be added. This will
		usually be a newly created (empty) text page, but it can be one
		containing data already (for example when merging multiple
		pages, or watermarking).
	
		options: Options to configure the stext device.
	*/
	FZ_FUNCTION FzDevice fz_new_stext_device(const FzStextPage& page, FzStextOptions& options);

	/* Class-aware wrapper for `fz_new_stext_page()`
	is not available because returned wrapper class for `fz_stext_page`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_stext_page_from_chapter_page_number()`
	is not available because returned wrapper class for `fz_stext_page`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_stext_page_from_display_list()`
	is not available because returned wrapper class for `fz_stext_page`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_stext_page_from_page()`
	is not available because returned wrapper class for `fz_stext_page`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_stext_page_from_page_number()`
	is not available because returned wrapper class for `fz_stext_page`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_store_context()`.  */
	/**
		Create a new store inside the context
	
		max: The maximum size (in bytes) that the store is allowed to
		grow to. FZ_STORE_UNLIMITED means no limit.
	*/
	FZ_FUNCTION void fz_new_store_context(size_t max);

	/* Class-aware wrapper for `fz_new_story()`
	is not available because returned wrapper class for `fz_story`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_stream()`.  */
	/**
		Create a new stream object with the given
		internal state and function pointers.
	
		state: Internal state (opaque to everything but implementation).
	
		next: Should provide the next set of bytes (up to max) of stream
		data. Return the number of bytes read, or EOF when there is no
		more data.
	
		drop: Should clean up and free the internal state. May not
		throw exceptions.
	*/
	FZ_FUNCTION FzStream fz_new_stream(void *state, ::fz_stream_next_fn *next, ::fz_stream_drop_fn *drop);

	/** Class-aware wrapper for `::fz_new_string()`.  */
	FZ_FUNCTION FzString fz_new_string(const char *str);

	/** Class-aware wrapper for `::fz_new_stroke_state()`.  */
	/**
		Create a new (empty) stroke state structure (with no dash
		data) and return a reference to it.
	
		Throws exception on failure to allocate.
	*/
	FZ_FUNCTION FzStrokeState fz_new_stroke_state();

	/** Class-aware wrapper for `::fz_new_stroke_state_with_dash_len()`.  */
	/**
		Create a new (empty) stroke state structure, with room for
		dash data of the given length, and return a reference to it.
	
		len: The number of dash elements to allow room for.
	
		Throws exception on failure to allocate.
	*/
	FZ_FUNCTION FzStrokeState fz_new_stroke_state_with_dash_len(int len);

	/** Class-aware wrapper for `::fz_new_svg_device()`.  */
	/**
		Create a device that outputs (single page) SVG files to
		the given output stream.
	
		Equivalent to fz_new_svg_device_with_id passing id = NULL.
	*/
	FZ_FUNCTION FzDevice fz_new_svg_device(const FzOutput& out, float page_width, float page_height, int text_format, int reuse_images);

	/** Class-aware wrapper for `::fz_new_svg_device_with_id()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_svg_device_with_id(::fz_output *out, float page_width, float page_height, int text_format, int reuse_images)` => `(fz_device *, int id)`
	 */
	/**
		Create a device that outputs (single page) SVG files to
		the given output stream.
	
		output: The output stream to send the constructed SVG page to.
	
		page_width, page_height: The page dimensions to use (in points).
	
		text_format: How to emit text. One of the following values:
			FZ_SVG_TEXT_AS_TEXT: As <text> elements with possible
			layout errors and mismatching fonts.
			FZ_SVG_TEXT_AS_PATH: As <path> elements with exact
			visual appearance.
	
		reuse_images: Share image resources using <symbol> definitions.
	
		id: ID parameter to keep generated IDs unique across SVG files.
	*/
	FZ_FUNCTION FzDevice fz_new_svg_device_with_id(const FzOutput& out, float page_width, float page_height, int text_format, int reuse_images, int *id);

	/* Class-aware wrapper for `fz_new_svg_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_svg_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_test_device()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_new_test_device(float threshold, int options, ::fz_device *passthrough)` => `(fz_device *, int is_color)`
	 */
	/**
		Create a device to test for features.
	
		Currently only tests for the presence of non-grayscale colors.
	
		is_color: Possible values returned:
			0: Definitely greyscale
			1: Probably color (all colors were grey, but there
			were images or shadings in a non grey colorspace).
			2: Definitely color
	
		threshold: The difference from grayscale that will be tolerated.
		Typical values to use are either 0 (be exact) and 0.02 (allow an
		imperceptible amount of slop).
	
		options: A set of bitfield options, from the FZ_TEST_OPT set.
	
		passthrough: A device to pass all calls through to, or NULL.
		If set, then the test device can both test and pass through to
		an underlying device (like, say, the display list device). This
		means that a display list can be created and at the end we'll
		know if it's colored or not.
	
		In the absence of a passthrough device, the device will throw
		an exception to stop page interpretation when color is found.
	*/
	FZ_FUNCTION FzDevice fz_new_test_device(int *is_color, float threshold, int options, const FzDevice& passthrough);

	/** Class-aware wrapper for `::fz_new_text()`.  */
	/**
		Create a new empty fz_text object.
	
		Throws exception on failure to allocate.
	*/
	FZ_FUNCTION FzText fz_new_text();

	/* Class-aware wrapper for `fz_new_text_writer()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_text_writer_with_output()`
	is not available because returned wrapper class for `fz_document_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_new_trace_device()`.  */
	/**
		Create a device to print a debug trace of all device calls.
	*/
	FZ_FUNCTION FzDevice fz_new_trace_device(const FzOutput& out);

	/** Class-aware wrapper for `::fz_new_tree_archive()`.  */
	/**
		Create an archive that holds named buffers.
	
		tree can either be a preformed tree with fz_buffers as values,
		or it can be NULL for an empty tree.
	*/
	FZ_FUNCTION FzArchive fz_new_tree_archive(const FzTree& tree);

	/** Class-aware wrapper for `::fz_new_type3_font()`.  */
	/**
		Create a new (empty) type3 font.
	
		name: Name of font (or NULL).
	
		matrix: Font matrix.
	
		Returns a new font handle, or throws exception on
		allocation failure.
	*/
	FZ_FUNCTION FzFont fz_new_type3_font(const char *name, const FzMatrix& matrix);

	/** Class-aware wrapper for `::fz_new_xhtml_document_from_document()`.  */
	/**
		Use text extraction to convert the input document into XHTML,
		then open the result as a new document that can be reflowed.
	*/
	FZ_FUNCTION FzDocument fz_new_xhtml_document_from_document(const FzDocument& old_doc, FzStextOptions& opts);

	/** Class-aware wrapper for `::fz_new_xmltext_device()`.  */
	/**
		Create a device to output raw information.
	*/
	FZ_FUNCTION FzDevice fz_new_xmltext_device(const FzOutput& out);

	/* Class-aware wrapper for `fz_new_zip_writer()`
	is not available because returned wrapper class for `fz_zip_writer`
	is non-copyable. */

	/* Class-aware wrapper for `fz_new_zip_writer_with_output()`
	is not available because returned wrapper class for `fz_zip_writer`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_next_page()`.  */
	/**
		Function to get the location of the next page (allowing for the
		end of chapters etc). If at the end of the document, returns the
		current location.
	*/
	FZ_FUNCTION FzLocation fz_next_page(const FzDocument& doc, const FzLocation& loc);

	/** Class-aware wrapper for `::fz_normalize_vector()`.  */
	/**
		Normalize a vector to length one.
	*/
	FZ_FUNCTION FzPoint fz_normalize_vector(const FzPoint& p);

	/** Class-aware wrapper for `::fz_open_a85d()`.  */
	/**
		a85d filter performs ASCII 85 Decoding of data read
		from the chained filter.
	*/
	FZ_FUNCTION FzStream fz_open_a85d(const FzStream& chain);

	/** Class-aware wrapper for `::fz_open_accelerated_document()`.  */
	/**
		Open a document file and read its basic structure so pages and
		objects can be located. MuPDF will try to repair broken
		documents (without actually changing the file contents).
	
		The returned fz_document is used when calling most other
		document related functions.
	
		filename: a path to a file as it would be given to open(2).
	*/
	FZ_FUNCTION FzDocument fz_open_accelerated_document(const char *filename, const char *accel);

	/** Class-aware wrapper for `::fz_open_accelerated_document_with_stream()`.  */
	/**
		Open a document using the specified stream object rather than
		opening a file on disk.
	
		magic: a string used to detect document type; either a file name
		or mime-type.
	
		stream: a stream of the document contents.
	
		accel: NULL, or a stream of the 'accelerator' contents for this document.
	
		NOTE: The caller retains ownership of 'stream' and 'accel' - the document will
		take its own references if required.
	*/
	FZ_FUNCTION FzDocument fz_open_accelerated_document_with_stream(const char *magic, const FzStream& stream, const FzStream& accel);

	/** Class-aware wrapper for `::fz_open_accelerated_document_with_stream_and_dir()`.  */
	/**
		Open a document using the specified stream object rather than
		opening a file on disk.
	
		magic: a string used to detect document type; either a file name
		or mime-type.
	
		stream: a stream of the document contents.
	
		accel: NULL, or a stream of the 'accelerator' contents for this document.
	
		dir: NULL, or the 'directory context' for the stream contents.
	
		NOTE: The caller retains ownership of 'stream', 'accel' and 'dir' - the document will
		take its own references if required.
	*/
	FZ_FUNCTION FzDocument fz_open_accelerated_document_with_stream_and_dir(const char *magic, const FzStream& stream, const FzStream& accel, const FzArchive& dir);

	/** Class-aware wrapper for `::fz_open_aesd()`.  */
	/**
		aesd filter performs AES decoding of data read from the chained
		filter using the supplied key.
	*/
	FZ_FUNCTION FzStream fz_open_aesd(const FzStream& chain, unsigned char *key, unsigned int keylen);

	/** Class-aware wrapper for `::fz_open_ahxd()`.  */
	/**
		ahxd filter performs ASCII Hex decoding of data read
		from the chained filter.
	*/
	FZ_FUNCTION FzStream fz_open_ahxd(const FzStream& chain);

	/** Class-aware wrapper for `::fz_open_arc4()`.  */
	/**
		arc4 filter performs RC4 decoding of data read from the chained
		filter using the supplied key.
	*/
	FZ_FUNCTION FzStream fz_open_arc4(const FzStream& chain, unsigned char *key, unsigned int keylen);

	/** Class-aware wrapper for `::fz_open_archive()`.  */
	/**
		Open a zip or tar archive
	
		Open a file and identify its archive type based on the archive
		signature contained inside.
	
		filename: a path to a file as it would be given to open(2).
	*/
	FZ_FUNCTION FzArchive fz_open_archive(const char *filename);

	/** Class-aware wrapper for `::fz_open_archive_entry()`.  */
	/**
		Opens an archive entry as a stream.
	
		name: Entry name to look for, this must be an exact match to
		the entry name in the archive.
	
		Throws an exception if a matching entry cannot be found.
	*/
	FZ_FUNCTION FzStream fz_open_archive_entry(const FzArchive& arch, const char *name);

	/** Class-aware wrapper for `::fz_open_archive_with_stream()`.  */
	/**
		Open zip or tar archive stream.
	
		Open an archive using a seekable stream object rather than
		opening a file or directory on disk.
	*/
	FZ_FUNCTION FzArchive fz_open_archive_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::fz_open_brotlid()`.  */
	/**
		brotlid filter performs Brotli decoding of data read
		from the chained filter.
	*/
	FZ_FUNCTION FzStream fz_open_brotlid(const FzStream& chain);

	/** Class-aware wrapper for `::fz_open_buffer()`.  */
	/**
		Open a buffer as a stream.
	
		buf: The buffer to open. Ownership of the buffer is NOT passed
		in (this function takes its own reference).
	
		Returns pointer to newly created stream. May throw exceptions on
		failure to allocate.
	*/
	FZ_FUNCTION FzStream fz_open_buffer(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_open_cfb_archive()`.  */
	/**
		Open a cfb file as an archive.
	
		An exception is thrown if the file is not recognised as a cfb.
	
		filename: a path to an archive file as it would be given to
		open(2).
	*/
	FZ_FUNCTION FzArchive fz_open_cfb_archive(const char *filename);

	/** Class-aware wrapper for `::fz_open_cfb_archive_with_stream()`.  */
	/**
		Open a cfb file as an archive.
	
		Open an archive using a seekable stream object rather than
		opening a file or directory on disk.
	
		An exception is thrown if the file is not recognised as a chm.
	*/
	FZ_FUNCTION FzArchive fz_open_cfb_archive_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::fz_open_compressed_buffer()`.  */
	/**
		Open a stream to read the decompressed version of a buffer.
	*/
	FZ_FUNCTION FzStream fz_open_compressed_buffer(const FzCompressedBuffer& arg_0);

	/** Class-aware wrapper for `::fz_open_concat()`.  */
	/**
		Concat filter concatenates several streams into one.
	*/
	FZ_FUNCTION FzStream fz_open_concat(int max, int pad);

	/** Class-aware wrapper for `::fz_open_dctd()`.  */
	/**
		dctd filter performs DCT (JPEG) decoding of data read
		from the chained filter.
	
		color_transform implements the PDF color_transform option
			use -1 for default behavior
			use 0 to disable YUV-RGB / YCCK-CMYK transforms
			use 1 to enable YUV-RGB / YCCK-CMYK transforms
	
		invert_cmyk implements the necessary inversion for Photoshop CMYK images
			use 0 if embedded in PDF
			use 1 if not embedded in PDF
	
		For subsampling on decode, set l2factor to the log2 of the
		reduction required (therefore 0 = full size decode).
	
		jpegtables is an optional stream from which the JPEG tables
		can be read. Use NULL if not required.
	*/
	FZ_FUNCTION FzStream fz_open_dctd(const FzStream& chain, int color_transform, int invert_cmyk, int l2factor, const FzStream& jpegtables);

	/** Class-aware wrapper for `::fz_open_directory()`.  */
	/**
		Open a directory as if it was an archive.
	
		A special case where a directory is opened as if it was an
		archive.
	
		Note that for directories it is not possible to retrieve the
		number of entries or list the entries. It is however possible
		to check if the archive has a particular entry.
	
		path: a path to a directory as it would be given to opendir(3).
	*/
	FZ_FUNCTION FzArchive fz_open_directory(const char *path);

	/** Class-aware wrapper for `::fz_open_document()`.  */
	/**
		Open a document file and read its basic structure so pages and
		objects can be located. MuPDF will try to repair broken
		documents (without actually changing the file contents).
	
		The returned fz_document is used when calling most other
		document related functions.
	
		filename: a path to a file as it would be given to open(2).
	*/
	FZ_FUNCTION FzDocument fz_open_document(const char *filename);

	/** Class-aware wrapper for `::fz_open_document_with_buffer()`.  */
	/**
		Open a document using a buffer rather than opening a file on disk.
	*/
	FZ_FUNCTION FzDocument fz_open_document_with_buffer(const char *magic, const FzBuffer& buffer);

	/** Class-aware wrapper for `::fz_open_document_with_stream()`.  */
	/**
		Open a document using the specified stream object rather than
		opening a file on disk.
	
		magic: a string used to detect document type; either a file name
		or mime-type.
	
		stream: a stream representing the contents of the document file.
	
		NOTE: The caller retains ownership of 'stream' - the document will take its
		own reference if required.
	*/
	FZ_FUNCTION FzDocument fz_open_document_with_stream(const char *magic, const FzStream& stream);

	/** Class-aware wrapper for `::fz_open_document_with_stream_and_dir()`.  */
	/**
		Open a document using the specified stream object rather than
		opening a file on disk.
	
		magic: a string used to detect document type; either a file name
		or mime-type.
	
		stream: a stream representing the contents of the document file.
	
		dir: a 'directory context' for those filetypes that need it.
	
		NOTE: The caller retains ownership of 'stream' and 'dir' - the document will
		take its own references if required.
	*/
	FZ_FUNCTION FzDocument fz_open_document_with_stream_and_dir(const char *magic, const FzStream& stream, const FzArchive& dir);

	/** Class-aware wrapper for `::fz_open_endstream_filter()`.  */
	/**
		The endstream filter reads a PDF substream, and starts to look
		for an 'endstream' token after the specified length.
	*/
	FZ_FUNCTION FzStream fz_open_endstream_filter(const FzStream& chain, uint64_t len, int64_t offset);

	/** Class-aware wrapper for `::fz_open_faxd()`.  */
	/**
		faxd filter performs FAX decoding of data read from
		the chained filter.
	
		k: see fax specification (fax default is 0).
	
		end_of_line: whether we expect end of line markers (fax default
		is 0).
	
		encoded_byte_align: whether we align to bytes after each line
		(fax default is 0).
	
		columns: how many columns in the image (fax default is 1728).
	
		rows: 0 for unspecified or the number of rows of data to expect.
	
		end_of_block: whether we expect end of block markers (fax
		default is 1).
	
		black_is_1: determines the polarity of the image (fax default is
		0).
	*/
	FZ_FUNCTION FzStream fz_open_faxd(const FzStream& chain, int k, int end_of_line, int encoded_byte_align, int columns, int rows, int end_of_block, int black_is_1);

	/** Class-aware wrapper for `::fz_open_file()`.  */
	/**
		Open the named file and wrap it in a stream.
	
		filename: Path to a file. On non-Windows machines the filename
		should be exactly as it would be passed to fopen(2). On Windows
		machines, the path should be UTF-8 encoded so that non-ASCII
		characters can be represented. Other platforms do the encoding
		as standard anyway (and in most cases, particularly for MacOS
		and Linux, the encoding they use is UTF-8 anyway).
	*/
	FZ_FUNCTION FzStream fz_open_file(const char *filename);

	/** Class-aware wrapper for `::fz_open_file_autodelete()`.  */
	/**
		Do the same as fz_open_file, but delete the file upon close.
	*/
	FZ_FUNCTION FzStream fz_open_file_autodelete(const char *filename);

	/** Class-aware wrapper for `::fz_open_file_ptr_no_close()`.  */
	/**
		Create a stream from a FILE * that will not be closed
		when the stream is dropped.
	*/
	FZ_FUNCTION FzStream fz_open_file_ptr_no_close(FILE *file);

	/** Class-aware wrapper for `::fz_open_flated()`.  */
	/**
		flated filter performs LZ77 decoding (inflating) of data read
		from the chained filter.
	
		window_bits: How large a decompression window to use. Typically
		15. A negative number, -n, means to use n bits, but to expect
		raw data with no header.
	*/
	FZ_FUNCTION FzStream fz_open_flated(const FzStream& chain, int window_bits);

	/** Class-aware wrapper for `::fz_open_image_decomp_stream()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_open_image_decomp_stream(::fz_stream *arg_0, ::fz_compression_params *arg_1)` => `(fz_stream *, int l2factor)`
	 */
	/**
		Open a stream to read the decompressed version of another stream
		with optional log2 subsampling.
	*/
	FZ_FUNCTION FzStream fz_open_image_decomp_stream(const FzStream& arg_0, const FzCompressionParams& arg_1, int *l2factor);

	/** Class-aware wrapper for `::fz_open_image_decomp_stream_from_buffer()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_open_image_decomp_stream_from_buffer(::fz_compressed_buffer *arg_0)` => `(fz_stream *, int l2factor)`
	 */
	/**
		Open a stream to read the decompressed version of a buffer,
		with optional log2 subsampling.
	
		l2factor = NULL for no subsampling, or a pointer to an integer
		containing the maximum log2 subsample factor acceptable (0 =
		none, 1 = halve dimensions, 2 = quarter dimensions etc). If
		non-NULL, then *l2factor will be updated on exit with the actual
		log2 subsample factor achieved.
	*/
	FZ_FUNCTION FzStream fz_open_image_decomp_stream_from_buffer(const FzCompressedBuffer& arg_0, int *l2factor);

	/** Class-aware wrapper for `::fz_open_jbig2d()`.  */
	/**
		Open a filter that performs jbig2 decompression on the chained
		stream, using the optional globals record.
	*/
	FZ_FUNCTION FzStream fz_open_jbig2d(const FzStream& chain, const FzJbig2Globals& globals, int embedded);

	/** Class-aware wrapper for `::fz_open_leecher()`.  */
	/**
		Attach a filter to a stream that will store any
		characters read from the stream into the supplied buffer.
	
		chain: The underlying stream to leech from.
	
		buf: The buffer into which the read data should be appended.
		The buffer will be resized as required.
	
		Returns pointer to newly created stream. May throw exceptions on
		failure to allocate.
	*/
	FZ_FUNCTION FzStream fz_open_leecher(const FzStream& chain, const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_open_libarchive_archive()`.  */
	/**
		Open an archive using libarchive.
	
		An exception is thrown if the file is not supported by libarchive.
	
		filename: a path to an archive file as it would be given to
		open(2).
	*/
	FZ_FUNCTION FzArchive fz_open_libarchive_archive(const char *filename);

	/** Class-aware wrapper for `::fz_open_libarchive_archive_with_stream()`.  */
	/**
		Open an archive using libarchive.
	
		Open an archive using a seekable stream object rather than
		opening a file or directory on disk.
	
		An exception is thrown if the stream is not supported by libarchive.
	*/
	FZ_FUNCTION FzArchive fz_open_libarchive_archive_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::fz_open_libarchived()`.  */
	/**
		libarchived filter performs generic compressed decoding of data
		in any format understood by libarchive from the chained filter.
	
		This will throw an exception if libarchive is not built in, or
		if the compression format is not recognised.
	*/
	FZ_FUNCTION FzStream fz_open_libarchived(const FzStream& chain);

	/** Class-aware wrapper for `::fz_open_lzwd()`.  */
	/**
		lzwd filter performs LZW decoding of data read from the chained
		filter.
	
		early_change: (Default 1) specifies whether to change codes 1
		bit early.
	
		min_bits: (Default 9) specifies the minimum number of bits to
		use.
	
		reverse_bits: (Default 0) allows for compatibility with gif and
		old style tiffs (1).
	
		old_tiff: (Default 0) allows for different handling of the clear
		code, as found in old style tiffs.
	*/
	FZ_FUNCTION FzStream fz_open_lzwd(const FzStream& chain, int early_change, int min_bits, int reverse_bits, int old_tiff);

	/** Class-aware wrapper for `::fz_open_memory()`.  */
	/**
		Open a block of memory as a stream.
	
		data: Pointer to start of data block. Ownership of the data
		block is NOT passed in.
	
		len: Number of bytes in data block.
	
		Returns pointer to newly created stream. May throw exceptions on
		failure to allocate.
	*/
	FZ_FUNCTION FzStream fz_open_memory(const unsigned char *data, size_t len);

	/** Class-aware wrapper for `::fz_open_null_filter()`.  */
	/**
		The null filter reads a specified amount of data from the
		substream.
	*/
	FZ_FUNCTION FzStream fz_open_null_filter(const FzStream& chain, uint64_t len, int64_t offset);

	/** Class-aware wrapper for `::fz_open_predict()`.  */
	/**
		predict filter performs pixel prediction on data read from
		the chained filter.
	
		predictor: 1 = copy, 2 = tiff, other = inline PNG predictor
	
		columns: width of image in pixels
	
		colors: number of components.
	
		bpc: bits per component (typically 8)
	*/
	FZ_FUNCTION FzStream fz_open_predict(const FzStream& chain, int predictor, int columns, int colors, int bpc);

	/** Class-aware wrapper for `::fz_open_range_filter()`.  */
	/**
		The range filter copies data from specified ranges of the
		chained stream.
	*/
	FZ_FUNCTION FzStream fz_open_range_filter(const FzStream& chain, const FzRange& ranges, int nranges);

	/** Class-aware wrapper for `::fz_open_reflowed_document()`.  */
	FZ_FUNCTION FzDocument fz_open_reflowed_document(const FzDocument& underdoc, FzStextOptions& opts);

	/** Class-aware wrapper for `::fz_open_rld()`.  */
	/**
		rld filter performs Run Length Decoding of data read
		from the chained filter.
	*/
	FZ_FUNCTION FzStream fz_open_rld(const FzStream& chain);

	/** Class-aware wrapper for `::fz_open_sgilog16()`.  */
	/**
		SGI Log 16bit (greyscale) decode from the chained filter.
		Decodes lines of w pixels to 8bpp greyscale.
	*/
	FZ_FUNCTION FzStream fz_open_sgilog16(const FzStream& chain, int w);

	/** Class-aware wrapper for `::fz_open_sgilog24()`.  */
	/**
		SGI Log 24bit (LUV) decode from the chained filter.
		Decodes lines of w pixels to 8bpc rgb.
	*/
	FZ_FUNCTION FzStream fz_open_sgilog24(const FzStream& chain, int w);

	/** Class-aware wrapper for `::fz_open_sgilog32()`.  */
	/**
		SGI Log 32bit (LUV) decode from the chained filter.
		Decodes lines of w pixels to 8bpc rgb.
	*/
	FZ_FUNCTION FzStream fz_open_sgilog32(const FzStream& chain, int w);

	/** Class-aware wrapper for `::fz_open_tar_archive()`.  */
	/**
		Open a tar archive file.
	
		An exception is thrown if the file is not a tar archive as
		indicated by the presence of a tar signature.
	
		filename: a path to a tar archive file as it would be given to
		open(2).
	*/
	FZ_FUNCTION FzArchive fz_open_tar_archive(const char *filename);

	/** Class-aware wrapper for `::fz_open_tar_archive_with_stream()`.  */
	/**
		Open a tar archive stream.
	
		Open an archive using a seekable stream object rather than
		opening a file or directory on disk.
	
		An exception is thrown if the stream is not a tar archive as
		indicated by the presence of a tar signature.
	
	*/
	FZ_FUNCTION FzArchive fz_open_tar_archive_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::fz_open_thunder()`.  */
	/**
		4bit greyscale Thunderscan decoding from the chained filter.
		Decodes lines of w pixels to 8bpp greyscale.
	*/
	FZ_FUNCTION FzStream fz_open_thunder(const FzStream& chain, int w);

	/** Class-aware wrapper for `::fz_open_zip_archive()`.  */
	/**
		Open a zip archive file.
	
		An exception is thrown if the file is not a zip archive as
		indicated by the presence of a zip signature.
	
		filename: a path to a zip archive file as it would be given to
		open(2).
	*/
	FZ_FUNCTION FzArchive fz_open_zip_archive(const char *path);

	/** Class-aware wrapper for `::fz_open_zip_archive_with_stream()`.  */
	/**
		Open a zip archive stream.
	
		Open an archive using a seekable stream object rather than
		opening a file or directory on disk.
	
		An exception is thrown if the stream is not a zip archive as
		indicated by the presence of a zip signature.
	
	*/
	FZ_FUNCTION FzArchive fz_open_zip_archive_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::fz_opt_from_list()`.  */
	/**
		Return the index of a (case-insensitive) option within an optlist.
	
		For instance for optlist = "Foo|Bar|Baz", and  opt = "bar",
		this would return 1.
	
		If the optlist ends with "|*" then that is a catch all case and
		matches all options allowing the caller to process it itself.
		fz_optarg will be set to point to the option, and the return
		value will be the index of the '*' option within that list.
	
		If an optlist entry ends with ':' (e.g. "Foo:") then that option
		may have suboptions appended to it (for example "JPG:80") and
		fz_optarg will be set to point at "80". Otherwise fz_optarg will
		be set to NULL.
	
		In the event of no-match found, prints an error and returns -1.
	*/
	FZ_FUNCTION int fz_opt_from_list(char *opt, const char *optlist);

	/** Class-aware wrapper for `::fz_option_eq()`.  */
	/**
		Check to see if an option, a, from a string matches a reference
		option, b.
	
		(i.e. a could be 'foo' or 'foo,bar...' etc, but b can only be
		'foo'.)
	*/
	FZ_FUNCTION int fz_option_eq(const char *a, const char *b);

	/** Class-aware wrapper for `::fz_optpath()`.  */
	/**
		Convert "-" to "/dev/stdout" for use with command lines.
		Also converts "nul" and "con" on Windows.
	*/
	FZ_FUNCTION char *fz_optpath(char *opt);

	/** Class-aware wrapper for `::fz_outline_glyph()`.  */
	/**
		Look a glyph up from a font, and return the outline of the
		glyph using the given transform.
	
		The caller owns the returned path, and so is responsible for
		ensuring that it eventually gets dropped.
	*/
	FZ_FUNCTION FzPath fz_outline_glyph(const FzFont& font, int gid, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_outline_iterator_delete()`.  */
	/**
		Delete the current item.
	
		This implicitly moves us to the 'next' item, and the return code is as for fz_outline_iterator_next.
	*/
	FZ_FUNCTION int fz_outline_iterator_delete(const FzOutlineIterator& iter);

	/** Class-aware wrapper for `::fz_outline_iterator_down()`.  */
	FZ_FUNCTION int fz_outline_iterator_down(const FzOutlineIterator& iter);

	/* Class-aware wrapper for `fz_outline_iterator_from_outline()`
	is not available because returned wrapper class for `fz_outline_iterator`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_outline_iterator_item()`.  */
	/**
		Call to get the current outline item.
	
		Can return NULL. The item is only valid until the next call.
	*/
	FZ_FUNCTION FzOutlineItem fz_outline_iterator_item(const FzOutlineIterator& iter);

	/** Class-aware wrapper for `::fz_outline_iterator_next()`.  */
	/**
		Calls to move the iterator position.
	
		A negative return value means we could not move as requested. Otherwise:
		0 = the final position has a valid item.
		1 = not a valid item, but we can insert an item here.
	*/
	FZ_FUNCTION int fz_outline_iterator_next(const FzOutlineIterator& iter);

	/** Class-aware wrapper for `::fz_outline_iterator_prev()`.  */
	FZ_FUNCTION int fz_outline_iterator_prev(const FzOutlineIterator& iter);

	/** Class-aware wrapper for `::fz_outline_iterator_up()`.  */
	FZ_FUNCTION int fz_outline_iterator_up(const FzOutlineIterator& iter);

	/** Class-aware wrapper for `::fz_output_accelerator()`.  */
	/**
		Output accelerator data for the document to a given output
		stream.
	*/
	FZ_FUNCTION void fz_output_accelerator(const FzDocument& doc, const FzOutput& accel);

	/** Class-aware wrapper for `::fz_output_supports_stream()`.  */
	/**
		Query whether a given fz_output supports fz_stream_from_output.
	*/
	FZ_FUNCTION int fz_output_supports_stream(const FzOutput& out);

	/** Class-aware wrapper for `::fz_output_xml()`.  */
	/**
		Pretty-print an XML tree to given output.
	*/
	FZ_FUNCTION void fz_output_xml(const FzOutput& out, const FzXml& item, int level);

	/** Class-aware wrapper for `::fz_pack_path()`.  */
	/**
		Pack a path into the given block.
		To minimise the size of paths, this function allows them to be
		packed into a buffer with other information. Paths can be used
		interchangeably regardless of how they are packed.
	
		pack: Pointer to a block of data to pack the path into. Should
		be aligned by the caller to the same alignment as required for
		a fz_path pointer.
	
		path: The path to pack.
	
		Returns the number of bytes within the block used. Callers can
		access the packed path data by casting the value of pack on
		entry to be a fz_path *.
	
		Throws exceptions on failure to allocate.
	
		Implementation details: Paths can be 'unpacked', 'flat', or
		'open'. Standard paths, as created are 'unpacked'. Paths
		will be packed as 'flat', unless they are too large
		(where large indicates that they exceed some private
		implementation defined limits, currently including having
		more than 256 coordinates or commands).
	
		Large paths are 'open' packed as a header into the given block,
		plus pointers to other data blocks.
	
		Users should not have to care about whether paths are 'open'
		or 'flat' packed. Simply pack a path (if required), and then
		forget about the details.
	*/
	FZ_FUNCTION size_t fz_pack_path(uint8_t *pack, const FzPath& path);

	/** Class-aware wrapper for `::fz_packed_path_size()`.  */
	/**
		Return the number of bytes required to pack a path.
	*/
	FZ_FUNCTION int fz_packed_path_size(const FzPath& path);

	/** Class-aware wrapper for `::fz_page_label()`.  */
	/**
		Get page label for a given page.
	*/
	FZ_FUNCTION const char *fz_page_label(const FzPage& page, char *buf, int size);

	/** Class-aware wrapper for `::fz_page_number_from_location()`.  */
	/**
		Converts from chapter+page to page number. This may cause many
		chapters to be laid out in order to calculate the number of
		pages within those chapters.
	*/
	FZ_FUNCTION int fz_page_number_from_location(const FzDocument& doc, const FzLocation& loc);

	/** Class-aware wrapper for `::fz_page_presentation()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_page_presentation(::fz_page *page, ::fz_transition *transition)` => `(fz_transition *, float duration)`
	 */
	/**
		Get the presentation details for a given page.
	
		transition: A pointer to a transition struct to fill out.
	
		duration: A pointer to a place to set the page duration in
		seconds. Will be set to 0 if no transition is specified for the
		page.
	
		Returns: a pointer to the transition structure, or NULL if there
		is no transition specified for the page.
	*/
	FZ_FUNCTION FzTransition fz_page_presentation(const FzPage& page, FzTransition& transition, float *duration);

	/** Class-aware wrapper for `::fz_page_separations()`.  */
	/**
		Get the separations details for a page.
		This will be NULL, unless the format specifically supports
		separations (such as PDF files). May be NULL even
		so, if there are no separations on a page.
	
		Returns a reference that must be dropped.
	*/
	FZ_FUNCTION FzSeparations fz_page_separations(const FzPage& page);

	/** Class-aware wrapper for `::fz_page_uses_overprint()`.  */
	/**
		Query if a given page requires overprint.
	*/
	FZ_FUNCTION int fz_page_uses_overprint(const FzPage& page);

	/** Class-aware wrapper for `::fz_paint_shade()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_paint_shade(::fz_shade *shade, ::fz_colorspace *override_cs, ::fz_matrix ctm, ::fz_pixmap *dest, ::fz_color_params color_params, ::fz_irect bbox, const ::fz_overprint *eop, ::fz_shade_color_cache **cache)` => 
	 */
	/**
		Render a shade to a given pixmap.
	
		shade: The shade to paint.
	
		override_cs: NULL, or colorspace to override the shades
		inbuilt colorspace.
	
		ctm: The transform to apply.
	
		dest: The pixmap to render into.
	
		color_params: The color rendering settings
	
		bbox: Pointer to a bounding box to limit the rendering
		of the shade.
	
		eop: NULL, or pointer to overprint bitmap.
	
		cache: *cache is used to cache color information. If *cache is NULL it
		is set to point to a new fz_shade_color_cache. If cache is NULL it is
		ignored.
	*/
	FZ_FUNCTION void fz_paint_shade(const FzShade& shade, const FzColorspace& override_cs, const FzMatrix& ctm, const FzPixmap& dest, const FzColorParams& color_params, const FzIrect& bbox, const FzOverprint& eop, FzShadeColorCache& cache);

	/** Class-aware wrapper for `::fz_paragraph_break()`.  */
	/**
		Attempt to break paragraphs at plausible places.
	*/
	FZ_FUNCTION void fz_paragraph_break(const FzStextPage& page);

	/* Class-aware wrapper for `fz_parse_draw_options()`
	is not available because returned wrapper class for `fz_draw_options`
	is non-copyable. */

	/* Class-aware wrapper for `fz_parse_json()`
	is not available because returned wrapper class for `fz_json`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_parse_page_range()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_parse_page_range(const char *s, int n)` => `(const char *, int a, int b)`
	 */
	FZ_FUNCTION const char *fz_parse_page_range(const char *s, int *a, int *b, int n);

	/* Class-aware wrapper for `fz_parse_pcl_options()`
	is not available because returned wrapper class for `fz_pcl_options`
	is non-copyable. */

	/* Class-aware wrapper for `fz_parse_pclm_options()`
	is not available because returned wrapper class for `fz_pclm_options`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_parse_pdfocr_options()`.  */
	/**
		Parse PDFOCR options.
	
		Currently defined options and values are as follows:
	
			compression=none: No compression
			compression=flate: Flate compression
			strip-height=n: Strip height (default 16)
			ocr-language=<lang>: OCR Language (default eng)
			ocr-datadir=<datadir>: OCR data path (default rely on TESSDATA_PREFIX)
	*/
	FZ_FUNCTION FzPdfocrOptions fz_parse_pdfocr_options(FzPdfocrOptions& opts, const char *args);

	/** Class-aware wrapper for `::fz_parse_stext_options()`.  */
	/**
		Parse stext device options from a comma separated key-value
		string.
	*/
	FZ_FUNCTION FzStextOptions fz_parse_stext_options(FzStextOptions& opts, const char *string);

	/** Class-aware wrapper for `::fz_parse_xml()`.  */
	/**
		Parse the contents of buffer into a tree of xml nodes.
	
		preserve_white: whether to keep or delete all-whitespace nodes.
	*/
	FZ_FUNCTION FzXml fz_parse_xml(const FzBuffer& buf, int preserve_white);

	/** Class-aware wrapper for `::fz_parse_xml_archive_entry()`.  */
	/**
		Parse the contents of an archive entry into a tree of xml nodes.
	
		preserve_white: whether to keep or delete all-whitespace nodes.
	*/
	FZ_FUNCTION FzXml fz_parse_xml_archive_entry(const FzArchive& dir, const char *filename, int preserve_white);

	/** Class-aware wrapper for `::fz_parse_xml_from_html5()`.  */
	/**
		Parse the contents of a buffer into a tree of XML nodes,
		using the HTML5 parsing algorithm.
	*/
	FZ_FUNCTION FzXml fz_parse_xml_from_html5(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_parse_xml_stream()`.  */
	/**
		Parse the contents of buffer into a tree of xml nodes.
	
		preserve_white: whether to keep or delete all-whitespace nodes.
	*/
	FZ_FUNCTION FzXml fz_parse_xml_stream(const FzStream& stream, int preserve_white);

	/** Class-aware wrapper for `::fz_path_is_rect()`.  */
	/**
		Check whether a given path, under the given transform
		is an axis-aligned rectangle.
	
		We accept zero width or height rectangles, so
		"move 100, 100; line 200, 100" would count as
		a rectangle too.
	*/
	FZ_FUNCTION int fz_path_is_rect(const FzPath& path, const FzMatrix& ctm);

	/** Class-aware wrapper for `::fz_path_is_rect_with_bounds()`.  */
	/**
		Check whether a given path, under the given transform
		is an axis-aligned rectangle.
	
		We accept zero width or height rectangles, so
		"move 100, 100; line 200, 100" would count as
		a rectangle too.
	
		bounds = NULL, or place to return the rectangle
		bounds if the path is a rectangle.
	*/
	FZ_FUNCTION int fz_path_is_rect_with_bounds(const FzPath& path, const FzMatrix& ctm, FzRect& bounds);

	/** Class-aware wrapper for `::fz_pcl_preset()`.  */
	/**
		Initialize PCL option struct for a given preset.
	
		Currently defined presets include:
	
			generic	Generic PCL printer
			ljet4	HP DeskJet
			dj500	HP DeskJet 500
			fs600	Kyocera FS-600
			lj	HP LaserJet, HP LaserJet Plus
			lj2	HP LaserJet IIp, HP LaserJet IId
			lj3	HP LaserJet III
			lj3d	HP LaserJet IIId
			lj4	HP LaserJet 4
			lj4pl	HP LaserJet 4 PL
			lj4d	HP LaserJet 4d
			lp2563b	HP 2563B line printer
			oce9050	Oce 9050 Line printer
	*/
	FZ_FUNCTION void fz_pcl_preset(const FzPclOptions& opts, const char *preset);

	/** Class-aware wrapper for `::fz_pdfocr_band_writer_set_progress()`.  */
	/**
		Set the progress callback for a pdfocr bandwriter.
	*/
	FZ_FUNCTION void fz_pdfocr_band_writer_set_progress(const FzBandWriter& writer, ::fz_pdfocr_progress_fn *progress_fn, void *progress_arg);

	/** Class-aware wrapper for `::fz_pdfocr_writer_set_progress()`.  */
	FZ_FUNCTION void fz_pdfocr_writer_set_progress(const FzDocumentWriter& writer, ::fz_pdfocr_progress_fn *progress, void *arg_2);

	/** Class-aware wrapper for `::fz_peek_byte()`.  */
	/**
		Peek at the next byte in a stream.
	
		stm: The stream to peek at.
	
		Returns -1 for EOF, or the next byte that will be read.
	*/
	FZ_FUNCTION int fz_peek_byte(const FzStream& stm);

	/** Class-aware wrapper for `::fz_pixmap_alpha()`.  */
	/**
		Return the number of alpha planes in a pixmap.
	
		Returns the number of alphas. Does not throw exceptions.
	*/
	FZ_FUNCTION int fz_pixmap_alpha(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_bbox()`.  */
	/**
		Return the bounding box for a pixmap.
	*/
	FZ_FUNCTION FzIrect fz_pixmap_bbox(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_colorants()`.  */
	/**
		Return the number of colorants in a pixmap.
	
		Returns the number of colorants (components, less any spots and
		alpha).
	*/
	FZ_FUNCTION int fz_pixmap_colorants(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_colorspace()`.  */
	/**
		Return the colorspace of a pixmap
	
		Returns colorspace.
	*/
	FZ_FUNCTION FzColorspace fz_pixmap_colorspace(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_components()`.  */
	/**
		Return the number of components in a pixmap.
	
		Returns the number of components (including spots and alpha).
	*/
	FZ_FUNCTION int fz_pixmap_components(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_height()`.  */
	/**
		Return the height of the pixmap in pixels.
	*/
	FZ_FUNCTION int fz_pixmap_height(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_image_tile()`.  */
	/**
		Retrieve the underlying fz_pixmap for an image.
	
		Returns a pointer to the underlying fz_pixmap for an image,
		or NULL if this image is not based upon an fz_pixmap.
	
		No reference is returned. Lifespan is limited to that of
		the image itself. If required, use fz_keep_pixmap to take
		a reference to keep it longer.
	*/
	FZ_FUNCTION FzPixmap fz_pixmap_image_tile(const FzPixmapImage& cimg);

	/** Class-aware wrapper for `::fz_pixmap_samples()`.  */
	/**
		Returns a pointer to the pixel data of a pixmap.
	
		Returns the pointer.
	*/
	FZ_FUNCTION unsigned char *fz_pixmap_samples(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_samples_int()`.  */
	/** */
	FZ_FUNCTION long long fz_pixmap_samples_int(const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_pixmap_size()`.  */
	/**
		Return sizeof fz_pixmap plus size of data, in bytes.
	*/
	FZ_FUNCTION size_t fz_pixmap_size(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_spots()`.  */
	/**
		Return the number of spots in a pixmap.
	
		Returns the number of spots (components, less colorants and
		alpha). Does not throw exceptions.
	*/
	FZ_FUNCTION int fz_pixmap_spots(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_stride()`.  */
	/**
		Return the number of bytes in a row in the pixmap.
	*/
	FZ_FUNCTION int fz_pixmap_stride(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_width()`.  */
	/**
		Return the width of the pixmap in pixels.
	*/
	FZ_FUNCTION int fz_pixmap_width(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_x()`.  */
	/**
		Return the x value of the pixmap in pixels.
	*/
	FZ_FUNCTION int fz_pixmap_x(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_pixmap_y()`.  */
	/**
		Return the y value of the pixmap in pixels.
	*/
	FZ_FUNCTION int fz_pixmap_y(const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_place_story()`.  */
	FZ_FUNCTION int fz_place_story(const FzStory& story, const FzRect& where, FzRect& filled);

	/** Class-aware wrapper for `::fz_place_story_flags()`.  */
	FZ_FUNCTION int fz_place_story_flags(const FzStory& story, const FzRect& where, FzRect& filled, int flags);

	/** Class-aware wrapper for `::fz_pool_alloc()`.  */
	/**
		Allocate a block of size bytes from the pool.
	*/
	FZ_FUNCTION void *fz_pool_alloc(const FzPool& pool, size_t size);

	/** Class-aware wrapper for `::fz_pool_size()`.  */
	/**
		The current size of the pool.
	
		The number of bytes of storage currently allocated to the pool.
		This is the total of the storage used for the blocks making
		up the pool, rather then total of the allocated blocks so far,
		so it will increase in 'lumps'.
		from the pool, then the pool size may still be X
	*/
	FZ_FUNCTION size_t fz_pool_size(const FzPool& pool);

	/** Class-aware wrapper for `::fz_pool_strdup()`.  */
	/**
		strdup equivalent allocating from the pool.
	*/
	FZ_FUNCTION char *fz_pool_strdup(const FzPool& pool, const char *s);

	/** Class-aware wrapper for `::fz_pop_clip()`.  */
	FZ_FUNCTION void fz_pop_clip(const FzDevice& dev);

	/** Class-aware wrapper for `::fz_post_scale()`.  */
	/**
		Scale a matrix by postmultiplication.
	
		m: Pointer to the matrix to scale
	
		sx, sy: Scaling factors along the X- and Y-axes. A scaling
		factor of 1.0 will not cause any scaling along the relevant
		axis.
	
		Returns m (updated).
	*/
	FZ_FUNCTION FzMatrix fz_post_scale(const FzMatrix& m, float sx, float sy);

	/** Class-aware wrapper for `::fz_pre_rotate()`.  */
	/**
		Rotate a transformation by premultiplying.
	
		The premultiplied matrix is of the form
		[ cos(deg) sin(deg) -sin(deg) cos(deg) 0 0 ].
	
		m: Pointer to matrix to premultiply.
	
		degrees: Degrees of counter clockwise rotation. Values less
		than zero and greater than 360 are handled as expected.
	
		Returns m (updated).
	*/
	FZ_FUNCTION FzMatrix fz_pre_rotate(const FzMatrix& m, float degrees);

	/** Class-aware wrapper for `::fz_pre_scale()`.  */
	/**
		Scale a matrix by premultiplication.
	
		m: Pointer to the matrix to scale
	
		sx, sy: Scaling factors along the X- and Y-axes. A scaling
		factor of 1.0 will not cause any scaling along the relevant
		axis.
	
		Returns m (updated).
	*/
	FZ_FUNCTION FzMatrix fz_pre_scale(const FzMatrix& m, float sx, float sy);

	/** Class-aware wrapper for `::fz_pre_shear()`.  */
	/**
		Premultiply a matrix with a shearing matrix.
	
		The shearing matrix is of the form [ 1 sy sx 1 0 0 ].
	
		m: pointer to matrix to premultiply
	
		sx, sy: Shearing factors. A shearing factor of 0.0 will not
		cause any shearing along the relevant axis.
	
		Returns m (updated).
	*/
	FZ_FUNCTION FzMatrix fz_pre_shear(const FzMatrix& m, float sx, float sy);

	/** Class-aware wrapper for `::fz_pre_translate()`.  */
	/**
		Translate a matrix by premultiplication.
	
		m: The matrix to translate
	
		tx, ty: Translation distances along the X- and Y-axes. A
		translation of 0 will not cause any translation along the
		relevant axis.
	
		Returns m.
	*/
	FZ_FUNCTION FzMatrix fz_pre_translate(const FzMatrix& m, float tx, float ty);

	/** Class-aware wrapper for `::fz_prepare_t3_glyph()`.  */
	/**
		Force a type3 font to cache the displaylist for a given glyph
		id.
	
		This caching can involve reading the underlying file, so must
		happen ahead of time, so we aren't suddenly forced to read the
		file while playing a displaylist back.
	*/
	FZ_FUNCTION void fz_prepare_t3_glyph(const FzFont& font, int gid);

	/** Class-aware wrapper for `::fz_previous_page()`.  */
	/**
		Function to get the location of the previous page (allowing for
		the end of chapters etc). If already at the start of the
		document, returns the current page.
	*/
	FZ_FUNCTION FzLocation fz_previous_page(const FzDocument& doc, const FzLocation& loc);

	/** Class-aware wrapper for `::fz_print_stext_header_as_html()`.  */
	FZ_FUNCTION void fz_print_stext_header_as_html(const FzOutput& out);

	/** Class-aware wrapper for `::fz_print_stext_header_as_xhtml()`.  */
	FZ_FUNCTION void fz_print_stext_header_as_xhtml(const FzOutput& out);

	/** Class-aware wrapper for `::fz_print_stext_page_as_html()`.  */
	/**
		Output structured text to a file in HTML (visual) format.
	*/
	FZ_FUNCTION void fz_print_stext_page_as_html(const FzOutput& out, const FzStextPage& page, int id);

	/** Class-aware wrapper for `::fz_print_stext_page_as_json()`.  */
	/**
		Output structured text to a file in JSON format.
	*/
	FZ_FUNCTION void fz_print_stext_page_as_json(const FzOutput& out, const FzStextPage& page, float scale);

	/** Class-aware wrapper for `::fz_print_stext_page_as_text()`.  */
	/**
		Output structured text to a file in plain-text UTF-8 format.
	*/
	FZ_FUNCTION void fz_print_stext_page_as_text(const FzOutput& out, const FzStextPage& page);

	/** Class-aware wrapper for `::fz_print_stext_page_as_xhtml()`.  */
	/**
		Output structured text to a file in XHTML (semantic) format.
	*/
	FZ_FUNCTION void fz_print_stext_page_as_xhtml(const FzOutput& out, const FzStextPage& page, int id);

	/** Class-aware wrapper for `::fz_print_stext_page_as_xml()`.  */
	/**
		Output structured text to a file in XML format.
	*/
	FZ_FUNCTION void fz_print_stext_page_as_xml(const FzOutput& out, const FzStextPage& page, int id);

	/** Class-aware wrapper for `::fz_print_stext_trailer_as_html()`.  */
	FZ_FUNCTION void fz_print_stext_trailer_as_html(const FzOutput& out);

	/** Class-aware wrapper for `::fz_print_stext_trailer_as_xhtml()`.  */
	FZ_FUNCTION void fz_print_stext_trailer_as_xhtml(const FzOutput& out);

	/** Class-aware wrapper for `::fz_process_opened_pages()`.  */
	/**
		Iterates over all opened pages of the document, calling the
		provided callback for each page for processing. If the callback
		returns non-NULL then the iteration stops and that value is returned
		to the called of fz_process_opened_pages().
	
		The state pointer provided to fz_process_opened_pages() is
		passed on to the callback but is owned by the caller.
	
		Returns the first non-NULL value returned by the callback,
		or NULL if the callback returned NULL for all opened pages.
	*/
	FZ_FUNCTION void *fz_process_opened_pages(const FzDocument& doc, ::fz_process_opened_page_fn *process_openend_page, void *state);

	/** Class-aware wrapper for `::fz_process_shade()`.  */
	/**
		Process a shade, using supplied callback functions. This
		decomposes the shading to a mesh (even ones that are not
		natively meshes, such as linear or radial shadings), and
		processes triangles from those meshes.
	
		shade: The shade to process.
	
		ctm: The transform to use
	
		prepare: Callback function to 'prepare' each vertex.
		This function is passed an array of floats, and populates
		a fz_vertex structure.
	
		process: This function is passed 3 pointers to vertex
		structures, and actually performs the processing (typically
		filling the area between the vertices).
	
		process_arg: An opaque argument passed through from caller
		to callback functions.
	*/
	FZ_FUNCTION void fz_process_shade(const FzShade& shade, const FzMatrix& ctm, const FzRect& scissor, ::fz_shade_prepare_fn *prepare, ::fz_shade_process_fn *process, void *process_arg);

	/** Class-aware wrapper for `::fz_ptr_heap_insert()`.  */
	FZ_FUNCTION void fz_ptr_heap_insert(const FzPtrHeap& heap, void *v, int (*HEAP_CMP)(void **, void **));

	/** Class-aware wrapper for `::fz_ptr_heap_sort()`.  */
	FZ_FUNCTION void fz_ptr_heap_sort(const FzPtrHeap& heap, int (*HEAP_CMP)(void **, void **));

	/** Class-aware wrapper for `::fz_ptr_heap_uniq()`.  */
	FZ_FUNCTION void fz_ptr_heap_uniq(const FzPtrHeap& heap, int (*HEAP_CMP)(void **, void **));

	/** Class-aware wrapper for `::fz_purge_glyph_cache()`.  */
	/**
		Purge all the glyphs from the cache.
	*/
	FZ_FUNCTION void fz_purge_glyph_cache();

	/** Class-aware wrapper for `::fz_quad_from_rect()`.  */
	/**
		Convert a rect to a quad (losslessly).
	*/
	FZ_FUNCTION FzQuad fz_quad_from_rect(const FzRect& r);

	/** Class-aware wrapper for `::fz_quadto()`.  */
	/**
		Append a 'quadto' command to an open path. (For a
		quadratic bezier).
	
		path: The path to modify.
	
		x0, y0: The control coordinates for the quadratic curve.
	
		x1, y1: The end coordinates for the quadratic curve.
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_quadto(const FzPath& path, float x0, float y0, float x1, float y1);

	/** Class-aware wrapper for `::fz_range_limit_xml_char()`.  */
	FZ_FUNCTION int fz_range_limit_xml_char(int c);

	/** Class-aware wrapper for `::fz_read()`.  */
	/**
		Read from a stream into a given data block.
	
		stm: The stream to read from.
	
		data: The data block to read into.
	
		len: The length of the data block (in bytes).
	
		Returns the number of bytes read. May throw exceptions.
	*/
	FZ_FUNCTION size_t fz_read(const FzStream& stm, unsigned char *data, size_t len);

	/** Class-aware wrapper for `::fz_read_all()`.  */
	/**
		Read all of a stream into a buffer.
	
		stm: The stream to read from
	
		initial: Suggested initial size for the buffer.
	
		Returns a buffer created from reading from the stream. May throw
		exceptions on failure to allocate.
	*/
	FZ_FUNCTION FzBuffer fz_read_all(const FzStream& stm, size_t initial);

	/** Class-aware wrapper for `::fz_read_archive_entry()`.  */
	/**
		Reads all bytes in an archive entry
		into a buffer.
	
		name: Entry name to look for, this must be an exact match to
		the entry name in the archive.
	
		Throws an exception if a matching entry cannot be found.
	*/
	FZ_FUNCTION FzBuffer fz_read_archive_entry(const FzArchive& arch, const char *name);

	/** Class-aware wrapper for `::fz_read_best()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_read_best(::fz_stream *stm, size_t initial, size_t worst_case)` => `(fz_buffer *, int truncated)`
	 */
	/**
		Attempt to read a stream into a buffer. If truncated
		is NULL behaves as fz_read_all, sets a truncated flag in case of
		error.
	
		stm: The stream to read from.
	
		initial: Suggested initial size for the buffer.
	
		truncated: Flag to store success/failure indication in.
	
		worst_case: 0 for unknown, otherwise an upper bound for the
		size of the stream.
	
		Returns a buffer created from reading from the stream.
	*/
	FZ_FUNCTION FzBuffer fz_read_best(const FzStream& stm, size_t initial, int *truncated, size_t worst_case);

	/** Class-aware wrapper for `::fz_read_bits()`.  */
	/**
		Read the next n bits from a stream (assumed to
		be packed most significant bit first).
	
		stm: The stream to read from.
	
		n: The number of bits to read, between 1 and 8*sizeof(int)
		inclusive.
	
		Returns -1 for EOF, or the required number of bits.
	*/
	FZ_FUNCTION unsigned int fz_read_bits(const FzStream& stm, int n);

	/** Class-aware wrapper for `::fz_read_byte()`.  */
	/**
		Read the next byte from a stream.
	
		stm: The stream t read from.
	
		Returns -1 for end of stream, or the next byte. May
		throw exceptions.
	*/
	FZ_FUNCTION int fz_read_byte(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_file()`.  */
	/**
		Read all the contents of a file into a buffer.
	*/
	FZ_FUNCTION FzBuffer fz_read_file(const char *filename);

	/** Class-aware wrapper for `::fz_read_float()`.  */
	FZ_FUNCTION float fz_read_float(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_float_le()`.  */
	FZ_FUNCTION float fz_read_float_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_int16()`.  */
	FZ_FUNCTION int16_t fz_read_int16(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_int16_le()`.  */
	FZ_FUNCTION int16_t fz_read_int16_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_int32()`.  */
	FZ_FUNCTION int32_t fz_read_int32(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_int32_le()`.  */
	FZ_FUNCTION int32_t fz_read_int32_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_int64()`.  */
	FZ_FUNCTION int64_t fz_read_int64(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_int64_le()`.  */
	FZ_FUNCTION int64_t fz_read_int64_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_line()`.  */
	/**
		Read a line from stream into the buffer until either a
		terminating newline or EOF, which it replaces with a null byte
		('\0').
	
		Returns buf on success, and NULL when end of file occurs while
		no characters have been read.
	*/
	FZ_FUNCTION char *fz_read_line(const FzStream& stm, char *buf, size_t max);

	/** Class-aware wrapper for `::fz_read_rbits()`.  */
	/**
		Read the next n bits from a stream (assumed to
		be packed least significant bit first).
	
		stm: The stream to read from.
	
		n: The number of bits to read, between 1 and 8*sizeof(int)
		inclusive.
	
		Returns (unsigned int)-1 for EOF, or the required number of bits.
	*/
	FZ_FUNCTION unsigned int fz_read_rbits(const FzStream& stm, int n);

	/** Class-aware wrapper for `::fz_read_rune()`.  */
	/**
		Read a utf-8 rune from a stream.
	
		In the event of encountering badly formatted utf-8 codes
		(such as a leading code with an unexpected number of following
		codes) no error/exception is given, but undefined values may be
		returned.
	*/
	FZ_FUNCTION int fz_read_rune(const FzStream& in);

	/** Class-aware wrapper for `::fz_read_string()`.  */
	/**
		Read a null terminated string from the stream into
		a buffer of a given length. The buffer will be null terminated.
		Throws on failure (including the failure to fit the entire
		string including the terminator into the buffer).
	*/
	FZ_FUNCTION void fz_read_string(const FzStream& stm, char *buffer, int len);

	/** Class-aware wrapper for `::fz_read_uint16()`.  */
	/**
		fz_read_[u]int(16|24|32|64)(_le)?
	
		Read a 16/32/64 bit signed/unsigned integer from stream,
		in big or little-endian byte orders.
	
		Throws an exception if EOF is encountered.
	*/
	FZ_FUNCTION uint16_t fz_read_uint16(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint16_le()`.  */
	FZ_FUNCTION uint16_t fz_read_uint16_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint24()`.  */
	FZ_FUNCTION uint32_t fz_read_uint24(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint24_le()`.  */
	FZ_FUNCTION uint32_t fz_read_uint24_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint32()`.  */
	FZ_FUNCTION uint32_t fz_read_uint32(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint32_le()`.  */
	FZ_FUNCTION uint32_t fz_read_uint32_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint64()`.  */
	FZ_FUNCTION uint64_t fz_read_uint64(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_uint64_le()`.  */
	FZ_FUNCTION uint64_t fz_read_uint64_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_utf16_be()`.  */
	FZ_FUNCTION int fz_read_utf16_be(const FzStream& stm);

	/** Class-aware wrapper for `::fz_read_utf16_le()`.  */
	/**
		Read a utf-16 rune from a stream. (little endian and
		big endian respectively).
	
		In the event of encountering badly formatted utf-16 codes
		(mismatched surrogates) no error/exception is given, but
		undefined values may be returned.
	*/
	FZ_FUNCTION int fz_read_utf16_le(const FzStream& stm);

	/** Class-aware wrapper for `::fz_realloc()`.  */
	/**
		Reallocates a block of memory to given size. Existing contents
		up to min(old_size,new_size) are maintained. The rest of the
		block is uninitialised.
	
		fz_realloc(ctx, NULL, size) behaves like fz_malloc(ctx, size).
	
		fz_realloc(ctx, p, 0); behaves like fz_free(ctx, p).
	
		Throws exception in the event of failure to allocate.
	*/
	FZ_FUNCTION void *fz_realloc(void *p, size_t size);

	/** Class-aware wrapper for `::fz_realloc_no_throw()`.  */
	/**
		fz_realloc equivalent that returns NULL rather than throwing
		exceptions.
	*/
	FZ_FUNCTION void *fz_realloc_no_throw(void *p, size_t size);

	/** Class-aware wrapper for `::fz_realpath()`.  */
	/**
		Resolve a path to an absolute file name.
		The resolved path buffer must be of at least PATH_MAX size.
	*/
	FZ_FUNCTION char *fz_realpath(const char *path, char *resolved_path);

	/* Class-aware wrapper for `fz_recognize_document()`
	is not available because returned wrapper class for `fz_document_handler`
	is non-copyable. */

	/* Class-aware wrapper for `fz_recognize_document_content()`
	is not available because returned wrapper class for `fz_document_handler`
	is non-copyable. */

	/* Class-aware wrapper for `fz_recognize_document_stream_and_dir_content()`
	is not available because returned wrapper class for `fz_document_handler`
	is non-copyable. */

	/* Class-aware wrapper for `fz_recognize_document_stream_content()`
	is not available because returned wrapper class for `fz_document_handler`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_recognize_image_format()`.  */
	/**
		Recognise image format strings in the first 8 bytes from image
		data.
	*/
	FZ_FUNCTION int fz_recognize_image_format(unsigned char p[8]);

	/** Class-aware wrapper for `::fz_rect_from_irect()`.  */
	/**
		Convert a bbox into a rect.
	
		For our purposes, a rect can represent all the values we meet in
		a bbox, so nothing can go wrong.
	
		rect: A place to store the generated rectangle.
	
		bbox: The bbox to convert.
	
		Returns rect (updated).
	*/
	FZ_FUNCTION FzRect fz_rect_from_irect(const FzIrect& bbox);

	/** Class-aware wrapper for `::fz_rect_from_quad()`.  */
	/**
		Convert a quad to the smallest rect that covers it.
	*/
	FZ_FUNCTION FzRect fz_rect_from_quad(const FzQuad& q);

	/** Class-aware wrapper for `::fz_rectto()`.  */
	/**
		Append a 'rectto' command to an open path.
	
		The rectangle is equivalent to:
			moveto x0 y0
			lineto x1 y0
			lineto x1 y1
			lineto x0 y1
			closepath
	
		path: The path to modify.
	
		x0, y0: First corner of the rectangle.
	
		x1, y1: Second corner of the rectangle.
	
		Throws exceptions on failure to allocate, or attempting to
		modify a packed path.
	*/
	FZ_FUNCTION void fz_rectto(const FzPath& path, float x0, float y0, float x1, float y1);

	/** Class-aware wrapper for `::fz_register_activity_logger()`.  */
	FZ_FUNCTION void fz_register_activity_logger(::fz_activity_fn *activity, void *opaque);

	/** Class-aware wrapper for `::fz_register_archive_handler()`.  */
	FZ_FUNCTION void fz_register_archive_handler(const FzArchiveHandler& handler);

	/** Class-aware wrapper for `::fz_register_document_handler()`.  */
	/**
		Register a handler for a document type.
	
		handler: The handler to register. This must live on for the duration of the
		use of this handler. It will be passed back to the handler for calls so
		the caller can use it to retrieve state.
	*/
	FZ_FUNCTION void fz_register_document_handler(const FzDocumentHandler& handler);

	/** Class-aware wrapper for `::fz_register_document_handlers()`.  */
	/**
		Register handlers for all the standard document types supported in
		this build.
	*/
	FZ_FUNCTION void fz_register_document_handlers();

	/** Class-aware wrapper for `::fz_remove_item()`.  */
	/**
		Remove an item from the store.
	
		If an item indexed by the given key exists in the store, remove
		it.
	
		drop: The function used to free the value (to ensure we get a
		value of the correct type).
	
		key: The key used to find the item to remove.
	
		type: Functions used to manipulate the key.
	*/
	FZ_FUNCTION void fz_remove_item(::fz_store_drop_fn *drop, void *key, const FzStoreType& type);

	/** Class-aware wrapper for `::fz_render_flags()`.  */
	FZ_FUNCTION void fz_render_flags(const FzDevice& dev, int set, int clear);

	/** Class-aware wrapper for `::fz_render_glyph_pixmap()`.  */
	/**
		Create a pixmap containing a rendered glyph.
	
		Lookup gid from font, clip it with scissor, and rendering it
		with aa bits of antialiasing into a new pixmap.
	
		The caller takes ownership of the pixmap and so must free it.
	
		Note: This function is no longer used for normal rendering
		operations, and is kept around just because we use it in the
		app. It should be considered "at risk" of removal from the API.
	*/
	FZ_FUNCTION FzPixmap fz_render_glyph_pixmap(const FzFont& font, int gid, FzMatrix& ctm, FzIrect& scissor, int aa);

	/** Class-aware wrapper for `::fz_render_t3_glyph_direct()`.  */
	/**
		Nasty PDF interpreter specific hernia, required to allow the
		interpreter to replay glyphs from a type3 font directly into
		the target device.
	
		This is only used in exceptional circumstances (such as type3
		glyphs that inherit current graphics state, or nested type3
		glyphs).
	*/
	FZ_FUNCTION void fz_render_t3_glyph_direct(const FzDevice& dev, const FzFont& font, int gid, const FzMatrix& trm, void *gstate, const FzDefaultColorspaces& def_cs, void *fill_gstate, void *stroke_gstate);

	/** Class-aware wrapper for `::fz_rendering_intent_name()`.  */
	/**
		Map from enumerated rendering intent to string.
	
		The returned string is static and therefore must not be freed.
	*/
	FZ_FUNCTION const char *fz_rendering_intent_name(int ri);

	/** Class-aware wrapper for `::fz_report_error()`.  */
	FZ_FUNCTION void fz_report_error();

	/** Class-aware wrapper for `::fz_reset_output()`.  */
	/**
		Reset a closed output stream. Returns state to
		(broadly) that which it was in when opened. Not
		all outputs can be reset, so this may throw an
		exception.
	*/
	FZ_FUNCTION void fz_reset_output(const FzOutput& arg_0);

	/** Class-aware wrapper for `::fz_reset_story()`.  */
	FZ_FUNCTION void fz_reset_story(const FzStory& story);

	/** Class-aware wrapper for `::fz_resize_buffer()`.  */
	/**
		Ensure that a buffer has a given capacity,
		truncating data if required.
	
		capacity: The desired capacity for the buffer. If the current
		size of the buffer contents is smaller than capacity, it is
		truncated.
	*/
	FZ_FUNCTION void fz_resize_buffer(const FzBuffer& buf, size_t capacity);

	/** Class-aware wrapper for `::fz_resolve_link()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_resolve_link(::fz_document *doc, const char *uri)` => `(fz_location, float xp, float yp)`
	 */
	/**
		Resolve an internal link to a page number.
	
		xp, yp: Pointer to store coordinate of destination on the page.
	
		Returns (-1,-1) if the URI cannot be resolved.
	*/
	FZ_FUNCTION FzLocation fz_resolve_link(const FzDocument& doc, const char *uri, float *xp, float *yp);

	/* Class-aware wrapper for `fz_resolve_link_dest()`
	is not available because returned wrapper class for `fz_link_dest`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_rethrow()`.  */
	FZ_FUNCTION void fz_rethrow();

	/** Class-aware wrapper for `::fz_rethrow_if()`.  */
	/**
		Within an fz_catch() block, rethrow the current exception
		if the errcode of the current exception matches.
	
		This assumes no intervening use of fz_try/fz_catch.
	*/
	FZ_FUNCTION void fz_rethrow_if(int errcode);

	/** Class-aware wrapper for `::fz_rethrow_unless()`.  */
	FZ_FUNCTION void fz_rethrow_unless(int errcode);

	/** Class-aware wrapper for `::fz_rotate()`.  */
	/**
		Create a rotation matrix.
	
		The returned matrix is of the form
		[ cos(deg) sin(deg) -sin(deg) cos(deg) 0 0 ].
	
		m: Pointer to place to store matrix
	
		degrees: Degrees of counter clockwise rotation. Values less
		than zero and greater than 360 are handled as expected.
	
		Returns m.
	*/
	FZ_FUNCTION FzMatrix fz_rotate(float degrees);

	/** Class-aware wrapper for `::fz_round_rect()`.  */
	/**
		Round rectangle coordinates.
	
		Coordinates in a bounding box are integers, so rounding of the
		rects coordinates takes place. The top left corner is rounded
		upwards and left while the bottom right corner is rounded
		downwards and to the right.
	
		This differs from fz_irect_from_rect, in that fz_irect_from_rect
		slavishly follows the numbers (i.e any slight over/under
		calculations can cause whole extra pixels to be added).
		fz_round_rect allows for a small amount of rounding error when
		calculating the bbox.
	*/
	FZ_FUNCTION FzIrect fz_round_rect(const FzRect& rect);

	/** Class-aware wrapper for `::fz_run_display_list()`.  */
	/**
		(Re)-run a display list through a device.
	
		list: A display list, created by fz_new_display_list and
		populated with objects from a page by running fz_run_page on a
		device obtained from fz_new_list_device.
	
		ctm: Transform to apply to display list contents. May include
		for example scaling and rotation, see fz_scale, fz_rotate and
		fz_concat. Set to fz_identity if no transformation is desired.
	
		scissor: Only the part of the contents of the display list
		visible within this area will be considered when the list is
		run through the device. This does not imply for tile objects
		contained in the display list.
	
		cookie: Communication mechanism between caller and library
		running the page. Intended for multi-threaded applications,
		while single-threaded applications set cookie to NULL. The
		caller may abort an ongoing page run. Cookie also communicates
		progress information back to the caller. The fields inside
		cookie are continually updated while the page is being run.
	*/
	FZ_FUNCTION void fz_run_display_list(const FzDisplayList& list, const FzDevice& dev, const FzMatrix& ctm, const FzRect& scissor, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_run_document_structure()`.  */
	/**
		Run the document structure through a device.
	
		doc: Document in question.
	
		dev: Device obtained from fz_new_*_device.
	
		cookie: Communication mechanism between caller and library.
		Intended for multi-threaded applications, while
		single-threaded applications set cookie to NULL. The
		caller may abort an ongoing rendering of a page. Cookie also
		communicates progress information back to the caller. The
		fields inside cookie are continually updated while the page is
		rendering.
	*/
	FZ_FUNCTION void fz_run_document_structure(const FzDocument& doc, const FzDevice& dev, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_run_page()`.  */
	/**
		Run a page through a device.
	
		page: Page obtained from fz_load_page.
	
		dev: Device obtained from fz_new_*_device.
	
		transform: Transform to apply to page. May include for example
		scaling and rotation, see fz_scale, fz_rotate and fz_concat.
		Set to fz_identity if no transformation is desired.
	
		cookie: Communication mechanism between caller and library
		rendering the page. Intended for multi-threaded applications,
		while single-threaded applications set cookie to NULL. The
		caller may abort an ongoing rendering of a page. Cookie also
		communicates progress information back to the caller. The
		fields inside cookie are continually updated while the page is
		rendering.
	*/
	FZ_FUNCTION void fz_run_page(const FzPage& page, const FzDevice& dev, const FzMatrix& transform, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_run_page_annots()`.  */
	/**
		Run the annotations on a page through a device.
	*/
	FZ_FUNCTION void fz_run_page_annots(const FzPage& page, const FzDevice& dev, const FzMatrix& transform, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_run_page_contents()`.  */
	/**
		Run a page through a device. Just the main
		page content, without the annotations, if any.
	
		page: Page obtained from fz_load_page.
	
		dev: Device obtained from fz_new_*_device.
	
		transform: Transform to apply to page. May include for example
		scaling and rotation, see fz_scale, fz_rotate and fz_concat.
		Set to fz_identity if no transformation is desired.
	
		cookie: Communication mechanism between caller and library
		rendering the page. Intended for multi-threaded applications,
		while single-threaded applications set cookie to NULL. The
		caller may abort an ongoing rendering of a page. Cookie also
		communicates progress information back to the caller. The
		fields inside cookie are continually updated while the page is
		rendering.
	*/
	FZ_FUNCTION void fz_run_page_contents(const FzPage& page, const FzDevice& dev, const FzMatrix& transform, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_run_page_widgets()`.  */
	/**
		Run the widgets on a page through a device.
	*/
	FZ_FUNCTION void fz_run_page_widgets(const FzPage& page, const FzDevice& dev, const FzMatrix& transform, FzCookie& cookie);

	/** Class-aware wrapper for `::fz_run_t3_glyph()`.  */
	/**
		Run a glyph from a Type3 font to
		a given device.
	
		font: The font to find the glyph in.
	
		gid: The glyph to run.
	
		trm: The transform to apply.
	
		dev: The device to render onto.
	*/
	FZ_FUNCTION void fz_run_t3_glyph(const FzFont& font, int gid, const FzMatrix& trm, const FzDevice& dev);

	/** Class-aware wrapper for `::fz_runeidx()`.  */
	/**
		Compute the index of a rune in a string.
	
		str: Pointer to beginning of a string.
	
		p: Pointer to a char in str.
	
		Returns the index of the rune pointed to by p in str.
	*/
	FZ_FUNCTION int fz_runeidx(const char *str, const char *p);

	/** Class-aware wrapper for `::fz_runelen()`.  */
	/**
		Count how many chars are required to represent a rune.
	
		rune: The rune to encode.
	
		Returns the number of bytes required to represent this run in
		UTF8.
	*/
	FZ_FUNCTION int fz_runelen(int rune);

	/** Class-aware wrapper for `::fz_runeptr()`.  */
	/**
		Obtain a pointer to the char representing the rune
		at a given index.
	
		str: Pointer to beginning of a string.
	
		idx: Index of a rune to return a char pointer to.
	
		Returns a pointer to the char where the desired rune starts,
		or NULL if the string ends before the index is reached.
	*/
	FZ_FUNCTION const char *fz_runeptr(const char *str, int idx);

	/** Class-aware wrapper for `::fz_runetochar()`.  */
	/**
		UTF8 encode a rune to a sequence of chars.
	
		str: Pointer to a place to put the UTF8 encoded character.
	
		rune: Pointer to a 'rune'.
	
		Returns the number of bytes the rune took to output.
	*/
	FZ_FUNCTION int fz_runetochar(char *str, int rune);

	/** Class-aware wrapper for `::fz_samples_get()`.  */
	/**
	Provides simple (but slow) access to pixmap data from Python and C#.
	*/
	FZ_FUNCTION int fz_samples_get(const FzPixmap& pixmap, int offset);

	/** Class-aware wrapper for `::fz_samples_set()`.  */
	/**
	Provides simple (but slow) write access to pixmap data from Python and
	C#.
	*/
	FZ_FUNCTION void fz_samples_set(const FzPixmap& pixmap, int offset, int value);

	/** Class-aware wrapper for `::fz_save_accelerator()`.  */
	/**
		Save accelerator data for the document to a given file.
	*/
	FZ_FUNCTION void fz_save_accelerator(const FzDocument& doc, const char *accel);

	/** Class-aware wrapper for `::fz_save_bitmap_as_pbm()`.  */
	/**
		Save a bitmap as a pbm.
	*/
	FZ_FUNCTION void fz_save_bitmap_as_pbm(const FzBitmap& bitmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_bitmap_as_pcl()`.  */
	/**
		Save a bitmap as mono PCL.
	*/
	FZ_FUNCTION void fz_save_bitmap_as_pcl(const FzBitmap& bitmap, char *filename, int append, const FzPclOptions& pcl);

	/** Class-aware wrapper for `::fz_save_bitmap_as_pkm()`.  */
	/**
		Save a CMYK bitmap as a pkm.
	*/
	FZ_FUNCTION void fz_save_bitmap_as_pkm(const FzBitmap& bitmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_bitmap_as_pwg()`.  */
	/**
		Save a bitmap as a PWG.
	*/
	FZ_FUNCTION void fz_save_bitmap_as_pwg(const FzBitmap& bitmap, char *filename, int append, FzPwgOptions& pwg);

	/** Class-aware wrapper for `::fz_save_buffer()`.  */
	/**
		Save the contents of a buffer to a file.
	*/
	FZ_FUNCTION void fz_save_buffer(const FzBuffer& buf, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_jpeg()`.  */
	/**
		Save a pixmap as a JPEG.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_jpeg(const FzPixmap& pixmap, const char *filename, int quality);

	/** Class-aware wrapper for `::fz_save_pixmap_as_jpx()`.  */
	/**
		Save pixmap data as JP2K with no subsampling.
	
		quality = 100 = lossless
		otherwise for a factor of x compression use 100-x. (so 80 is 1:20 compression)
	*/
	FZ_FUNCTION void fz_save_pixmap_as_jpx(const FzPixmap& pixmap, const char *filename, int q);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pam()`.  */
	/**
		Save a pixmap as a pnm (greyscale, rgb or cmyk, with or without
		alpha).
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pam(const FzPixmap& pixmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pbm()`.  */
	/**
		Save a pixmap as a pbm. (Performing halftoning).
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pbm(const FzPixmap& pixmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pcl()`.  */
	/**
		Save an (RGB) pixmap as color PCL.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pcl(const FzPixmap& pixmap, char *filename, int append, const FzPclOptions& pcl);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pclm()`.  */
	/**
		Save a (Greyscale or RGB) pixmap as pclm.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pclm(const FzPixmap& pixmap, const char *filename, int append, const FzPclmOptions& options);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pdfocr()`.  */
	/**
		Save a (Greyscale or RGB) pixmap as pdfocr.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pdfocr(const FzPixmap& pixmap, char *filename, int append, FzPdfocrOptions& options);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pkm()`.  */
	/**
		Save a CMYK pixmap as a pkm. (Performing halftoning).
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pkm(const FzPixmap& pixmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_png()`.  */
	/**
		Save a (Greyscale or RGB) pixmap as a png.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_png(const FzPixmap& pixmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pnm()`.  */
	/**
		Save a pixmap as a pnm (greyscale or rgb, no alpha).
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pnm(const FzPixmap& pixmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_ps()`.  */
	/**
		Save a (gray, rgb, or cmyk, no alpha) pixmap out as postscript.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_ps(const FzPixmap& pixmap, char *filename, int append);

	/** Class-aware wrapper for `::fz_save_pixmap_as_psd()`.  */
	/**
		Save a pixmap as a PSD file.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_psd(const FzPixmap& pixmap, const char *filename);

	/** Class-aware wrapper for `::fz_save_pixmap_as_pwg()`.  */
	/**
		Save a pixmap as a PWG.
	*/
	FZ_FUNCTION void fz_save_pixmap_as_pwg(const FzPixmap& pixmap, char *filename, int append, FzPwgOptions& pwg);

	/** Class-aware wrapper for `::fz_save_xml()`.  */
	/**
		As for fz_write_xml, but direct to a file.
	*/
	FZ_FUNCTION void fz_save_xml(const FzXml& root, const char *path, int indented);

	/** Class-aware wrapper for `::fz_scale()`.  */
	/**
		Create a scaling matrix.
	
		The returned matrix is of the form [ sx 0 0 sy 0 0 ].
	
		m: Pointer to the matrix to populate
	
		sx, sy: Scaling factors along the X- and Y-axes. A scaling
		factor of 1.0 will not cause any scaling along the relevant
		axis.
	
		Returns m.
	*/
	FZ_FUNCTION FzMatrix fz_scale(float sx, float sy);

	/** Class-aware wrapper for `::fz_scale_pixmap()`.  */
	FZ_FUNCTION FzPixmap fz_scale_pixmap(const FzPixmap& src, float x, float y, float w, float h, FzIrect& clip);

	/** Class-aware wrapper for `::fz_search_chapter_page_number()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_search_chapter_page_number(::fz_document *doc, int chapter, int page, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
	 */
	FZ_FUNCTION int fz_search_chapter_page_number(const FzDocument& doc, int chapter, int page, const char *needle, int *hit_mark, FzQuad& hit_bbox, int hit_max);

	/** Class-aware wrapper for `::fz_search_chapter_page_number_cb()`.  */
	FZ_FUNCTION int fz_search_chapter_page_number_cb(const FzDocument& doc, int chapter, int page, const char *needle, ::fz_search_callback_fn *cb, void *opaque);

	/** Class-aware wrapper for `::fz_search_display_list()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_search_display_list(::fz_display_list *list, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
	 */
	FZ_FUNCTION int fz_search_display_list(const FzDisplayList& list, const char *needle, int *hit_mark, FzQuad& hit_bbox, int hit_max);

	/** Class-aware wrapper for `::fz_search_display_list_cb()`.  */
	FZ_FUNCTION int fz_search_display_list_cb(const FzDisplayList& list, const char *needle, ::fz_search_callback_fn *cb, void *opaque);

	/** Class-aware wrapper for `::fz_search_page()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_search_page(::fz_page *page, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
	 */
	/**
		Search for the 'needle' text on the page.
		Record the hits in the hit_bbox array and return the number of
		hits. Will stop looking once it has filled hit_max rectangles.
	*/
	FZ_FUNCTION int fz_search_page(const FzPage& page, const char *needle, int *hit_mark, FzQuad& hit_bbox, int hit_max);

	/** Class-aware wrapper for `::fz_search_page2()`.  */
	/**
	C++ alternative to fz_search_page() that returns information in a std::vector.
	*/
	FZ_FUNCTION std::vector<fz_search_page2_hit> fz_search_page2(const FzDocument& doc, int number, const char *needle, int hit_max);

	/** Class-aware wrapper for `::fz_search_page_cb()`.  */
	/**
		Search for the 'needle' text on the page.
	*/
	FZ_FUNCTION int fz_search_page_cb(const FzPage& page, const char *needle, ::fz_search_callback_fn *cb, void *opaque);

	/** Class-aware wrapper for `::fz_search_page_number()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_search_page_number(::fz_document *doc, int number, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
	 */
	FZ_FUNCTION int fz_search_page_number(const FzDocument& doc, int number, const char *needle, int *hit_mark, FzQuad& hit_bbox, int hit_max);

	/** Class-aware wrapper for `::fz_search_page_number_cb()`.  */
	FZ_FUNCTION int fz_search_page_number_cb(const FzDocument& doc, int number, const char *needle, ::fz_search_callback_fn *cb, void *opaque);

	/** Class-aware wrapper for `::fz_search_stext_page()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_search_stext_page(::fz_stext_page *text, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
	 */
	/**
		Search for occurrence of 'needle' in text page.
	
		Return the number of quads and store hit quads in the passed in
		array.
	
		NOTE: This is an experimental interface and subject to change
		without notice.
	*/
	FZ_FUNCTION int fz_search_stext_page(const FzStextPage& text, const char *needle, int *hit_mark, FzQuad& hit_bbox, int hit_max);

	/** Class-aware wrapper for `::fz_search_stext_page_cb()`.  */
	/**
		Search for occurrence of 'needle' in text page.
	
		Call callback once for each hit. This callback will receive
		(potentially) multiple quads for each hit.
	
		Returns the number of hits - note that this is potentially
		different from (i.e. is not greater than) the number of quads
		as returned by the non callback API.
	
		NOTE: This is an experimental interface and subject to change
		without notice.
	*/
	FZ_FUNCTION int fz_search_stext_page_cb(const FzStextPage& text, const char *needle, ::fz_search_callback_fn *cb, void *opaque);

	/** Class-aware wrapper for `::fz_seek()`.  */
	/**
		Seek within a stream.
	
		stm: The stream to seek within.
	
		offset: The offset to seek to.
	
		whence: From where the offset is measured (see fseek).
		SEEK_SET - start of stream.
		SEEK_CUR - current position.
		SEEK_END - end of stream.
	
	*/
	FZ_FUNCTION void fz_seek(const FzStream& stm, int64_t offset, int whence);

	/** Class-aware wrapper for `::fz_seek_output()`.  */
	/**
		Seek to the specified file position.
		See fseek for arguments.
	
		Throw an error on unseekable outputs.
	*/
	FZ_FUNCTION void fz_seek_output(const FzOutput& out, int64_t off, int whence);

	/** Class-aware wrapper for `::fz_segment_stext_page()`.  */
	/**
		Perform segmentation analysis on an (unstructured) page to look for
		recursive subdivisions.
	
		Essentially this code attempts to split the page horizontally and/or
		vertically repeatedly into smaller and smaller "segments" (divisions).
	
		Returns 0 if no changes were made to the document.
	
		This is experimental code, and may change (or be removed) in future
		versions!
	*/
	FZ_FUNCTION int fz_segment_stext_page(const FzStextPage& page);

	/** Class-aware wrapper for `::fz_separation_current_behavior()`.  */
	/**
		Test for the current behavior of a separation.
	*/
	FZ_FUNCTION ::fz_separation_behavior fz_separation_current_behavior(const FzSeparations& sep, int separation);

	/** Class-aware wrapper for `::fz_separation_equivalent()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_separation_equivalent(const ::fz_separations *seps, int idx, ::fz_colorspace *dst_cs, ::fz_colorspace *prf, ::fz_color_params color_params)` => float dst_color
	 */
	/**
		Get the equivalent separation color in a given colorspace.
	*/
	FZ_FUNCTION void fz_separation_equivalent(const FzSeparations& seps, int idx, const FzColorspace& dst_cs, float *dst_color, const FzColorspace& prf, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_separation_name()`.  */
	FZ_FUNCTION const char *fz_separation_name(const FzSeparations& sep, int separation);

	/** Class-aware wrapper for `::fz_set_aa_level()`.  */
	/**
		Set the number of bits of antialiasing we should
		use (for both text and graphics).
	
		bits: The number of bits of antialiasing to use (values are
		clamped to within the 0 to 8 range).
	*/
	FZ_FUNCTION void fz_set_aa_level(int bits);

	/** Class-aware wrapper for `::fz_set_compressed_image_buffer()`.  */
	FZ_FUNCTION void fz_set_compressed_image_buffer(const FzCompressedImage& cimg, const FzCompressedBuffer& buf);

	/** Class-aware wrapper for `::fz_set_default_cmyk()`.  */
	FZ_FUNCTION void fz_set_default_cmyk(const FzDefaultColorspaces& default_cs, const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_set_default_colorspaces()`.  */
	FZ_FUNCTION void fz_set_default_colorspaces(const FzDevice& dev, const FzDefaultColorspaces& default_cs);

	/** Class-aware wrapper for `::fz_set_default_gray()`.  */
	/**
		Set new defaults within the default colorspace structure.
	
		New references are taken to the new default, and references to
		the old defaults dropped.
	
		Never throws exceptions.
	*/
	FZ_FUNCTION void fz_set_default_gray(const FzDefaultColorspaces& default_cs, const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_set_default_output_intent()`.  */
	FZ_FUNCTION void fz_set_default_output_intent(const FzDefaultColorspaces& default_cs, const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_set_default_rgb()`.  */
	FZ_FUNCTION void fz_set_default_rgb(const FzDefaultColorspaces& default_cs, const FzColorspace& cs);

	/** Class-aware wrapper for `::fz_set_error_callback()`.  */
	/**
		Set the error callback. This will be called as part of the
		exception handling.
	
		The callback must not throw exceptions!
	*/
	FZ_FUNCTION void fz_set_error_callback(::fz_error_cb *error_cb, void *user);

	/** Class-aware wrapper for `::fz_set_font_bbox()`.  */
	/**
		Set the font bbox.
	
		font: The font to set the bbox for.
	
		xmin, ymin, xmax, ymax: The bounding box.
	*/
	FZ_FUNCTION void fz_set_font_bbox(const FzFont& font, float xmin, float ymin, float xmax, float ymax);

	/** Class-aware wrapper for `::fz_set_font_embedding()`.  */
	/**
		Control whether a given font should be embedded or not when writing.
	*/
	FZ_FUNCTION void fz_set_font_embedding(const FzFont& font, int embed);

	/** Class-aware wrapper for `::fz_set_graphics_aa_level()`.  */
	/**
		Set the number of bits of antialiasing we
		should use for graphics.
	
		bits: The number of bits of antialiasing to use (values are
		clamped to within the 0 to 8 range).
	*/
	FZ_FUNCTION void fz_set_graphics_aa_level(int bits);

	/** Class-aware wrapper for `::fz_set_graphics_min_line_width()`.  */
	/**
		Set the minimum line width to be
		used for stroked lines.
	
		min_line_width: The minimum line width to use (in pixels).
	*/
	FZ_FUNCTION void fz_set_graphics_min_line_width(float min_line_width);

	/** Class-aware wrapper for `::fz_set_link_rect()`.  */
	FZ_FUNCTION void fz_set_link_rect(const FzLink& link, const FzRect& rect);

	/** Class-aware wrapper for `::fz_set_link_uri()`.  */
	FZ_FUNCTION void fz_set_link_uri(const FzLink& link, const char *uri);

	/** Class-aware wrapper for `::fz_set_metadata()`.  */
	FZ_FUNCTION void fz_set_metadata(const FzDocument& doc, const char *key, const char *value);

	/** Class-aware wrapper for `::fz_set_pixmap_image_tile()`.  */
	FZ_FUNCTION void fz_set_pixmap_image_tile(const FzPixmapImage& cimg, const FzPixmap& pix);

	/** Class-aware wrapper for `::fz_set_pixmap_resolution()`.  */
	/**
		Set the pixels per inch resolution of the pixmap.
	*/
	FZ_FUNCTION void fz_set_pixmap_resolution(const FzPixmap& pix, int xres, int yres);

	/** Class-aware wrapper for `::fz_set_separation_behavior()`.  */
	/**
		Control the rendering of a given separation.
	*/
	FZ_FUNCTION void fz_set_separation_behavior(const FzSeparations& sep, int separation, ::fz_separation_behavior behavior);

	/** Class-aware wrapper for `::fz_set_stddbg()`.  */
	/**
		Set the output stream to be used for fz_stddbg. Set to NULL to
		reset to default (stderr).
	*/
	FZ_FUNCTION void fz_set_stddbg(const FzOutput& out);

	/** Class-aware wrapper for `::fz_set_text_aa_level()`.  */
	/**
		Set the number of bits of antialiasing we
		should use for text.
	
		bits: The number of bits of antialiasing to use (values are
		clamped to within the 0 to 8 range).
	*/
	FZ_FUNCTION void fz_set_text_aa_level(int bits);

	/** Class-aware wrapper for `::fz_set_use_document_css()`.  */
	/**
		Toggle whether to respect document styles in HTML and EPUB.
	*/
	FZ_FUNCTION void fz_set_use_document_css(int use);

	/** Class-aware wrapper for `::fz_set_user_context()`.  */
	/**
		Set the user field in the context.
	
		NULL initially, this field can be set to any opaque value
		required by the user. It is copied on clones.
	*/
	FZ_FUNCTION void fz_set_user_context(void *user);

	/** Class-aware wrapper for `::fz_set_user_css()`.  */
	/**
		Set the user stylesheet source text for use with HTML and EPUB.
	*/
	FZ_FUNCTION void fz_set_user_css(const char *text);

	/** Class-aware wrapper for `::fz_set_warning_callback()`.  */
	/**
		Set the warning callback. This will be called as part of the
		exception handling.
	
		The callback must not throw exceptions!
	*/
	FZ_FUNCTION void fz_set_warning_callback(::fz_warning_cb *warning_cb, void *user);

	/** Class-aware wrapper for `::fz_sha256_final()`.  */
	/**
		MD5 finalization. Ends an MD5 message-digest operation, writing
		the message digest and zeroizing the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha256_final(const FzSha256& state, unsigned char digest[32]);

	/** Class-aware wrapper for `::fz_sha256_init()`.  */
	/**
		SHA256 initialization. Begins an SHA256 operation, initialising
		the supplied context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha256_init(const FzSha256& state);

	/** Class-aware wrapper for `::fz_sha256_update()`.  */
	/**
		SHA256 block update operation. Continues an SHA256 message-
		digest operation, processing another message block, and updating
		the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha256_update(const FzSha256& state, const unsigned char *input, size_t inlen);

	/** Class-aware wrapper for `::fz_sha384_final()`.  */
	/**
		SHA384 finalization. Ends an SHA384 message-digest operation,
		writing the message digest and zeroizing the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha384_final(const FzSha512& state, unsigned char digest[64]);

	/** Class-aware wrapper for `::fz_sha384_init()`.  */
	/**
		SHA384 initialization. Begins an SHA384 operation, initialising
		the supplied context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha384_init(const FzSha512& state);

	/** Class-aware wrapper for `::fz_sha384_update()`.  */
	/**
		SHA384 block update operation. Continues an SHA384 message-
		digest operation, processing another message block, and updating
		the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha384_update(const FzSha512& state, const unsigned char *input, size_t inlen);

	/** Class-aware wrapper for `::fz_sha512_final()`.  */
	/**
		SHA512 finalization. Ends an SHA512 message-digest operation,
		writing the message digest and zeroizing the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha512_final(const FzSha512& state, unsigned char digest[64]);

	/** Class-aware wrapper for `::fz_sha512_init()`.  */
	/**
		SHA512 initialization. Begins an SHA512 operation, initialising
		the supplied context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha512_init(const FzSha512& state);

	/** Class-aware wrapper for `::fz_sha512_update()`.  */
	/**
		SHA512 block update operation. Continues an SHA512 message-
		digest operation, processing another message block, and updating
		the context.
	
		Never throws an exception.
	*/
	FZ_FUNCTION void fz_sha512_update(const FzSha512& state, const unsigned char *input, size_t inlen);

	/** Class-aware wrapper for `::fz_shear()`.  */
	/**
		Create a shearing matrix.
	
		The returned matrix is of the form [ 1 sy sx 1 0 0 ].
	
		m: pointer to place to store returned matrix
	
		sx, sy: Shearing factors. A shearing factor of 0.0 will not
		cause any shearing along the relevant axis.
	
		Returns m.
	*/
	FZ_FUNCTION FzMatrix fz_shear(float sx, float sy);

	/** Class-aware wrapper for `::fz_show_glyph()`.  */
	/**
		Add a glyph/unicode value to a text object.
	
		text: Text object to add to.
	
		font: The font the glyph should be added in.
	
		trm: The transform to use for the glyph.
	
		glyph: The glyph id to add.
	
		unicode: The unicode character for the glyph.
	
		cid: The CJK CID value or raw character code.
	
		wmode: 1 for vertical mode, 0 for horizontal.
	
		bidi_level: The bidirectional level for this glyph.
	
		markup_dir: The direction of the text as specified in the
		markup.
	
		language: The language in use (if known, 0 otherwise)
		(e.g. FZ_LANG_zh_Hans).
	
		Throws exception on failure to allocate.
	*/
	FZ_FUNCTION void fz_show_glyph(const FzText& text, const FzFont& font, const FzMatrix& trm, int glyph, int unicode, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language language);

	/** Class-aware wrapper for `::fz_show_glyph_aux()`.  */
	FZ_FUNCTION void fz_show_glyph_aux(const FzText& text, const FzFont& font, const FzMatrix& trm, float adv, int glyph, int unicode, int cid, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language lang);

	/** Class-aware wrapper for `::fz_show_string()`.  */
	/**
		Add a UTF8 string to a text object.
	
		text: Text object to add to.
	
		font: The font the string should be added in.
	
		trm: The transform to use.
	
		s: The utf-8 string to add.
	
		wmode: 1 for vertical mode, 0 for horizontal.
	
		bidi_level: The bidirectional level for this glyph.
	
		markup_dir: The direction of the text as specified in the markup.
	
		language: The language in use (if known, 0 otherwise)
			(e.g. FZ_LANG_zh_Hans).
	
		Returns the transform updated with the advance width of the
		string.
	*/
	FZ_FUNCTION FzMatrix fz_show_string(const FzText& text, const FzFont& font, const FzMatrix& trm, const char *s, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language language);

	/** Class-aware wrapper for `::fz_shrink_store()`.  */
	/**
		Evict items from the store until the total size of
		the objects in the store is reduced to a given percentage of its
		current size.
	
		percent: %age of current size to reduce the store to.
	
		Returns non zero if we managed to free enough memory, zero
		otherwise.
	*/
	FZ_FUNCTION int fz_shrink_store(unsigned int percent);

	/** Class-aware wrapper for `::fz_skip()`.  */
	/**
		Read from a stream discarding data.
	
		stm: The stream to read from.
	
		len: The number of bytes to read.
	
		Returns the number of bytes read. May throw exceptions.
	*/
	FZ_FUNCTION size_t fz_skip(const FzStream& stm, size_t len);

	/** Class-aware wrapper for `::fz_skip_space()`.  */
	/**
		Skip over whitespace (bytes <= 32) in a stream.
	*/
	FZ_FUNCTION void fz_skip_space(const FzStream& stm);

	/** Class-aware wrapper for `::fz_skip_string()`.  */
	/**
		Skip over a given string in a stream. Return 0 if successfully
		skipped, non-zero otherwise. As many characters will be skipped
		over as matched in the string.
	*/
	FZ_FUNCTION int fz_skip_string(const FzStream& stm, const char *str);

	/** Class-aware wrapper for `::fz_slice_buffer()`.  */
	/**
		Create a new buffer with a (subset of) the data from the buffer.
	
		start: if >= 0, offset from start of buffer, if < 0 offset from end of buffer.
	
		end: if >= 0, offset from start of buffer, if < 0 offset from end of buffer.
	
	*/
	FZ_FUNCTION FzBuffer fz_slice_buffer(const FzBuffer& buf, int64_t start, int64_t end);

	/** Class-aware wrapper for `::fz_snap_selection()`.  */
	FZ_FUNCTION FzQuad fz_snap_selection(const FzStextPage& page, FzPoint& ap, FzPoint& bp, int mode);

	/** Class-aware wrapper for `::fz_start_throw_on_repair()`.  */
	FZ_FUNCTION void fz_start_throw_on_repair();

	/** Class-aware wrapper for `::fz_stat_ctime()`.  */
	FZ_FUNCTION int64_t fz_stat_ctime(const char *path);

	/** Class-aware wrapper for `::fz_stat_mtime()`.  */
	FZ_FUNCTION int64_t fz_stat_mtime(const char *path);

	/* Class-aware wrapper for `fz_stddbg()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_stderr()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/* Class-aware wrapper for `fz_stdout()`
	is not available because returned wrapper class for `fz_output`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_store_item()`.  */
	/**
		Add an item to the store.
	
		Add an item into the store, returning NULL for success. If an
		item with the same key is found in the store, then our item will
		not be inserted, and the function will return a pointer to that
		value instead. This function takes its own reference to val, as
		required (i.e. the caller maintains ownership of its own
		reference).
	
		key: The key used to index the item.
	
		val: The value to store.
	
		itemsize: The size in bytes of the value (as counted towards the
		store size).
	
		type: Functions used to manipulate the key.
	*/
	FZ_FUNCTION void *fz_store_item(void *key, void *val, size_t itemsize, const FzStoreType& type);

	/** Class-aware wrapper for `::fz_store_scavenge()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_store_scavenge(size_t size)` => `(int, int phase)`
	 */
	/**
		Internal function used as part of the scavenging
		allocator; when we fail to allocate memory, before returning a
		failure to the caller, we try to scavenge space within the store
		by evicting at least 'size' bytes. The allocator then retries.
	
		size: The number of bytes we are trying to have free.
	
		phase: What phase of the scavenge we are in. Updated on exit.
	
		Returns non zero if we managed to free any memory.
	*/
	FZ_FUNCTION int fz_store_scavenge(size_t size, int *phase);

	/** Class-aware wrapper for `::fz_store_scavenge_external()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_store_scavenge_external(size_t size)` => `(int, int phase)`
	 */
	/**
		External function for callers to use
		to scavenge while trying allocations.
	
		size: The number of bytes we are trying to have free.
	
		phase: What phase of the scavenge we are in. Updated on exit.
	
		Returns non zero if we managed to free any memory.
	*/
	FZ_FUNCTION int fz_store_scavenge_external(size_t size, int *phase);

	/** Class-aware wrapper for `::fz_story_document()`.  */
	FZ_FUNCTION FzXml fz_story_document(const FzStory& story);

	/** Class-aware wrapper for `::fz_story_positions()`.  */
	FZ_FUNCTION void fz_story_positions(const FzStory& story, ::fz_story_position_callback *cb, void *arg);

	/** Class-aware wrapper for `::fz_story_warnings()`.  */
	FZ_FUNCTION const char *fz_story_warnings(const FzStory& story);

	/** Class-aware wrapper for `::fz_strcasecmp()`.  */
	/**
		Case insensitive (UTF8) string comparison.
	*/
	FZ_FUNCTION int fz_strcasecmp(const char *a, const char *b);

	/** Class-aware wrapper for `::fz_strdup()`.  */
	/**
		Portable strdup implementation, using fz allocators.
	*/
	FZ_FUNCTION char *fz_strdup(const char *s);

	/** Class-aware wrapper for `::fz_stream_filename()`.  */
	/**
		Return the filename (UTF-8 encoded) from which a stream was opened.
	
		Returns NULL if the filename is not available (or the stream was
		opened from a source other than a file).
	*/
	FZ_FUNCTION const char *fz_stream_filename(const FzStream& stm);

	/** Class-aware wrapper for `::fz_stream_from_output()`.  */
	/**
		Obtain the fz_output in the form of a fz_stream.
	
		This allows data to be read back from some forms of fz_output
		object. When finished reading, the fz_stream should be released
		by calling fz_drop_stream. Until the fz_stream is dropped, no
		further operations should be performed on the fz_output object.
	*/
	FZ_FUNCTION FzStream fz_stream_from_output(const FzOutput& arg_0);

	/** Class-aware wrapper for `::fz_string_from_barcode_type()`.  */
	/**
		Return barcode string matching one of the above barcode types.
		All lowercase, e.g. "none", "aztec" etc.
	*/
	FZ_FUNCTION const char *fz_string_from_barcode_type(::fz_barcode_type type);

	/** Class-aware wrapper for `::fz_string_from_box_type()`.  */
	FZ_FUNCTION const char *fz_string_from_box_type(::fz_box_type box);

	/** Class-aware wrapper for `::fz_string_from_buffer()`.  */
	/**
		Ensure that a buffer's data ends in a
		0 byte, and return a pointer to it.
	*/
	FZ_FUNCTION const char *fz_string_from_buffer(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_string_from_linecap()`.  */
	FZ_FUNCTION const char *fz_string_from_linecap(::fz_linecap cap);

	/** Class-aware wrapper for `::fz_string_from_linejoin()`.  */
	FZ_FUNCTION const char *fz_string_from_linejoin(::fz_linejoin join);

	/** Class-aware wrapper for `::fz_string_from_text_language()`.  */
	/**
		Recover ISO 639 (639-{1,2,3,5}) language specification
		strings losslessly from a 15 bit fz_text_language code.
	
		No validation is carried out. See note above.
	*/
	FZ_FUNCTION char *fz_string_from_text_language(char str[8], ::fz_text_language lang);

	/** Class-aware wrapper for `::fz_string_from_text_language2()`.  */
	/**
	C++ alternative to fz_string_from_text_language() that returns information in a std::string.
	*/
	FZ_FUNCTION std::string fz_string_from_text_language2(::fz_text_language lang);

	/** Class-aware wrapper for `::fz_strlcat()`.  */
	/**
		Concatenate 2 strings, with a maximum length.
	
		dst: pointer to first string in a buffer of n bytes.
	
		src: pointer to string to concatenate.
	
		n: Size (in bytes) of buffer that dst is in.
	
		Returns the real length that a concatenated dst + src would have
		been (not including terminator).
	*/
	FZ_FUNCTION size_t fz_strlcat(char *dst, const char *src, size_t n);

	/** Class-aware wrapper for `::fz_strlcpy()`.  */
	/**
		Copy at most n-1 chars of a string into a destination
		buffer with null termination, returning the real length of the
		initial string (excluding terminator).
	
		dst: Destination buffer, at least n bytes long.
	
		src: C string (non-NULL).
	
		n: Size of dst buffer in bytes.
	
		Returns the length (excluding terminator) of src.
	*/
	FZ_FUNCTION size_t fz_strlcpy(char *dst, const char *src, size_t n);

	/** Class-aware wrapper for `::fz_strncasecmp()`.  */
	/**
		Case insensitive (UTF8) string comparison.
	
		n = maximum number of bytes to read from either a or b.
	*/
	FZ_FUNCTION int fz_strncasecmp(const char *a, const char *b, size_t n);

	/** Class-aware wrapper for `::fz_strnlen()`.  */
	/**
		Return strlen(s), if that is less than maxlen, or maxlen if
		there is no null byte ('\0') among the first maxlen bytes.
	*/
	FZ_FUNCTION size_t fz_strnlen(const char *s, size_t maxlen);

	/** Class-aware wrapper for `::fz_stroke_path()`.  */
	FZ_FUNCTION void fz_stroke_path(const FzDevice& dev, const FzPath& path, const FzStrokeState& stroke, const FzMatrix& ctm, const FzColorspace& colorspace, const float *color, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_stroke_state_eq()`.  */
	FZ_FUNCTION int fz_stroke_state_eq(const FzStrokeState& a, const FzStrokeState& b);

	/** Class-aware wrapper for `::fz_stroke_text()`.  */
	FZ_FUNCTION void fz_stroke_text(const FzDevice& dev, const FzText& text, const FzStrokeState& stroke, const FzMatrix& ctm, const FzColorspace& colorspace, const float *color, float alpha, const FzColorParams& color_params);

	/** Class-aware wrapper for `::fz_strsep()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_strsep(const char *delim)` => `(char *, char *stringp)`
	 */
	/**
		Given a pointer to a C string (or a pointer to NULL) break
		it at the first occurrence of a delimiter char (from a given
		set).
	
		stringp: Pointer to a C string pointer (or NULL). Updated on
		exit to point to the first char of the string after the
		delimiter that was found. The string pointed to by stringp will
		be corrupted by this call (as the found delimiter will be
		overwritten by 0).
	
		delim: A C string of acceptable delimiter characters.
	
		Returns a pointer to a C string containing the chars of stringp
		up to the first delimiter char (or the end of the string), or
		NULL.
	*/
	FZ_FUNCTION char *fz_strsep(char **stringp, const char *delim);

	/** Class-aware wrapper for `::fz_strstr()`.  */
	/**
		Safe strstr function.
	
		haystack: Where to look (may be NULL).
	
		needled: What to look for.
	
		Returns NULL if unmatched, or pointer to start of match.
	*/
	FZ_FUNCTION const char *fz_strstr(const char *haystack, const char *needle);

	/** Class-aware wrapper for `::fz_strstrcase()`.  */
	/**
		Safe case-insensitive strstr function. (Accepts UTF-8).
	
		haystack: Where to look (may be NULL).
	
		needled: What to look for.
	
		Returns NULL if unmatched, or pointer to start of match.
	*/
	FZ_FUNCTION const char *fz_strstrcase(const char *haystack, const char *needle);

	/** Class-aware wrapper for `::fz_strtof()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_strtof(const char *s)` => `(float, char *es)`
	 */
	/**
		Locale-independent decimal to binary conversion. On overflow
		return (-)INFINITY and set errno to ERANGE. On underflow return
		0 and set errno to ERANGE. Special inputs (case insensitive):
		"NAN", "INF" or "INFINITY".
	*/
	FZ_FUNCTION float fz_strtof(const char *s, char **es);

	/** Class-aware wrapper for `::fz_structure_from_string()`.  */
	FZ_FUNCTION ::fz_structure fz_structure_from_string(const char *str);

	/** Class-aware wrapper for `::fz_structure_to_string()`.  */
	FZ_FUNCTION const char *fz_structure_to_string(::fz_structure type);

	/** Class-aware wrapper for `::fz_strverscmp()`.  */
	/**
		portable strverscmp(3) function
	*/
	FZ_FUNCTION int fz_strverscmp(const char *s1, const char *s2);

	/** Class-aware wrapper for `::fz_subpixel_adjust()`.  */
	/**
		Perform subpixel quantisation and adjustment on a glyph matrix.
	
		ctm: On entry, the desired 'ideal' transformation for a glyph.
		On exit, adjusted to a (very similar) transformation quantised
		for subpixel caching.
	
		subpix_ctm: Initialised by the routine to the transform that
		should be used to render the glyph.
	
		qe, qf: which subpixel position we quantised to.
	
		Returns: the size of the glyph.
	
		Note: This is currently only exposed for use in our app. It
		should be considered "at risk" of removal from the API.
	*/
	FZ_FUNCTION float fz_subpixel_adjust(FzMatrix& ctm, FzMatrix& subpix_ctm, unsigned char *qe, unsigned char *qf);

	/** Class-aware wrapper for `::fz_subsample_pixmap()`.  */
	FZ_FUNCTION void fz_subsample_pixmap(const FzPixmap& tile, int factor);

	/** Class-aware wrapper for `::fz_subset_cff_for_gids()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_subset_cff_for_gids(::fz_buffer *orig, int num_gids, int symbolic, int cidfont)` => `(fz_buffer *, int gids)`
	 */
	FZ_FUNCTION FzBuffer fz_subset_cff_for_gids(const FzBuffer& orig, int *gids, int num_gids, int symbolic, int cidfont);

	/** Class-aware wrapper for `::fz_subset_ttf_for_gids()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_subset_ttf_for_gids(::fz_buffer *orig, int num_gids, int symbolic, int cidfont)` => `(fz_buffer *, int gids)`
	 */
	FZ_FUNCTION FzBuffer fz_subset_ttf_for_gids(const FzBuffer& orig, int *gids, int num_gids, int symbolic, int cidfont);

	/** Class-aware wrapper for `::fz_sync_bits()`.  */
	/**
		Called after reading bits to tell the stream
		that we are about to return to reading bytewise. Resyncs
		the stream to whole byte boundaries.
	*/
	FZ_FUNCTION void fz_sync_bits(const FzStream& stm);

	/** Class-aware wrapper for `::fz_table_hunt()`.  */
	/**
		Hunt for possible tables on a page, and update the stext with
		information.
	*/
	FZ_FUNCTION void fz_table_hunt(const FzStextPage& page);

	/** Class-aware wrapper for `::fz_tell()`.  */
	/**
		return the current reading position within a stream
	*/
	FZ_FUNCTION int64_t fz_tell(const FzStream& stm);

	/** Class-aware wrapper for `::fz_tell_output()`.  */
	/**
		Return the current file position.
	
		Throw an error on untellable outputs.
	*/
	FZ_FUNCTION int64_t fz_tell_output(const FzOutput& out);

	/** Class-aware wrapper for `::fz_terminate_buffer()`.  */
	/**
		Zero-terminate buffer in order to use as a C string.
	
		This byte is invisible and does not affect the length of the
		buffer as returned by fz_buffer_storage. The zero byte is
		written *after* the data, and subsequent writes will overwrite
		the terminating byte.
	
		Subsequent changes to the size of the buffer (such as by
		fz_buffer_trim, fz_buffer_grow, fz_resize_buffer, etc) may
		invalidate this.
	*/
	FZ_FUNCTION void fz_terminate_buffer(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_text_aa_level()`.  */
	/**
		Get the number of bits of antialiasing we are
		using for text. Between 0 and 8.
	*/
	FZ_FUNCTION int fz_text_aa_level();

	/** Class-aware wrapper for `::fz_text_language_from_string()`.  */
	/**
		Convert ISO 639 (639-{1,2,3,5}) language specification
		strings losslessly to a 15 bit fz_text_language code.
	
		No validation is carried out. Obviously invalid (out
		of spec) codes will be mapped to FZ_LANG_UNSET, but
		well-formed (but undefined) codes will be blithely
		accepted.
	*/
	FZ_FUNCTION ::fz_text_language fz_text_language_from_string(const char *str);

	/** Class-aware wrapper for `::fz_tint_pixmap()`.  */
	/**
		Tint all the pixels in an RGB, BGR, or Gray pixmap.
	
		black: Map black to this hexadecimal RGB color.
	
		white: Map white to this hexadecimal RGB color.
	*/
	FZ_FUNCTION void fz_tint_pixmap(const FzPixmap& pix, int black, int white);

	/** Class-aware wrapper for `::fz_tolower()`.  */
	/**
		Unicode aware tolower and toupper functions.
	*/
	FZ_FUNCTION int fz_tolower(int c);

	/** Class-aware wrapper for `::fz_toupper()`.  */
	FZ_FUNCTION int fz_toupper(int c);

	/** Class-aware wrapper for `::fz_transform_page()`.  */
	/**
		Create transform matrix to draw page
		at a given resolution and rotation. Adjusts the scaling
		factors so that the page covers whole number of
		pixels and adjust the page origin to be at 0,0.
	*/
	FZ_FUNCTION FzMatrix fz_transform_page(const FzRect& mediabox, float resolution, float rotate);

	/** Class-aware wrapper for `::fz_transform_path()`.  */
	/**
		Transform a path by a given
		matrix.
	
		path: The path to modify (must not be a packed path).
	
		transform: The transform to apply.
	
		Throws exceptions if the path is packed, or on failure
		to allocate.
	*/
	FZ_FUNCTION void fz_transform_path(const FzPath& path, const FzMatrix& transform);

	/** Class-aware wrapper for `::fz_transform_point()`.  */
	/**
		Apply a transformation to a point.
	
		transform: Transformation matrix to apply. See fz_concat,
		fz_scale, fz_rotate and fz_translate for how to create a
		matrix.
	
		point: Pointer to point to update.
	
		Returns transform (unchanged).
	*/
	FZ_FUNCTION FzPoint fz_transform_point(const FzPoint& point, const FzMatrix& m);

	/** Class-aware wrapper for `::fz_transform_point_xy()`.  */
	FZ_FUNCTION FzPoint fz_transform_point_xy(float x, float y, const FzMatrix& m);

	/** Class-aware wrapper for `::fz_transform_quad()`.  */
	/**
		Transform a quad by a matrix.
	*/
	FZ_FUNCTION FzQuad fz_transform_quad(const FzQuad& q, const FzMatrix& m);

	/** Class-aware wrapper for `::fz_transform_rect()`.  */
	/**
		Apply a transform to a rectangle.
	
		After the four corner points of the axis-aligned rectangle
		have been transformed it may not longer be axis-aligned. So a
		new axis-aligned rectangle is created covering at least the
		area of the transformed rectangle.
	
		transform: Transformation matrix to apply. See fz_concat,
		fz_scale and fz_rotate for how to create a matrix.
	
		rect: Rectangle to be transformed. The two special cases
		fz_empty_rect and fz_infinite_rect, may be used but are
		returned unchanged as expected.
	*/
	FZ_FUNCTION FzRect fz_transform_rect(const FzRect& rect, const FzMatrix& m);

	/** Class-aware wrapper for `::fz_transform_vector()`.  */
	/**
		Apply a transformation to a vector.
	
		transform: Transformation matrix to apply. See fz_concat,
		fz_scale and fz_rotate for how to create a matrix. Any
		translation will be ignored.
	
		vector: Pointer to vector to update.
	*/
	FZ_FUNCTION FzPoint fz_transform_vector(const FzPoint& vector, const FzMatrix& m);

	/** Class-aware wrapper for `::fz_translate()`.  */
	/**
		Create a translation matrix.
	
		The returned matrix is of the form [ 1 0 0 1 tx ty ].
	
		m: A place to store the created matrix.
	
		tx, ty: Translation distances along the X- and Y-axes. A
		translation of 0 will not cause any translation along the
		relevant axis.
	
		Returns m.
	*/
	FZ_FUNCTION FzMatrix fz_translate(float tx, float ty);

	/** Class-aware wrapper for `::fz_translate_irect()`.  */
	FZ_FUNCTION FzIrect fz_translate_irect(const FzIrect& a, int xoff, int yoff);

	/** Class-aware wrapper for `::fz_translate_rect()`.  */
	/**
		Translate bounding box.
	
		Translate a bbox by a given x and y offset. Allows for overflow.
	*/
	FZ_FUNCTION FzRect fz_translate_rect(const FzRect& a, float xoff, float yoff);

	/** Class-aware wrapper for `::fz_tree_archive_add_buffer()`.  */
	/**
		Add a named buffer to an existing tree archive.
	
		The tree will take a new reference to the buffer. Ownership
		is not transferred.
	*/
	FZ_FUNCTION void fz_tree_archive_add_buffer(const FzArchive& arch_, const char *name, const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_tree_archive_add_data()`.  */
	/**
		Add a named block of data to an existing tree archive.
	
		The data will be copied into a buffer, and so the caller
		may free it as soon as this returns.
	*/
	FZ_FUNCTION void fz_tree_archive_add_data(const FzArchive& arch_, const char *name, const void *data, size_t size);

	/* Class-aware wrapper for `fz_tree_insert()`
	is not available because returned wrapper class for `fz_tree`
	is non-copyable. */

	/** Class-aware wrapper for `::fz_tree_lookup()`.  */
	/**
		Look for the value of a node in the tree with the given key.
	
		Simple pointer equivalence is used for key.
	
		Returns NULL for no match.
	*/
	FZ_FUNCTION void *fz_tree_lookup(const FzTree& node, const char *key);

	/** Class-aware wrapper for `::fz_trim_buffer()`.  */
	/**
		Trim wasted capacity from a buffer by resizing internal memory.
	*/
	FZ_FUNCTION void fz_trim_buffer(const FzBuffer& buf);

	/** Class-aware wrapper for `::fz_trim_path()`.  */
	/**
		Minimise the internal storage used by a path.
	
		As paths are constructed, the internal buffers
		grow. To avoid repeated reallocations they
		grow with some spare space. Once a path has
		been fully constructed, this call allows the
		excess space to be trimmed.
	*/
	FZ_FUNCTION void fz_trim_path(const FzPath& path);

	/** Class-aware wrapper for `::fz_truncate_output()`.  */
	/**
		Truncate the output at the current position.
	
		This allows output streams which have seeked back from the end
		of their storage to be truncated at the current point.
	*/
	FZ_FUNCTION void fz_truncate_output(const FzOutput& arg_0);

	/** Class-aware wrapper for `::fz_try_invert_matrix()`.  */
	/**
		Attempt to create an inverse matrix.
	
		inv: Place to store inverse matrix.
	
		src: Matrix to invert. A degenerate matrix, where the
		determinant is equal to zero, can not be inverted.
	
		Returns 1 if matrix is degenerate (singular), or 0 otherwise.
	*/
	FZ_FUNCTION int fz_try_invert_matrix(FzMatrix& inv, const FzMatrix& src);

	/** Class-aware wrapper for `::fz_try_open_archive_entry()`.  */
	/**
		Opens an archive entry as a stream.
	
		Returns NULL if a matching entry cannot be found, otherwise
		behaves exactly as fz_open_archive_entry.
	*/
	FZ_FUNCTION FzStream fz_try_open_archive_entry(const FzArchive& arch, const char *name);

	/** Class-aware wrapper for `::fz_try_open_archive_with_stream()`.  */
	/**
		Open zip or tar archive stream.
	
		Does the same as fz_open_archive_with_stream, but will not throw
		an error in the event of failing to recognise the format. Will
		still throw errors in other cases though!
	*/
	FZ_FUNCTION FzArchive fz_try_open_archive_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::fz_try_open_file()`.  */
	/**
		Open the named file and wrap it in a stream.
	
		Does the same as fz_open_file, but in the event the file
		does not open, it will return NULL rather than throw an
		exception.
	*/
	FZ_FUNCTION FzStream fz_try_open_file(const char *name);

	/** Class-aware wrapper for `::fz_try_parse_xml_archive_entry()`.  */
	/**
		Try and parse the contents of an archive entry into a tree of xml nodes.
	
		preserve_white: whether to keep or delete all-whitespace nodes.
	
		Will return NULL if the archive entry can't be found. Otherwise behaves
		the same as fz_parse_xml_archive_entry. May throw exceptions.
	*/
	FZ_FUNCTION FzXml fz_try_parse_xml_archive_entry(const FzArchive& dir, const char *filename, int preserve_white);

	/** Class-aware wrapper for `::fz_try_read_archive_entry()`.  */
	/**
		Reads all bytes in an archive entry
		into a buffer.
	
		name: Entry name to look for, this must be an exact match to
		the entry name in the archive.
	
		Returns NULL if a matching entry cannot be found. Otherwise behaves
		the same as fz_read_archive_entry. Exceptions may be thrown.
	*/
	FZ_FUNCTION FzBuffer fz_try_read_archive_entry(const FzArchive& arch, const char *name);

	/** Class-aware wrapper for `::fz_try_read_file()`.  */
	/**
		Read all the contents of a file into a buffer.
	
		Returns NULL if the file does not exist, otherwise
		behaves exactly as fz_read_file.
	*/
	FZ_FUNCTION FzBuffer fz_try_read_file(const char *filename);

	/** Class-aware wrapper for `::fz_tune_image_decode()`.  */
	/**
		Set the tuning function to use for
		image decode.
	
		image_decode: Function to use.
	
		arg: Opaque argument to be passed to tuning function.
	*/
	FZ_FUNCTION void fz_tune_image_decode(::fz_tune_image_decode_fn *image_decode, void *arg);

	/** Class-aware wrapper for `::fz_tune_image_scale()`.  */
	/**
		Set the tuning function to use for
		image scaling.
	
		image_scale: Function to use.
	
		arg: Opaque argument to be passed to tuning function.
	*/
	FZ_FUNCTION void fz_tune_image_scale(::fz_tune_image_scale_fn *image_scale, void *arg);

	/** Class-aware wrapper for `::fz_unicode_from_glyph_name()`.  */
	FZ_FUNCTION int fz_unicode_from_glyph_name(const char *name);

	/** Class-aware wrapper for `::fz_unicode_from_glyph_name_strict()`.  */
	FZ_FUNCTION int fz_unicode_from_glyph_name_strict(const char *name);

	/** Class-aware wrapper for `::fz_union_rect()`.  */
	/**
		Compute union of two rectangles.
	
		Given two rectangles, update the first to be the smallest
		axis-aligned rectangle that encompasses both given rectangles.
		If either rectangle is infinite then the union is also infinite.
		If either rectangle is empty then the union is simply the
		non-empty rectangle. Should both rectangles be empty, then the
		union is also empty.
	*/
	FZ_FUNCTION FzRect fz_union_rect(const FzRect& a, const FzRect& b);

	/** Class-aware wrapper for `::fz_unlock()`.  */
	/**
		Unlock one of the user supplied mutexes.
	*/
	FZ_FUNCTION void fz_unlock(int lock);

	/** Class-aware wrapper for `::fz_unpack_stream()`.  */
	FZ_FUNCTION FzStream fz_unpack_stream(const FzStream& src, int depth, int w, int h, int n, int indexed, int pad, int skip);

	/** Class-aware wrapper for `::fz_unread_byte()`.  */
	/**
		Unread the single last byte successfully
		read from a stream. Do not call this without having
		successfully read a byte.
	
		stm: The stream to operate upon.
	*/
	FZ_FUNCTION void fz_unread_byte(const FzStream& stm);

	/** Class-aware wrapper for `::fz_unshare_stroke_state()`.  */
	/**
		Given a reference to a (possibly) shared stroke_state structure,
		return a reference to an equivalent stroke_state structure
		that is guaranteed to be unshared (i.e. one that can
		safely be modified).
	
		shared: The reference to a (possibly) shared structure
		to unshare. Ownership of this reference is passed in
		to this function, even in the case of exceptions being
		thrown.
	
		Exceptions may be thrown in the event of failure to
		allocate if required.
	*/
	FZ_FUNCTION FzStrokeState fz_unshare_stroke_state(const FzStrokeState& shared);

	/** Class-aware wrapper for `::fz_unshare_stroke_state_with_dash_len()`.  */
	/**
		Given a reference to a (possibly) shared stroke_state structure,
		return a reference to a stroke_state structure (with room for a
		given amount of dash data) that is guaranteed to be unshared
		(i.e. one that can safely be modified).
	
		shared: The reference to a (possibly) shared structure
		to unshare. Ownership of this reference is passed in
		to this function, even in the case of exceptions being
		thrown.
	
		Exceptions may be thrown in the event of failure to
		allocate if required.
	*/
	FZ_FUNCTION FzStrokeState fz_unshare_stroke_state_with_dash_len(const FzStrokeState& shared, int len);

	/** Class-aware wrapper for `::fz_urldecode()`.  */
	/**
		Like fz_decode_uri_component but in-place.
	*/
	FZ_FUNCTION char *fz_urldecode(char *url);

	/** Class-aware wrapper for `::fz_use_document_css()`.  */
	/**
		Return whether to respect document styles in HTML and EPUB.
	*/
	FZ_FUNCTION int fz_use_document_css();

	/** Class-aware wrapper for `::fz_user_context()`.  */
	/**
		Read the user field from the context.
	*/
	FZ_FUNCTION void *fz_user_context();

	/** Class-aware wrapper for `::fz_user_css()`.  */
	/**
		Get the user stylesheet source text.
	*/
	FZ_FUNCTION const char *fz_user_css();

	/** Class-aware wrapper for `::fz_utflen()`.  */
	/**
		Count how many runes the UTF-8 encoded string
		consists of.
	
		s: The UTF-8 encoded, NUL-terminated text string.
	
		Returns the number of runes in the string.
	*/
	FZ_FUNCTION int fz_utflen(const char *s);

	/** Class-aware wrapper for `::fz_var_imp()`.  */
	FZ_FUNCTION void fz_var_imp(void *arg_0);

	/** Class-aware wrapper for `::fz_vlog_error_printf()`.  */
	FZ_FUNCTION void fz_vlog_error_printf(const char *fmt, va_list ap);

	/** Class-aware wrapper for `::fz_vsnprintf()`.  */
	/**
		A vsnprintf work-alike, using our custom formatter.
	*/
	FZ_FUNCTION size_t fz_vsnprintf(char *buffer, size_t space, const char *fmt, va_list args);

	/** Class-aware wrapper for `::fz_vthrow()`.  */
	/**
		Throw an exception.
	
		This assumes an enclosing fz_try() block within the callstack.
	*/
	FZ_FUNCTION void fz_vthrow(int errcode, const char *arg_1, va_list ap);

	/** Class-aware wrapper for `::fz_vwarn()`.  */
	/**
		Log a warning.
	
		This goes to the registered warning stream (stderr by
		default).
	*/
	FZ_FUNCTION void fz_vwarn(const char *fmt, va_list ap);

	/** Class-aware wrapper for `::fz_walk_path()`.  */
	/**
		Walk the segments of a path, calling the
		appropriate callback function from a given set for each
		segment of the path.
	
		path: The path to walk.
	
		walker: The set of callback functions to use. The first
		4 callback pointers in the set must be non-NULL. The
		subsequent ones can either be supplied, or can be left
		as NULL, in which case the top 4 functions will be
		called as appropriate to simulate them.
	
		arg: An opaque argument passed in to each callback.
	
		Exceptions will only be thrown if the underlying callback
		functions throw them.
	*/
	FZ_FUNCTION void fz_walk_path(const FzPath& path, const FzPathWalker& walker, void *arg);

	/** Class-aware wrapper for `::fz_warn()`.  */
	FZ_FUNCTION void fz_warn(const char *fmt, ...);

	/** Class-aware wrapper for `::fz_warning_callback()`.
	
	This function has out-params. Python/C# wrappers look like:
		`fz_warning_callback()` => `(fz_warning_cb *, void *user)`
	 */
	/**
		Retrieve the currently set warning callback, or NULL if none
		has been set. Optionally, if user is non-NULL, the user pointer
		given when the warning callback was set is also passed back to
		the caller.
	*/
	FZ_FUNCTION ::fz_warning_cb *fz_warning_callback(void **user);

	/** Class-aware wrapper for `::fz_warp_pixmap()`.  */
	FZ_FUNCTION FzPixmap fz_warp_pixmap(const FzPixmap& src, const FzQuad& points, int width, int height);

	/** Class-aware wrapper for `::fz_windows_1250_from_unicode()`.  */
	FZ_FUNCTION int fz_windows_1250_from_unicode(int u);

	/** Class-aware wrapper for `::fz_windows_1251_from_unicode()`.  */
	FZ_FUNCTION int fz_windows_1251_from_unicode(int u);

	/** Class-aware wrapper for `::fz_windows_1252_from_unicode()`.  */
	FZ_FUNCTION int fz_windows_1252_from_unicode(int u);

	/** Class-aware wrapper for `::fz_write_band()`.  */
	/**
		Cause a band writer to write the next band
		of data for an image.
	
		stride: The byte offset from the first byte of the data
		for a pixel to the first byte of the data for the same pixel
		on the row below.
	
		band_height: The number of lines in this band.
	
		samples: Pointer to first byte of the data.
	*/
	FZ_FUNCTION void fz_write_band(const FzBandWriter& writer, int stride, int band_height, const unsigned char *samples);

	/** Class-aware wrapper for `::fz_write_base64()`.  */
	/**
		Write a base64 encoded data block, optionally with periodic
		newlines.
	*/
	FZ_FUNCTION void fz_write_base64(const FzOutput& out, const unsigned char *data, size_t size, int newline);

	/** Class-aware wrapper for `::fz_write_base64_buffer()`.  */
	/**
		Write a base64 encoded fz_buffer, optionally with periodic
		newlines.
	*/
	FZ_FUNCTION void fz_write_base64_buffer(const FzOutput& out, const FzBuffer& data, int newline);

	/** Class-aware wrapper for `::fz_write_bitmap_as_pbm()`.  */
	/**
		Write a bitmap as a pbm.
	*/
	FZ_FUNCTION void fz_write_bitmap_as_pbm(const FzOutput& out, const FzBitmap& bitmap);

	/** Class-aware wrapper for `::fz_write_bitmap_as_pcl()`.  */
	/**
		Write a bitmap as mono PCL.
	*/
	FZ_FUNCTION void fz_write_bitmap_as_pcl(const FzOutput& out, const FzBitmap& bitmap, const FzPclOptions& pcl);

	/** Class-aware wrapper for `::fz_write_bitmap_as_pkm()`.  */
	/**
		Write a CMYK bitmap as a pkm.
	*/
	FZ_FUNCTION void fz_write_bitmap_as_pkm(const FzOutput& out, const FzBitmap& bitmap);

	/** Class-aware wrapper for `::fz_write_bitmap_as_pwg()`.  */
	/**
		Write a bitmap as a PWG.
	*/
	FZ_FUNCTION void fz_write_bitmap_as_pwg(const FzOutput& out, const FzBitmap& bitmap, FzPwgOptions& pwg);

	/** Class-aware wrapper for `::fz_write_bitmap_as_pwg_page()`.  */
	/**
		Write a bitmap as a PWG page.
	
		Caller should provide a file header by calling
		fz_write_pwg_file_header, but can then write several pages to
		the same file.
	*/
	FZ_FUNCTION void fz_write_bitmap_as_pwg_page(const FzOutput& out, const FzBitmap& bitmap, FzPwgOptions& pwg);

	/** Class-aware wrapper for `::fz_write_bits()`.  */
	/**
		Write num_bits of data to the end of the output stream, assumed to be packed
		most significant bits first.
	*/
	FZ_FUNCTION void fz_write_bits(const FzOutput& out, unsigned int data, int num_bits);

	/** Class-aware wrapper for `::fz_write_bits_sync()`.  */
	/**
		Sync to byte boundary after writing bits.
	*/
	FZ_FUNCTION void fz_write_bits_sync(const FzOutput& out);

	/** Class-aware wrapper for `::fz_write_buffer()`.  */
	FZ_FUNCTION void fz_write_buffer(const FzOutput& out, const FzBuffer& data);

	/** Class-aware wrapper for `::fz_write_byte()`.  */
	FZ_FUNCTION void fz_write_byte(const FzOutput& out, unsigned char x);

	/** Class-aware wrapper for `::fz_write_char()`.  */
	FZ_FUNCTION void fz_write_char(const FzOutput& out, char x);

	/** Class-aware wrapper for `::fz_write_data()`.  */
	/**
		Write data to output.
	
		data: Pointer to data to write.
		size: Size of data to write in bytes.
	*/
	FZ_FUNCTION void fz_write_data(const FzOutput& out, const void *data, size_t size);

	/** Class-aware wrapper for `::fz_write_document()`.  */
	/**
		Convenience function to feed all the pages of a document to
		fz_begin_page/fz_run_page/fz_end_page.
	*/
	FZ_FUNCTION void fz_write_document(const FzDocumentWriter& wri, const FzDocument& doc);

	/** Class-aware wrapper for `::fz_write_float_be()`.  */
	FZ_FUNCTION void fz_write_float_be(const FzOutput& out, float f);

	/** Class-aware wrapper for `::fz_write_float_le()`.  */
	FZ_FUNCTION void fz_write_float_le(const FzOutput& out, float f);

	/** Class-aware wrapper for `::fz_write_header()`.  */
	/**
		Cause a band writer to write the header for
		a banded image with the given properties/dimensions etc. This
		also configures the bandwriter for the format of the data to be
		passed in future calls.
	
		w, h: Width and Height of the entire page.
	
		n: Number of components (including spots and alphas).
	
		alpha: Number of alpha components.
	
		xres, yres: X and Y resolutions in dpi.
	
		cs: Colorspace (NULL for bitmaps)
	
		seps: Separation details (or NULL).
	*/
	FZ_FUNCTION void fz_write_header(const FzBandWriter& writer, int w, int h, int n, int alpha, int xres, int yres, int pagenum, const FzColorspace& cs, const FzSeparations& seps);

	/** Class-aware wrapper for `::fz_write_image_as_data_uri()`.  */
	/**
		Write image as a data URI (for HTML and SVG output).
	*/
	FZ_FUNCTION void fz_write_image_as_data_uri(const FzOutput& out, const FzImage& image);

	/** Class-aware wrapper for `::fz_write_int16_be()`.  */
	FZ_FUNCTION void fz_write_int16_be(const FzOutput& out, int x);

	/** Class-aware wrapper for `::fz_write_int16_le()`.  */
	FZ_FUNCTION void fz_write_int16_le(const FzOutput& out, int x);

	/** Class-aware wrapper for `::fz_write_int32_be()`.  */
	/**
		Write different sized data to an output stream.
	*/
	FZ_FUNCTION void fz_write_int32_be(const FzOutput& out, int x);

	/** Class-aware wrapper for `::fz_write_int32_le()`.  */
	FZ_FUNCTION void fz_write_int32_le(const FzOutput& out, int x);

	/** Class-aware wrapper for `::fz_write_json()`.  */
	FZ_FUNCTION void fz_write_json(const FzOutput& out, const FzJson& value);

	/** Class-aware wrapper for `::fz_write_pixmap_as_data_uri()`.  */
	FZ_FUNCTION void fz_write_pixmap_as_data_uri(const FzOutput& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_write_pixmap_as_jpeg()`.  */
	/**
		Write a pixmap as a JPEG.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_jpeg(const FzOutput& out, const FzPixmap& pix, int quality, int invert_cmyk);

	/** Class-aware wrapper for `::fz_write_pixmap_as_jpx()`.  */
	/**
		Pixmap data as JP2K with no subsampling.
	
		quality = 100 = lossless
		otherwise for a factor of x compression use 100-x. (so 80 is 1:20 compression)
	*/
	FZ_FUNCTION void fz_write_pixmap_as_jpx(const FzOutput& out, const FzPixmap& pix, int quality);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pam()`.  */
	/**
		Write a pixmap as a pnm (greyscale, rgb or cmyk, with or without
		alpha).
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pam(const FzOutput& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pcl()`.  */
	/**
		Write an (RGB) pixmap as color PCL.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pcl(const FzOutput& out, const FzPixmap& pixmap, const FzPclOptions& pcl);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pclm()`.  */
	/**
		Write a (Greyscale or RGB) pixmap as pclm.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pclm(const FzOutput& out, const FzPixmap& pixmap, const FzPclmOptions& options);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pdfocr()`.  */
	/**
		Write a (Greyscale or RGB) pixmap as pdfocr.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pdfocr(const FzOutput& out, const FzPixmap& pixmap, FzPdfocrOptions& options);

	/** Class-aware wrapper for `::fz_write_pixmap_as_png()`.  */
	/**
		Write a (Greyscale or RGB) pixmap as a png.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_png(const FzOutput& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pnm()`.  */
	/**
		Write a pixmap as a pnm (greyscale or rgb, no alpha).
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pnm(const FzOutput& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_write_pixmap_as_ps()`.  */
	/**
		Write a (gray, rgb, or cmyk, no alpha) pixmap out as postscript.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_ps(const FzOutput& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_write_pixmap_as_psd()`.  */
	/**
		Write a pixmap as a PSD file.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_psd(const FzOutput& out, const FzPixmap& pixmap);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pwg()`.  */
	/**
		Write a pixmap as a PWG.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pwg(const FzOutput& out, const FzPixmap& pixmap, FzPwgOptions& pwg);

	/** Class-aware wrapper for `::fz_write_pixmap_as_pwg_page()`.  */
	/**
		Write a pixmap as a PWG page.
	
		Caller should provide a file header by calling
		fz_write_pwg_file_header, but can then write several pages to
		the same file.
	*/
	FZ_FUNCTION void fz_write_pixmap_as_pwg_page(const FzOutput& out, const FzPixmap& pixmap, FzPwgOptions& pwg);

	/** Class-aware wrapper for `::fz_write_ps_file_header()`.  */
	/**
		Write the file level header for ps band writer output.
	*/
	FZ_FUNCTION void fz_write_ps_file_header(const FzOutput& out);

	/** Class-aware wrapper for `::fz_write_ps_file_trailer()`.  */
	/**
		Write the file level trailer for ps band writer output.
	*/
	FZ_FUNCTION void fz_write_ps_file_trailer(const FzOutput& out, int pages);

	/** Class-aware wrapper for `::fz_write_pwg_file_header()`.  */
	/**
		Output the file header to a pwg stream, ready for pages to follow it.
	*/
	FZ_FUNCTION void fz_write_pwg_file_header(const FzOutput& out);

	/** Class-aware wrapper for `::fz_write_rune()`.  */
	/**
		Write a UTF-8 encoded unicode character.
	*/
	FZ_FUNCTION void fz_write_rune(const FzOutput& out, int rune);

	/** Class-aware wrapper for `::fz_write_stabilized_story()`.  */
	FZ_FUNCTION void fz_write_stabilized_story(const FzDocumentWriter& writer, const char *user_css, float em, ::fz_write_story_contentfn contentfn, void *contentfn_ref, ::fz_write_story_rectfn rectfn, void *rectfn_ref, ::fz_write_story_pagefn pagefn, void *pagefn_ref, const FzArchive& dir);

	/** Class-aware wrapper for `::fz_write_story()`.  */
	FZ_FUNCTION void fz_write_story(const FzDocumentWriter& writer, const FzStory& story, ::fz_write_story_rectfn rectfn, void *rectfn_ref, ::fz_write_story_positionfn positionfn, void *positionfn_ref, ::fz_write_story_pagefn pagefn, void *pagefn_ref);

	/** Class-aware wrapper for `::fz_write_stream()`.  */
	/**
		Copy the stream contents to the output.
	*/
	FZ_FUNCTION void fz_write_stream(const FzOutput& out, const FzStream& in);

	/** Class-aware wrapper for `::fz_write_string()`.  */
	/**
		Write a string. Does not write zero terminator.
	*/
	FZ_FUNCTION void fz_write_string(const FzOutput& out, const char *s);

	/** Class-aware wrapper for `::fz_write_uint16_be()`.  */
	FZ_FUNCTION void fz_write_uint16_be(const FzOutput& out, unsigned int x);

	/** Class-aware wrapper for `::fz_write_uint16_le()`.  */
	FZ_FUNCTION void fz_write_uint16_le(const FzOutput& out, unsigned int x);

	/** Class-aware wrapper for `::fz_write_uint32_be()`.  */
	FZ_FUNCTION void fz_write_uint32_be(const FzOutput& out, unsigned int x);

	/** Class-aware wrapper for `::fz_write_uint32_le()`.  */
	FZ_FUNCTION void fz_write_uint32_le(const FzOutput& out, unsigned int x);

	/** Class-aware wrapper for `::fz_write_vprintf()`.  */
	/**
		va_list version of fz_write_printf.
	*/
	FZ_FUNCTION void fz_write_vprintf(const FzOutput& out, const char *fmt, va_list ap);

	/** Class-aware wrapper for `::fz_write_xml()`.  */
	/**
		Write our xml structure out to an xml stream.
	
		Properly formatted XML is only allowed to have a single top-level node
		under which everything must sit. Our structures allow for multiple
		top level nodes. If required, we will output an extra 'ROOT' node
		at the top so that the xml is well-formed.
	
		If 'indented' is non-zero then additional whitespace will be added to
		make the XML easier to read in a text editor. It will NOT be properly
		compliant.
	*/
	FZ_FUNCTION void fz_write_xml(const FzXml& root, const FzOutput& out, int indented);

	/** Class-aware wrapper for `::fz_write_zip_entry()`.  */
	/**
		Given a buffer of data, (optionally) compress it, and add it to
		the zip file with the given name.
	*/
	FZ_FUNCTION void fz_write_zip_entry(const FzZipWriter& zip, const char *name, const FzBuffer& buf, int compress);

	/** Class-aware wrapper for `::fz_xml_add_att()`.  */
	/**
		Add an attribute to an XML node.
	*/
	FZ_FUNCTION void fz_xml_add_att(const FzPool& pool, const FzXml& node, const char *key, const char *val);

	/** Class-aware wrapper for `::fz_xml_att()`.  */
	/**
		Return the value of an attribute of an XML node.
		NULL if the attribute doesn't exist.
	*/
	FZ_FUNCTION char *fz_xml_att(const FzXml& item, const char *att);

	/** Class-aware wrapper for `::fz_xml_att_alt()`.  */
	/**
		Return the value of an attribute of an XML node.
		If the first attribute doesn't exist, try the second.
		NULL if neither attribute exists.
	*/
	FZ_FUNCTION char *fz_xml_att_alt(const FzXml& item, const char *one, const char *two);

	/** Class-aware wrapper for `::fz_xml_att_eq()`.  */
	/**
		Check for a matching attribute on an XML node.
	
		If the node has the requested attribute (name), and the value
		matches (match) then return 1. Otherwise, 0.
	*/
	FZ_FUNCTION int fz_xml_att_eq(const FzXml& item, const char *name, const char *match);

	/** Class-aware wrapper for `::fz_xml_down()`.  */
	/**
		Return first child of XML node.
	*/
	FZ_FUNCTION FzXml fz_xml_down(const FzXml& item);

	/** Class-aware wrapper for `::fz_xml_find()`.  */
	/**
		Search the siblings of XML nodes starting with item looking for
		the first with the given tag.
	
		Return NULL if none found.
	*/
	FZ_FUNCTION FzXml fz_xml_find(const FzXml& item, const char *tag);

	/** Class-aware wrapper for `::fz_xml_find_dfs()`.  */
	/**
		Perform a depth first search from item, returning the first
		child that matches the given tag (or any tag if tag is NULL),
		with the given attribute (if att is non NULL), that matches
		match (if match is non NULL).
	*/
	FZ_FUNCTION FzXml fz_xml_find_dfs(const FzXml& item, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_xml_find_dfs_top()`.  */
	/**
		Perform a depth first search from item, returning the first
		child that matches the given tag (or any tag if tag is NULL),
		with the given attribute (if att is non NULL), that matches
		match (if match is non NULL). The search stops if it ever
		reaches the top of the tree, or the declared 'top' item.
	*/
	FZ_FUNCTION FzXml fz_xml_find_dfs_top(const FzXml& item, const char *tag, const char *att, const char *match, const FzXml& top);

	/** Class-aware wrapper for `::fz_xml_find_down()`.  */
	/**
		Search the siblings of XML nodes starting with the first child
		of item looking for the first with the given tag.
	
		Return NULL if none found.
	*/
	FZ_FUNCTION FzXml fz_xml_find_down(const FzXml& item, const char *tag);

	/** Class-aware wrapper for `::fz_xml_find_down_match()`.  */
	/**
		Search the siblings of XML nodes starting with the first child
		of item looking for the first with the given tag (or any tag if
		tag is NULL), and with a matching attribute.
	
		Return NULL if none found.
	*/
	FZ_FUNCTION FzXml fz_xml_find_down_match(const FzXml& item, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_xml_find_match()`.  */
	/**
		Search the siblings of XML nodes starting with item looking for
		the first with the given tag (or any tag if tag is NULL), and
		with a matching attribute.
	
		Return NULL if none found.
	*/
	FZ_FUNCTION FzXml fz_xml_find_match(const FzXml& item, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_xml_find_next()`.  */
	/**
		Search the siblings of XML nodes starting with the first sibling
		of item looking for the first with the given tag.
	
		Return NULL if none found.
	*/
	FZ_FUNCTION FzXml fz_xml_find_next(const FzXml& item, const char *tag);

	/** Class-aware wrapper for `::fz_xml_find_next_dfs()`.  */
	/**
		Perform a depth first search onwards from item, returning the first
		child that matches the given tag (or any tag if tag is NULL),
		with the given attribute (if att is non NULL), that matches
		match (if match is non NULL).
	*/
	FZ_FUNCTION FzXml fz_xml_find_next_dfs(const FzXml& item, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_xml_find_next_dfs_top()`.  */
	/**
		Perform a depth first search onwards from item, returning the first
		child that matches the given tag (or any tag if tag is NULL),
		with the given attribute (if att is non NULL), that matches
		match (if match is non NULL). The search stops if it ever reaches
		the top of the tree, or the declared 'top' item.
	*/
	FZ_FUNCTION FzXml fz_xml_find_next_dfs_top(const FzXml& item, const char *tag, const char *att, const char *match, const FzXml& top);

	/** Class-aware wrapper for `::fz_xml_find_next_match()`.  */
	/**
		Search the siblings of XML nodes starting with the first sibling
		of item looking for the first with the given tag (or any tag if tag
		is NULL), and with a matching attribute.
	
		Return NULL if none found.
	*/
	FZ_FUNCTION FzXml fz_xml_find_next_match(const FzXml& item, const char *tag, const char *att, const char *match);

	/** Class-aware wrapper for `::fz_xml_is_tag()`.  */
	/**
		Return true if the tag name matches.
	*/
	FZ_FUNCTION int fz_xml_is_tag(const FzXml& item, const char *name);

	/** Class-aware wrapper for `::fz_xml_next()`.  */
	/**
		Return next sibling of XML node.
	*/
	FZ_FUNCTION FzXml fz_xml_next(const FzXml& item);

	/** Class-aware wrapper for `::fz_xml_prev()`.  */
	/**
		Return previous sibling of XML node.
	*/
	FZ_FUNCTION FzXml fz_xml_prev(const FzXml& item);

	/** Class-aware wrapper for `::fz_xml_root()`.  */
	/**
		Return the topmost XML node of a document.
	*/
	FZ_FUNCTION FzXml fz_xml_root(const FzXml& xml);

	/** Class-aware wrapper for `::fz_xml_tag()`.  */
	/**
		Return tag of XML node. Return NULL for text nodes.
	*/
	FZ_FUNCTION char *fz_xml_tag(const FzXml& item);

	/** Class-aware wrapper for `::fz_xml_text()`.  */
	/**
		Return the text content of an XML node.
		Return NULL if the node is a tag.
	*/
	FZ_FUNCTION char *fz_xml_text(const FzXml& item);

	/** Class-aware wrapper for `::fz_xml_up()`.  */
	/**
		Return parent of XML node.
	*/
	FZ_FUNCTION FzXml fz_xml_up(const FzXml& item);

	/** Class-aware wrapper for `::pdf_abandon_operation()`.  */
	FZ_FUNCTION void pdf_abandon_operation(const PdfDocument& doc);

	/* Class-aware wrapper for `pdf_access_alert_event()`
	is not available because returned wrapper class for `pdf_alert_event`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_access_exec_menu_item_event()`.  */
	FZ_FUNCTION const char *pdf_access_exec_menu_item_event(const PdfDocEvent& evt);

	/* Class-aware wrapper for `pdf_access_launch_url_event()`
	is not available because returned wrapper class for `pdf_launch_url_event`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_access_mail_doc_event()`
	is not available because returned wrapper class for `pdf_mail_doc_event`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_add_annot_border_dash_item()`.  */
	FZ_FUNCTION void pdf_add_annot_border_dash_item(const PdfAnnot& annot, float length);

	/** Class-aware wrapper for `::pdf_add_annot_ink_list()`.  */
	FZ_FUNCTION void pdf_add_annot_ink_list(const PdfAnnot& annot, int n, ::fz_point stroke[]);

	/** Class-aware wrapper for `::pdf_add_annot_ink_list_stroke()`.  */
	FZ_FUNCTION void pdf_add_annot_ink_list_stroke(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_add_annot_ink_list_stroke_vertex()`.  */
	FZ_FUNCTION void pdf_add_annot_ink_list_stroke_vertex(const PdfAnnot& annot, const FzPoint& p);

	/** Class-aware wrapper for `::pdf_add_annot_quad_point()`.  */
	FZ_FUNCTION void pdf_add_annot_quad_point(const PdfAnnot& annot, const FzQuad& quad);

	/** Class-aware wrapper for `::pdf_add_annot_vertex()`.  */
	FZ_FUNCTION void pdf_add_annot_vertex(const PdfAnnot& annot, const FzPoint& p);

	/** Class-aware wrapper for `::pdf_add_cid_font()`.  */
	FZ_FUNCTION PdfObj pdf_add_cid_font(const PdfDocument& doc, const FzFont& font);

	/** Class-aware wrapper for `::pdf_add_cjk_font()`.  */
	FZ_FUNCTION PdfObj pdf_add_cjk_font(const PdfDocument& doc, const FzFont& font, int script, int wmode, int serif);

	/** Class-aware wrapper for `::pdf_add_codespace()`.  */
	FZ_FUNCTION void pdf_add_codespace(const PdfCmap& cmap, unsigned int low, unsigned int high, size_t n);

	/** Class-aware wrapper for `::pdf_add_colorspace()`.  */
	FZ_FUNCTION PdfObj pdf_add_colorspace(const PdfDocument& doc, const FzColorspace& cs);

	/** Class-aware wrapper for `::pdf_add_embedded_file()`.  */
	FZ_FUNCTION PdfObj pdf_add_embedded_file(const PdfDocument& doc, const char *filename, const char *mimetype, const FzBuffer& contents, int64_t created, int64_t modified, int add_checksum);

	/** Class-aware wrapper for `::pdf_add_hmtx()`.  */
	FZ_FUNCTION void pdf_add_hmtx(const PdfFontDesc& font, int lo, int hi, int w);

	/** Class-aware wrapper for `::pdf_add_image()`.  */
	FZ_FUNCTION PdfObj pdf_add_image(const PdfDocument& doc, const FzImage& image);

	/** Class-aware wrapper for `::pdf_add_journal_fragment()`.  */
	FZ_FUNCTION void pdf_add_journal_fragment(const PdfDocument& doc, int parent, const PdfObj& copy, const FzBuffer& copy_stream, int newobj);

	/** Class-aware wrapper for `::pdf_add_new_array()`.  */
	FZ_FUNCTION PdfObj pdf_add_new_array(const PdfDocument& doc, int initial);

	/** Class-aware wrapper for `::pdf_add_new_dict()`.  */
	FZ_FUNCTION PdfObj pdf_add_new_dict(const PdfDocument& doc, int initial);

	/** Class-aware wrapper for `::pdf_add_object()`.  */
	FZ_FUNCTION PdfObj pdf_add_object(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_add_page()`.  */
	FZ_FUNCTION PdfObj pdf_add_page(const PdfDocument& doc, const FzRect& mediabox, int rotate, const PdfObj& resources, const FzBuffer& contents);

	/** Class-aware wrapper for `::pdf_add_simple_font()`.  */
	FZ_FUNCTION PdfObj pdf_add_simple_font(const PdfDocument& doc, const FzFont& font, int encoding);

	/** Class-aware wrapper for `::pdf_add_stream()`.  */
	FZ_FUNCTION PdfObj pdf_add_stream(const PdfDocument& doc, const FzBuffer& buf, const PdfObj& obj, int compressed);

	/** Class-aware wrapper for `::pdf_add_substitute_font()`.  */
	FZ_FUNCTION PdfObj pdf_add_substitute_font(const PdfDocument& doc, const FzFont& font);

	/** Class-aware wrapper for `::pdf_add_vmtx()`.  */
	FZ_FUNCTION void pdf_add_vmtx(const PdfFontDesc& font, int lo, int hi, int x, int y, int w);

	/** Class-aware wrapper for `::pdf_annot_MK_BC()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_MK_BC(::pdf_annot *annot, float color[4])` => int n
	 */
	FZ_FUNCTION void pdf_annot_MK_BC(const PdfAnnot& annot, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_annot_MK_BC_rgb()`.  */
	FZ_FUNCTION int pdf_annot_MK_BC_rgb(const PdfAnnot& annot, float rgb[3]);

	/** Class-aware wrapper for `::pdf_annot_MK_BG()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_MK_BG(::pdf_annot *annot, float color[4])` => int n
	 */
	FZ_FUNCTION void pdf_annot_MK_BG(const PdfAnnot& annot, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_annot_MK_BG_rgb()`.  */
	FZ_FUNCTION int pdf_annot_MK_BG_rgb(const PdfAnnot& annot, float rgb[3]);

	/** Class-aware wrapper for `::pdf_annot_active()`.  */
	FZ_FUNCTION int pdf_annot_active(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_ap()`.  */
	FZ_FUNCTION PdfObj pdf_annot_ap(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_author()`.  */
	FZ_FUNCTION const char *pdf_annot_author(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_border()`.  */
	FZ_FUNCTION float pdf_annot_border(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_border_dash_count()`.  */
	FZ_FUNCTION int pdf_annot_border_dash_count(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_border_dash_item()`.  */
	FZ_FUNCTION float pdf_annot_border_dash_item(const PdfAnnot& annot, int i);

	/** Class-aware wrapper for `::pdf_annot_border_effect()`.  */
	FZ_FUNCTION enum pdf_border_effect pdf_annot_border_effect(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_border_effect_intensity()`.  */
	FZ_FUNCTION float pdf_annot_border_effect_intensity(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_border_style()`.  */
	FZ_FUNCTION enum pdf_border_style pdf_annot_border_style(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_border_width()`.  */
	FZ_FUNCTION float pdf_annot_border_width(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_callout_line()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_callout_line(::pdf_annot *annot, ::fz_point callout[3])` => int n
	 */
	FZ_FUNCTION void pdf_annot_callout_line(const PdfAnnot& annot, ::fz_point callout[3], int *n);

	/** Class-aware wrapper for `::pdf_annot_callout_point()`.  */
	FZ_FUNCTION FzPoint pdf_annot_callout_point(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_callout_style()`.  */
	FZ_FUNCTION enum pdf_line_ending pdf_annot_callout_style(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_color()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_color(::pdf_annot *annot, float color[4])` => int n
	 */
	FZ_FUNCTION void pdf_annot_color(const PdfAnnot& annot, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_annot_contents()`.  */
	FZ_FUNCTION const char *pdf_annot_contents(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_creation_date()`.  */
	FZ_FUNCTION int64_t pdf_annot_creation_date(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_default_appearance()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_default_appearance(::pdf_annot *annot, float color[4])` => `(const char *font, float size, int n)`
	 */
	FZ_FUNCTION void pdf_annot_default_appearance(const PdfAnnot& annot, const char **font, float *size, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_annot_default_appearance_unmapped()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_default_appearance_unmapped(::pdf_annot *annot, char *font_name, int font_name_len, float color[4])` => `(float size, int n)`
	 */
	FZ_FUNCTION void pdf_annot_default_appearance_unmapped(const PdfAnnot& annot, char *font_name, int font_name_len, float *size, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_annot_ensure_local_xref()`.  */
	FZ_FUNCTION void pdf_annot_ensure_local_xref(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_blur()`.  */
	FZ_FUNCTION void pdf_annot_event_blur(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_down()`.  */
	FZ_FUNCTION void pdf_annot_event_down(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_enter()`.  */
	FZ_FUNCTION void pdf_annot_event_enter(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_exit()`.  */
	FZ_FUNCTION void pdf_annot_event_exit(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_focus()`.  */
	FZ_FUNCTION void pdf_annot_event_focus(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_page_close()`.  */
	FZ_FUNCTION void pdf_annot_event_page_close(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_page_invisible()`.  */
	FZ_FUNCTION void pdf_annot_event_page_invisible(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_page_open()`.  */
	FZ_FUNCTION void pdf_annot_event_page_open(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_page_visible()`.  */
	FZ_FUNCTION void pdf_annot_event_page_visible(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_event_up()`.  */
	FZ_FUNCTION void pdf_annot_event_up(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_field_event_keystroke()`.  */
	FZ_FUNCTION int pdf_annot_field_event_keystroke(const PdfDocument& doc, const PdfAnnot& annot, const PdfKeystrokeEvent& evt);

	/** Class-aware wrapper for `::pdf_annot_field_flags()`.  */
	FZ_FUNCTION int pdf_annot_field_flags(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_field_label()`.  */
	FZ_FUNCTION const char *pdf_annot_field_label(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_annot_field_value()`.  */
	FZ_FUNCTION const char *pdf_annot_field_value(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_filespec()`.  */
	FZ_FUNCTION PdfObj pdf_annot_filespec(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_flags()`.  */
	FZ_FUNCTION int pdf_annot_flags(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_author()`.  */
	FZ_FUNCTION int pdf_annot_has_author(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_border()`.  */
	FZ_FUNCTION int pdf_annot_has_border(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_border_effect()`.  */
	FZ_FUNCTION int pdf_annot_has_border_effect(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_callout()`.  */
	FZ_FUNCTION int pdf_annot_has_callout(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_default_appearance()`.  */
	FZ_FUNCTION int pdf_annot_has_default_appearance(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_filespec()`.  */
	FZ_FUNCTION int pdf_annot_has_filespec(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_icon_name()`.  */
	FZ_FUNCTION int pdf_annot_has_icon_name(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_ink_list()`.  */
	FZ_FUNCTION int pdf_annot_has_ink_list(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_intent()`.  */
	FZ_FUNCTION int pdf_annot_has_intent(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_interior_color()`.  */
	FZ_FUNCTION int pdf_annot_has_interior_color(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_line()`.  */
	FZ_FUNCTION int pdf_annot_has_line(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_line_ending_styles()`.  */
	FZ_FUNCTION int pdf_annot_has_line_ending_styles(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_open()`.  */
	FZ_FUNCTION int pdf_annot_has_open(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_popup()`.  */
	FZ_FUNCTION int pdf_annot_has_popup(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_quad_points()`.  */
	FZ_FUNCTION int pdf_annot_has_quad_points(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_quadding()`.  */
	FZ_FUNCTION int pdf_annot_has_quadding(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_rect()`.  */
	FZ_FUNCTION int pdf_annot_has_rect(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_rich_contents()`.  */
	FZ_FUNCTION int pdf_annot_has_rich_contents(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_rich_defaults()`.  */
	FZ_FUNCTION int pdf_annot_has_rich_defaults(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_has_vertices()`.  */
	FZ_FUNCTION int pdf_annot_has_vertices(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_hidden_for_editing()`.  */
	FZ_FUNCTION int pdf_annot_hidden_for_editing(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_hot()`.  */
	FZ_FUNCTION int pdf_annot_hot(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_icon_name()`.  */
	FZ_FUNCTION const char *pdf_annot_icon_name(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_ink_list_count()`.  */
	FZ_FUNCTION int pdf_annot_ink_list_count(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_ink_list_stroke_count()`.  */
	FZ_FUNCTION int pdf_annot_ink_list_stroke_count(const PdfAnnot& annot, int i);

	/** Class-aware wrapper for `::pdf_annot_ink_list_stroke_vertex()`.  */
	FZ_FUNCTION FzPoint pdf_annot_ink_list_stroke_vertex(const PdfAnnot& annot, int i, int k);

	/** Class-aware wrapper for `::pdf_annot_intent()`.  */
	FZ_FUNCTION enum pdf_intent pdf_annot_intent(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_interior_color()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_interior_color(::pdf_annot *annot, float color[4])` => int n
	 */
	FZ_FUNCTION void pdf_annot_interior_color(const PdfAnnot& annot, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_annot_is_open()`.  */
	FZ_FUNCTION int pdf_annot_is_open(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_is_standard_stamp()`.  */
	FZ_FUNCTION int pdf_annot_is_standard_stamp(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_language()`.  */
	FZ_FUNCTION ::fz_text_language pdf_annot_language(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line()`.  */
	FZ_FUNCTION void pdf_annot_line(const PdfAnnot& annot, FzPoint& a, FzPoint& b);

	/** Class-aware wrapper for `::pdf_annot_line_caption()`.  */
	FZ_FUNCTION int pdf_annot_line_caption(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line_caption_offset()`.  */
	FZ_FUNCTION FzPoint pdf_annot_line_caption_offset(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line_end_style()`.  */
	FZ_FUNCTION enum pdf_line_ending pdf_annot_line_end_style(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line_ending_styles()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_annot_line_ending_styles(::pdf_annot *annot)` => `(enum pdf_line_ending start_style, enum pdf_line_ending end_style)`
	 */
	FZ_FUNCTION void pdf_annot_line_ending_styles(const PdfAnnot& annot, enum pdf_line_ending *start_style, enum pdf_line_ending *end_style);

	/** Class-aware wrapper for `::pdf_annot_line_leader()`.  */
	FZ_FUNCTION float pdf_annot_line_leader(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line_leader_extension()`.  */
	FZ_FUNCTION float pdf_annot_line_leader_extension(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line_leader_offset()`.  */
	FZ_FUNCTION float pdf_annot_line_leader_offset(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_line_start_style()`.  */
	FZ_FUNCTION enum pdf_line_ending pdf_annot_line_start_style(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_modification_date()`.  */
	FZ_FUNCTION int64_t pdf_annot_modification_date(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_needs_resynthesis()`.  */
	FZ_FUNCTION int pdf_annot_needs_resynthesis(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_obj()`.  */
	FZ_FUNCTION PdfObj pdf_annot_obj(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_opacity()`.  */
	FZ_FUNCTION float pdf_annot_opacity(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_page()`.  */
	FZ_FUNCTION PdfPage pdf_annot_page(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_pop_and_discard_local_xref()`.  */
	FZ_FUNCTION void pdf_annot_pop_and_discard_local_xref(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_pop_local_xref()`.  */
	FZ_FUNCTION void pdf_annot_pop_local_xref(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_popup()`.  */
	FZ_FUNCTION FzRect pdf_annot_popup(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_push_local_xref()`.  */
	FZ_FUNCTION void pdf_annot_push_local_xref(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_quad_point()`.  */
	FZ_FUNCTION FzQuad pdf_annot_quad_point(const PdfAnnot& annot, int i);

	/** Class-aware wrapper for `::pdf_annot_quad_point_count()`.  */
	FZ_FUNCTION int pdf_annot_quad_point_count(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_quadding()`.  */
	FZ_FUNCTION int pdf_annot_quadding(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_rect()`.  */
	FZ_FUNCTION FzRect pdf_annot_rect(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_request_resynthesis()`.  */
	FZ_FUNCTION void pdf_annot_request_resynthesis(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_request_synthesis()`.  */
	FZ_FUNCTION void pdf_annot_request_synthesis(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_rich_contents()`.  */
	FZ_FUNCTION const char *pdf_annot_rich_contents(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_rich_defaults()`.  */
	FZ_FUNCTION const char *pdf_annot_rich_defaults(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_stamp_image_obj()`.  */
	FZ_FUNCTION PdfObj pdf_annot_stamp_image_obj(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_transform()`.  */
	FZ_FUNCTION FzMatrix pdf_annot_transform(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_type()`.  */
	FZ_FUNCTION enum pdf_annot_type pdf_annot_type(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_annot_type_from_string()`.  */
	FZ_FUNCTION enum pdf_annot_type pdf_annot_type_from_string(const char *subtype);

	/** Class-aware wrapper for `::pdf_annot_vertex()`.  */
	FZ_FUNCTION FzPoint pdf_annot_vertex(const PdfAnnot& annot, int i);

	/** Class-aware wrapper for `::pdf_annot_vertex_count()`.  */
	FZ_FUNCTION int pdf_annot_vertex_count(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_append_explicit_dest_to_uri()`.  */
	FZ_FUNCTION char *pdf_append_explicit_dest_to_uri(const char *url, const FzLinkDest& dest);

	/** Class-aware wrapper for `::pdf_append_named_dest_to_uri()`.  */
	FZ_FUNCTION char *pdf_append_named_dest_to_uri(const char *url, const char *name);

	/** Class-aware wrapper for `::pdf_append_token()`.  */
	FZ_FUNCTION void pdf_append_token(const FzBuffer& buf, int tok, const PdfLexbuf& lex);

	/** Class-aware wrapper for `::pdf_apply_redaction()`.  */
	FZ_FUNCTION int pdf_apply_redaction(const PdfAnnot& annot, PdfRedactOptions& opts);

	/** Class-aware wrapper for `::pdf_array_contains()`.  */
	FZ_FUNCTION int pdf_array_contains(const PdfObj& array, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_array_delete()`.  */
	FZ_FUNCTION void pdf_array_delete(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_find()`.  */
	FZ_FUNCTION int pdf_array_find(const PdfObj& array, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_array_get()`.  */
	FZ_FUNCTION PdfObj pdf_array_get(const PdfObj& array, int i);

	/** Class-aware wrapper for `::pdf_array_get_bool()`.  */
	FZ_FUNCTION int pdf_array_get_bool(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_get_int()`.  */
	FZ_FUNCTION int pdf_array_get_int(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_get_matrix()`.  */
	FZ_FUNCTION FzMatrix pdf_array_get_matrix(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_get_name()`.  */
	FZ_FUNCTION const char *pdf_array_get_name(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_get_real()`.  */
	FZ_FUNCTION float pdf_array_get_real(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_get_rect()`.  */
	FZ_FUNCTION FzRect pdf_array_get_rect(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_get_string()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_array_get_string(::pdf_obj *array, int index)` => `(const char *, size_t sizep)`
	 */
	FZ_FUNCTION const char *pdf_array_get_string(const PdfObj& array, int index, size_t *sizep);

	/** Class-aware wrapper for `::pdf_array_get_text_string()`.  */
	FZ_FUNCTION const char *pdf_array_get_text_string(const PdfObj& array, int index);

	/** Class-aware wrapper for `::pdf_array_insert()`.  */
	FZ_FUNCTION void pdf_array_insert(const PdfObj& array, const PdfObj& obj, int index);

	/** Class-aware wrapper for `::pdf_array_len()`.  */
	FZ_FUNCTION int pdf_array_len(const PdfObj& array);

	/** Class-aware wrapper for `::pdf_array_push()`.  */
	FZ_FUNCTION void pdf_array_push(const PdfObj& array, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_array_push_array()`.  */
	FZ_FUNCTION PdfObj pdf_array_push_array(const PdfObj& array, int initial);

	/** Class-aware wrapper for `::pdf_array_push_bool()`.  */
	FZ_FUNCTION void pdf_array_push_bool(const PdfObj& array, int x);

	/** Class-aware wrapper for `::pdf_array_push_dict()`.  */
	FZ_FUNCTION PdfObj pdf_array_push_dict(const PdfObj& array, int initial);

	/** Class-aware wrapper for `::pdf_array_push_int()`.  */
	FZ_FUNCTION void pdf_array_push_int(const PdfObj& array, int64_t x);

	/** Class-aware wrapper for `::pdf_array_push_name()`.  */
	FZ_FUNCTION void pdf_array_push_name(const PdfObj& array, const char *x);

	/** Class-aware wrapper for `::pdf_array_push_real()`.  */
	FZ_FUNCTION void pdf_array_push_real(const PdfObj& array, double x);

	/** Class-aware wrapper for `::pdf_array_push_string()`.  */
	FZ_FUNCTION void pdf_array_push_string(const PdfObj& array, const char *x, size_t n);

	/** Class-aware wrapper for `::pdf_array_push_text_string()`.  */
	FZ_FUNCTION void pdf_array_push_text_string(const PdfObj& array, const char *x);

	/** Class-aware wrapper for `::pdf_array_put()`.  */
	FZ_FUNCTION void pdf_array_put(const PdfObj& array, int i, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_array_put_array()`.  */
	FZ_FUNCTION PdfObj pdf_array_put_array(const PdfObj& array, int i, int initial);

	/** Class-aware wrapper for `::pdf_array_put_bool()`.  */
	FZ_FUNCTION void pdf_array_put_bool(const PdfObj& array, int i, int x);

	/** Class-aware wrapper for `::pdf_array_put_dict()`.  */
	FZ_FUNCTION PdfObj pdf_array_put_dict(const PdfObj& array, int i, int initial);

	/** Class-aware wrapper for `::pdf_array_put_int()`.  */
	FZ_FUNCTION void pdf_array_put_int(const PdfObj& array, int i, int64_t x);

	/** Class-aware wrapper for `::pdf_array_put_name()`.  */
	FZ_FUNCTION void pdf_array_put_name(const PdfObj& array, int i, const char *x);

	/** Class-aware wrapper for `::pdf_array_put_real()`.  */
	FZ_FUNCTION void pdf_array_put_real(const PdfObj& array, int i, double x);

	/** Class-aware wrapper for `::pdf_array_put_string()`.  */
	FZ_FUNCTION void pdf_array_put_string(const PdfObj& array, int i, const char *x, size_t n);

	/** Class-aware wrapper for `::pdf_array_put_text_string()`.  */
	FZ_FUNCTION void pdf_array_put_text_string(const PdfObj& array, int i, const char *x);

	/** Class-aware wrapper for `::pdf_authenticate_password()`.  */
	FZ_FUNCTION int pdf_authenticate_password(const PdfDocument& doc, const char *pw);

	/** Class-aware wrapper for `::pdf_bake_document()`.  */
	FZ_FUNCTION void pdf_bake_document(const PdfDocument& doc, int bake_annots, int bake_widgets);

	/** Class-aware wrapper for `::pdf_begin_implicit_operation()`.  */
	FZ_FUNCTION void pdf_begin_implicit_operation(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_begin_operation()`.  */
	FZ_FUNCTION void pdf_begin_operation(const PdfDocument& doc, const char *operation);

	/** Class-aware wrapper for `::pdf_bound_annot()`.  */
	FZ_FUNCTION FzRect pdf_bound_annot(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_bound_page()`.  */
	FZ_FUNCTION FzRect pdf_bound_page(const PdfPage& page, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_bound_widget()`.  */
	FZ_FUNCTION FzRect pdf_bound_widget(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_button_field_on_state()`.  */
	FZ_FUNCTION PdfObj pdf_button_field_on_state(const PdfObj& field);

	/* Class-aware wrapper for `pdf_cache_object()`
	is not available because returned wrapper class for `pdf_xref_entry`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_calculate_form()`.  */
	FZ_FUNCTION void pdf_calculate_form(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_can_be_saved_incrementally()`.  */
	FZ_FUNCTION int pdf_can_be_saved_incrementally(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_can_redo()`.  */
	FZ_FUNCTION int pdf_can_redo(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_can_undo()`.  */
	FZ_FUNCTION int pdf_can_undo(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_check_certificate()`.  */
	FZ_FUNCTION ::pdf_signature_error pdf_check_certificate(const PdfPkcs7Verifier& verifier, const PdfDocument& doc, const PdfObj& signature);

	/** Class-aware wrapper for `::pdf_check_digest()`.  */
	FZ_FUNCTION ::pdf_signature_error pdf_check_digest(const PdfPkcs7Verifier& verifier, const PdfDocument& doc, const PdfObj& signature);

	/** Class-aware wrapper for `::pdf_check_widget_certificate()`.  */
	FZ_FUNCTION ::pdf_signature_error pdf_check_widget_certificate(const PdfPkcs7Verifier& verifier, const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_check_widget_digest()`.  */
	FZ_FUNCTION ::pdf_signature_error pdf_check_widget_digest(const PdfPkcs7Verifier& verifier, const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_choice_field_option()`.  */
	FZ_FUNCTION const char *pdf_choice_field_option(const PdfObj& field, int exportval, int i);

	/** Class-aware wrapper for `::pdf_choice_field_option_count()`.  */
	FZ_FUNCTION int pdf_choice_field_option_count(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_choice_widget_is_multiselect()`.  */
	FZ_FUNCTION int pdf_choice_widget_is_multiselect(const PdfAnnot& tw);

	/** Class-aware wrapper for `::pdf_choice_widget_options()`.  */
	FZ_FUNCTION int pdf_choice_widget_options(const PdfAnnot& tw, int exportval, const char *opts[]);

	/** Class-aware wrapper for `::pdf_choice_widget_options2()`.  */
	/** Swig-friendly wrapper for pdf_choice_widget_options(), returns the
	options directly in a vector. */
	FZ_FUNCTION std::vector<std::string> pdf_choice_widget_options2(const PdfAnnot& tw, int exportval);

	/** Class-aware wrapper for `::pdf_choice_widget_set_value()`.  */
	FZ_FUNCTION void pdf_choice_widget_set_value(const PdfAnnot& tw, int n, const char *opts[]);

	/** Class-aware wrapper for `::pdf_choice_widget_value()`.  */
	FZ_FUNCTION int pdf_choice_widget_value(const PdfAnnot& tw, const char *opts[]);

	/** Class-aware wrapper for `::pdf_clean_file()`.  */
	FZ_FUNCTION void pdf_clean_file(char *infile, char *outfile, char *password, PdfCleanOptions& opts, int retainlen, char *retainlist[]);

	/** Class-aware wrapper for `::pdf_clean_font_name()`.  */
	FZ_FUNCTION const char *pdf_clean_font_name(const char *fontname);

	/** Class-aware wrapper for `::pdf_clean_obj()`.  */
	FZ_FUNCTION void pdf_clean_obj(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_clear_annot_border_dash()`.  */
	FZ_FUNCTION void pdf_clear_annot_border_dash(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_clear_annot_ink_list()`.  */
	FZ_FUNCTION void pdf_clear_annot_ink_list(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_clear_annot_quad_points()`.  */
	FZ_FUNCTION void pdf_clear_annot_quad_points(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_clear_annot_vertices()`.  */
	FZ_FUNCTION void pdf_clear_annot_vertices(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_clear_signature()`.  */
	FZ_FUNCTION void pdf_clear_signature(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_clear_xref()`.  */
	FZ_FUNCTION void pdf_clear_xref(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_clear_xref_to_mark()`.  */
	FZ_FUNCTION void pdf_clear_xref_to_mark(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_clip_page()`.  */
	FZ_FUNCTION void pdf_clip_page(const PdfPage& page, FzRect& clip);

	/** Class-aware wrapper for `::pdf_close_processor()`.  */
	FZ_FUNCTION void pdf_close_processor(const PdfProcessor& proc);

	/** Class-aware wrapper for `::pdf_cmap_size()`.  */
	FZ_FUNCTION size_t pdf_cmap_size(const PdfCmap& cmap);

	/** Class-aware wrapper for `::pdf_cmap_wmode()`.  */
	FZ_FUNCTION int pdf_cmap_wmode(const PdfCmap& cmap);

	/** Class-aware wrapper for `::pdf_copy_array()`.  */
	FZ_FUNCTION PdfObj pdf_copy_array(const PdfObj& array);

	/** Class-aware wrapper for `::pdf_copy_dict()`.  */
	FZ_FUNCTION PdfObj pdf_copy_dict(const PdfObj& dict);

	/** Class-aware wrapper for `::pdf_count_document_associated_files()`.  */
	FZ_FUNCTION int pdf_count_document_associated_files(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_layer_config_ui()`.  */
	FZ_FUNCTION int pdf_count_layer_config_ui(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_layer_configs()`.  */
	FZ_FUNCTION int pdf_count_layer_configs(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_layers()`.  */
	FZ_FUNCTION int pdf_count_layers(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_objects()`.  */
	FZ_FUNCTION int pdf_count_objects(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_page_associated_files()`.  */
	FZ_FUNCTION int pdf_count_page_associated_files(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_count_pages()`.  */
	FZ_FUNCTION int pdf_count_pages(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_pages_imp()`.  */
	FZ_FUNCTION int pdf_count_pages_imp(const FzDocument& doc, int chapter);

	/** Class-aware wrapper for `::pdf_count_q_balance()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_count_q_balance(::pdf_document *doc, ::pdf_obj *res, ::pdf_obj *stm)` => `(int prepend, int append)`
	 */
	FZ_FUNCTION void pdf_count_q_balance(const PdfDocument& doc, const PdfObj& res, const PdfObj& stm, int *prepend, int *append);

	/** Class-aware wrapper for `::pdf_count_signatures()`.  */
	FZ_FUNCTION int pdf_count_signatures(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_unsaved_versions()`.  */
	FZ_FUNCTION int pdf_count_unsaved_versions(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_count_versions()`.  */
	FZ_FUNCTION int pdf_count_versions(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_create_annot()`.  */
	FZ_FUNCTION PdfAnnot pdf_create_annot(const PdfPage& page, enum pdf_annot_type type);

	/** Class-aware wrapper for `::pdf_create_annot_raw()`.  */
	FZ_FUNCTION PdfAnnot pdf_create_annot_raw(const PdfPage& page, enum pdf_annot_type type);

	/** Class-aware wrapper for `::pdf_create_document()`.  */
	FZ_FUNCTION PdfDocument pdf_create_document();

	/** Class-aware wrapper for `::pdf_create_field_name()`.  */
	FZ_FUNCTION void pdf_create_field_name(const PdfDocument& doc, const char *prefix, char *buf, size_t len);

	/** Class-aware wrapper for `::pdf_create_link()`.  */
	FZ_FUNCTION FzLink pdf_create_link(const PdfPage& page, const FzRect& bbox, const char *uri);

	/** Class-aware wrapper for `::pdf_create_object()`.  */
	FZ_FUNCTION int pdf_create_object(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_create_signature_widget()`.  */
	FZ_FUNCTION PdfAnnot pdf_create_signature_widget(const PdfPage& page, char *name);

	/** Class-aware wrapper for `::pdf_crypt_encrypt_metadata()`.  */
	FZ_FUNCTION int pdf_crypt_encrypt_metadata(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_key()`.  */
	FZ_FUNCTION unsigned char *pdf_crypt_key(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_length()`.  */
	FZ_FUNCTION int pdf_crypt_length(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_method()`.  */
	FZ_FUNCTION const char *pdf_crypt_method(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_obj()`.  */
	FZ_FUNCTION void pdf_crypt_obj(const PdfCrypt& crypt, const PdfObj& obj, int num, int gen);

	/** Class-aware wrapper for `::pdf_crypt_owner_encryption()`.  */
	FZ_FUNCTION unsigned char *pdf_crypt_owner_encryption(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_owner_password()`.  */
	FZ_FUNCTION unsigned char *pdf_crypt_owner_password(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_permissions()`.  */
	FZ_FUNCTION int pdf_crypt_permissions(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_permissions_encryption()`.  */
	FZ_FUNCTION unsigned char *pdf_crypt_permissions_encryption(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_revision()`.  */
	FZ_FUNCTION int pdf_crypt_revision(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_stream_method()`.  */
	FZ_FUNCTION const char *pdf_crypt_stream_method(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_string_method()`.  */
	FZ_FUNCTION const char *pdf_crypt_string_method(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_user_encryption()`.  */
	FZ_FUNCTION unsigned char *pdf_crypt_user_encryption(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_user_password()`.  */
	FZ_FUNCTION unsigned char *pdf_crypt_user_password(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_crypt_version()`.  */
	FZ_FUNCTION int pdf_crypt_version(const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_cycle()`.  */
	FZ_FUNCTION int pdf_cycle(const PdfCycleList& here, const PdfCycleList& prev, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_debug_doc_changes()`.  */
	FZ_FUNCTION void pdf_debug_doc_changes(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_debug_obj()`.  */
	FZ_FUNCTION void pdf_debug_obj(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_debug_ref()`.  */
	FZ_FUNCTION void pdf_debug_ref(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_decode_cmap()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_decode_cmap(::pdf_cmap *cmap, unsigned char *s, unsigned char *e)` => `(int, unsigned int cpt)`
	 */
	FZ_FUNCTION int pdf_decode_cmap(const PdfCmap& cmap, unsigned char *s, unsigned char *e, unsigned int *cpt);

	/** Class-aware wrapper for `::pdf_deep_copy_obj()`.  */
	FZ_FUNCTION PdfObj pdf_deep_copy_obj(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_delete_annot()`.  */
	FZ_FUNCTION void pdf_delete_annot(const PdfPage& page, const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_delete_link()`.  */
	FZ_FUNCTION void pdf_delete_link(const PdfPage& page, const FzLink& link);

	/** Class-aware wrapper for `::pdf_delete_object()`.  */
	FZ_FUNCTION void pdf_delete_object(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_delete_page()`.  */
	FZ_FUNCTION void pdf_delete_page(const PdfDocument& doc, int number);

	/** Class-aware wrapper for `::pdf_delete_page_labels()`.  */
	FZ_FUNCTION void pdf_delete_page_labels(const PdfDocument& doc, int index);

	/** Class-aware wrapper for `::pdf_delete_page_range()`.  */
	FZ_FUNCTION void pdf_delete_page_range(const PdfDocument& doc, int start, int end);

	/** Class-aware wrapper for `::pdf_deselect_layer_config_ui()`.  */
	FZ_FUNCTION void pdf_deselect_layer_config_ui(const PdfDocument& doc, int ui);

	/** Class-aware wrapper for `::pdf_deserialise_journal()`.  */
	FZ_FUNCTION void pdf_deserialise_journal(const PdfDocument& doc, const FzStream& stm);

	/** Class-aware wrapper for `::pdf_dict_del()`.  */
	FZ_FUNCTION void pdf_dict_del(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_dels()`.  */
	FZ_FUNCTION void pdf_dict_dels(const PdfObj& dict, const char *key);

	/** Class-aware wrapper for `::pdf_dict_get()`.  */
	FZ_FUNCTION PdfObj pdf_dict_get(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_bool()`.  */
	FZ_FUNCTION int pdf_dict_get_bool(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_bool_default()`.  */
	FZ_FUNCTION int pdf_dict_get_bool_default(const PdfObj& dict, const PdfObj& key, int def);

	/** Class-aware wrapper for `::pdf_dict_get_date()`.  */
	FZ_FUNCTION int64_t pdf_dict_get_date(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable()`.  */
	FZ_FUNCTION PdfObj pdf_dict_get_inheritable(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_bool()`.  */
	FZ_FUNCTION int pdf_dict_get_inheritable_bool(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_date()`.  */
	FZ_FUNCTION int64_t pdf_dict_get_inheritable_date(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_int()`.  */
	FZ_FUNCTION int pdf_dict_get_inheritable_int(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_int64()`.  */
	FZ_FUNCTION int64_t pdf_dict_get_inheritable_int64(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_matrix()`.  */
	FZ_FUNCTION FzMatrix pdf_dict_get_inheritable_matrix(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_name()`.  */
	FZ_FUNCTION const char *pdf_dict_get_inheritable_name(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_real()`.  */
	FZ_FUNCTION float pdf_dict_get_inheritable_real(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_rect()`.  */
	FZ_FUNCTION FzRect pdf_dict_get_inheritable_rect(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_string()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_dict_get_inheritable_string(::pdf_obj *dict, ::pdf_obj *key)` => `(const char *, size_t sizep)`
	 */
	FZ_FUNCTION const char *pdf_dict_get_inheritable_string(const PdfObj& dict, const PdfObj& key, size_t *sizep);

	/** Class-aware wrapper for `::pdf_dict_get_inheritable_text_string()`.  */
	FZ_FUNCTION const char *pdf_dict_get_inheritable_text_string(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_int()`.  */
	FZ_FUNCTION int pdf_dict_get_int(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_int64()`.  */
	FZ_FUNCTION int64_t pdf_dict_get_int64(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_int_default()`.  */
	FZ_FUNCTION int pdf_dict_get_int_default(const PdfObj& dict, const PdfObj& key, int def);

	/** Class-aware wrapper for `::pdf_dict_get_key()`.  */
	FZ_FUNCTION PdfObj pdf_dict_get_key(const PdfObj& dict, int idx);

	/** Class-aware wrapper for `::pdf_dict_get_matrix()`.  */
	FZ_FUNCTION FzMatrix pdf_dict_get_matrix(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_name()`.  */
	FZ_FUNCTION const char *pdf_dict_get_name(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_point()`.  */
	FZ_FUNCTION FzPoint pdf_dict_get_point(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_real()`.  */
	FZ_FUNCTION float pdf_dict_get_real(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_real_default()`.  */
	FZ_FUNCTION float pdf_dict_get_real_default(const PdfObj& dict, const PdfObj& key, float def);

	/** Class-aware wrapper for `::pdf_dict_get_rect()`.  */
	FZ_FUNCTION FzRect pdf_dict_get_rect(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_string()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_dict_get_string(::pdf_obj *dict, ::pdf_obj *key)` => `(const char *, size_t sizep)`
	 */
	FZ_FUNCTION const char *pdf_dict_get_string(const PdfObj& dict, const PdfObj& key, size_t *sizep);

	/** Class-aware wrapper for `::pdf_dict_get_text_string()`.  */
	FZ_FUNCTION const char *pdf_dict_get_text_string(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_text_string_opt()`.  */
	FZ_FUNCTION const char *pdf_dict_get_text_string_opt(const PdfObj& dict, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_dict_get_val()`.  */
	FZ_FUNCTION PdfObj pdf_dict_get_val(const PdfObj& dict, int idx);

	/** Class-aware wrapper for `::pdf_dict_geta()`.  */
	FZ_FUNCTION PdfObj pdf_dict_geta(const PdfObj& dict, const PdfObj& key, const PdfObj& abbrev);

	/** Class-aware wrapper for `::pdf_dict_getp()`.  */
	FZ_FUNCTION PdfObj pdf_dict_getp(const PdfObj& dict, const char *path);

	/** Class-aware wrapper for `::pdf_dict_getp_inheritable()`.  */
	FZ_FUNCTION PdfObj pdf_dict_getp_inheritable(const PdfObj& dict, const char *path);

	/** Class-aware wrapper for `::pdf_dict_gets()`.  */
	FZ_FUNCTION PdfObj pdf_dict_gets(const PdfObj& dict, const char *key);

	/** Class-aware wrapper for `::pdf_dict_gets_inheritable()`.  */
	FZ_FUNCTION PdfObj pdf_dict_gets_inheritable(const PdfObj& dict, const char *key);

	/** Class-aware wrapper for `::pdf_dict_getsa()`.  */
	FZ_FUNCTION PdfObj pdf_dict_getsa(const PdfObj& dict, const char *key, const char *abbrev);

	/** Class-aware wrapper for `::pdf_dict_len()`.  */
	FZ_FUNCTION int pdf_dict_len(const PdfObj& dict);

	/** Class-aware wrapper for `::pdf_dict_put()`.  */
	FZ_FUNCTION void pdf_dict_put(const PdfObj& dict, const PdfObj& key, const PdfObj& val);

	/** Class-aware wrapper for `::pdf_dict_put_array()`.  */
	FZ_FUNCTION PdfObj pdf_dict_put_array(const PdfObj& dict, const PdfObj& key, int initial);

	/** Class-aware wrapper for `::pdf_dict_put_bool()`.  */
	FZ_FUNCTION void pdf_dict_put_bool(const PdfObj& dict, const PdfObj& key, int x);

	/** Class-aware wrapper for `::pdf_dict_put_date()`.  */
	FZ_FUNCTION void pdf_dict_put_date(const PdfObj& dict, const PdfObj& key, int64_t time);

	/** Class-aware wrapper for `::pdf_dict_put_dict()`.  */
	FZ_FUNCTION PdfObj pdf_dict_put_dict(const PdfObj& dict, const PdfObj& key, int initial);

	/** Class-aware wrapper for `::pdf_dict_put_indirect()`.  */
	FZ_FUNCTION void pdf_dict_put_indirect(const PdfObj& dict, const PdfObj& key, int num);

	/** Class-aware wrapper for `::pdf_dict_put_int()`.  */
	FZ_FUNCTION void pdf_dict_put_int(const PdfObj& dict, const PdfObj& key, int64_t x);

	/** Class-aware wrapper for `::pdf_dict_put_matrix()`.  */
	FZ_FUNCTION void pdf_dict_put_matrix(const PdfObj& dict, const PdfObj& key, const FzMatrix& x);

	/** Class-aware wrapper for `::pdf_dict_put_name()`.  */
	FZ_FUNCTION void pdf_dict_put_name(const PdfObj& dict, const PdfObj& key, const char *x);

	/** Class-aware wrapper for `::pdf_dict_put_point()`.  */
	FZ_FUNCTION void pdf_dict_put_point(const PdfObj& dict, const PdfObj& key, const FzPoint& x);

	/** Class-aware wrapper for `::pdf_dict_put_real()`.  */
	FZ_FUNCTION void pdf_dict_put_real(const PdfObj& dict, const PdfObj& key, double x);

	/** Class-aware wrapper for `::pdf_dict_put_rect()`.  */
	FZ_FUNCTION void pdf_dict_put_rect(const PdfObj& dict, const PdfObj& key, const FzRect& x);

	/** Class-aware wrapper for `::pdf_dict_put_string()`.  */
	FZ_FUNCTION void pdf_dict_put_string(const PdfObj& dict, const PdfObj& key, const char *x, size_t n);

	/** Class-aware wrapper for `::pdf_dict_put_text_string()`.  */
	FZ_FUNCTION void pdf_dict_put_text_string(const PdfObj& dict, const PdfObj& key, const char *x);

	/** Class-aware wrapper for `::pdf_dict_put_val_null()`.  */
	FZ_FUNCTION void pdf_dict_put_val_null(const PdfObj& obj, int idx);

	/** Class-aware wrapper for `::pdf_dict_putp()`.  */
	FZ_FUNCTION void pdf_dict_putp(const PdfObj& dict, const char *path, const PdfObj& val);

	/** Class-aware wrapper for `::pdf_dict_puts()`.  */
	FZ_FUNCTION void pdf_dict_puts(const PdfObj& dict, const char *key, const PdfObj& val);

	/** Class-aware wrapper for `::pdf_dict_puts_dict()`.  */
	FZ_FUNCTION PdfObj pdf_dict_puts_dict(const PdfObj& dict, const char *key, int initial);

	/** Class-aware wrapper for `::pdf_dirty_annot()`.  */
	FZ_FUNCTION void pdf_dirty_annot(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_dirty_obj()`.  */
	FZ_FUNCTION void pdf_dirty_obj(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_disable_js()`.  */
	FZ_FUNCTION void pdf_disable_js(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_discard_journal()`.  */
	FZ_FUNCTION void pdf_discard_journal(const PdfJournal& journal);

	/** Class-aware wrapper for `::pdf_doc_was_linearized()`.  */
	FZ_FUNCTION int pdf_doc_was_linearized(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_associated_file()`.  */
	FZ_FUNCTION PdfObj pdf_document_associated_file(const PdfDocument& doc, int idx);

	/** Class-aware wrapper for `::pdf_document_event_did_print()`.  */
	FZ_FUNCTION void pdf_document_event_did_print(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_event_did_save()`.  */
	FZ_FUNCTION void pdf_document_event_did_save(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_event_will_close()`.  */
	FZ_FUNCTION void pdf_document_event_will_close(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_event_will_print()`.  */
	FZ_FUNCTION void pdf_document_event_will_print(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_event_will_save()`.  */
	FZ_FUNCTION void pdf_document_event_will_save(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_from_fz_document()`.  */
	FZ_FUNCTION PdfDocument pdf_document_from_fz_document(const FzDocument& ptr);

	/** Class-aware wrapper for `::pdf_document_language()`.  */
	FZ_FUNCTION ::fz_text_language pdf_document_language(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_output_intent()`.  */
	FZ_FUNCTION FzColorspace pdf_document_output_intent(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_document_permissions()`.  */
	FZ_FUNCTION int pdf_document_permissions(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_edit_text_field_value()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_edit_text_field_value(::pdf_annot *widget, const char *value, const char *change)` => `(int, int selStart, int selEnd, char *newvalue)`
	 */
	FZ_FUNCTION int pdf_edit_text_field_value(const PdfAnnot& widget, const char *value, const char *change, int *selStart, int *selEnd, char **newvalue);

	/** Class-aware wrapper for `::pdf_empty_store()`.  */
	FZ_FUNCTION void pdf_empty_store(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_enable_journal()`.  */
	FZ_FUNCTION void pdf_enable_journal(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_enable_js()`.  */
	FZ_FUNCTION void pdf_enable_js(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_enable_layer()`.  */
	FZ_FUNCTION void pdf_enable_layer(const PdfDocument& doc, int layer, int enabled);

	/** Class-aware wrapper for `::pdf_encrypt_data()`.  */
	FZ_FUNCTION void pdf_encrypt_data(const PdfCrypt& crypt, int num, int gen, void (*fmt_str_out)(::fz_context *, void *, const unsigned char *, size_t ), void *arg, const unsigned char *s, size_t n);

	/** Class-aware wrapper for `::pdf_encrypted_len()`.  */
	FZ_FUNCTION size_t pdf_encrypted_len(const PdfCrypt& crypt, int num, int gen, size_t len);

	/** Class-aware wrapper for `::pdf_end_hmtx()`.  */
	FZ_FUNCTION void pdf_end_hmtx(const PdfFontDesc& font);

	/** Class-aware wrapper for `::pdf_end_operation()`.  */
	FZ_FUNCTION void pdf_end_operation(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_end_vmtx()`.  */
	FZ_FUNCTION void pdf_end_vmtx(const PdfFontDesc& font);

	/** Class-aware wrapper for `::pdf_ensure_solid_xref()`.  */
	FZ_FUNCTION void pdf_ensure_solid_xref(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_eval_function()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_eval_function(::pdf_function *func, const float *in, int inlen, int outlen)` => float out
	 */
	FZ_FUNCTION void pdf_eval_function(const PdfFunction& func, const float *in, int inlen, float *out, int outlen);

	/** Class-aware wrapper for `::pdf_event_issue_alert()`.  */
	FZ_FUNCTION void pdf_event_issue_alert(const PdfDocument& doc, const PdfAlertEvent& evt);

	/** Class-aware wrapper for `::pdf_event_issue_exec_menu_item()`.  */
	FZ_FUNCTION void pdf_event_issue_exec_menu_item(const PdfDocument& doc, const char *item);

	/** Class-aware wrapper for `::pdf_event_issue_launch_url()`.  */
	FZ_FUNCTION void pdf_event_issue_launch_url(const PdfDocument& doc, const char *url, int new_frame);

	/** Class-aware wrapper for `::pdf_event_issue_mail_doc()`.  */
	FZ_FUNCTION void pdf_event_issue_mail_doc(const PdfDocument& doc, const PdfMailDocEvent& evt);

	/** Class-aware wrapper for `::pdf_event_issue_print()`.  */
	FZ_FUNCTION void pdf_event_issue_print(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_field_border_style()`.  */
	FZ_FUNCTION char *pdf_field_border_style(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_display()`.  */
	FZ_FUNCTION int pdf_field_display(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_event_calculate()`.  */
	FZ_FUNCTION void pdf_field_event_calculate(const PdfDocument& doc, const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_event_format()`.  */
	FZ_FUNCTION char *pdf_field_event_format(const PdfDocument& doc, const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_event_keystroke()`.  */
	FZ_FUNCTION int pdf_field_event_keystroke(const PdfDocument& doc, const PdfObj& field, const PdfKeystrokeEvent& evt);

	/** Class-aware wrapper for `::pdf_field_event_validate()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_field_event_validate(::pdf_document *doc, ::pdf_obj *field, const char *value)` => `(int, char *newvalue)`
	 */
	FZ_FUNCTION int pdf_field_event_validate(const PdfDocument& doc, const PdfObj& field, const char *value, char **newvalue);

	/** Class-aware wrapper for `::pdf_field_flags()`.  */
	FZ_FUNCTION int pdf_field_flags(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_label()`.  */
	FZ_FUNCTION const char *pdf_field_label(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_reset()`.  */
	FZ_FUNCTION void pdf_field_reset(const PdfDocument& doc, const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_set_border_style()`.  */
	FZ_FUNCTION void pdf_field_set_border_style(const PdfObj& field, const char *text);

	/** Class-aware wrapper for `::pdf_field_set_button_caption()`.  */
	FZ_FUNCTION void pdf_field_set_button_caption(const PdfObj& field, const char *text);

	/** Class-aware wrapper for `::pdf_field_set_display()`.  */
	FZ_FUNCTION void pdf_field_set_display(const PdfObj& field, int d);

	/** Class-aware wrapper for `::pdf_field_set_fill_color()`.  */
	FZ_FUNCTION void pdf_field_set_fill_color(const PdfObj& field, const PdfObj& col);

	/** Class-aware wrapper for `::pdf_field_set_text_color()`.  */
	FZ_FUNCTION void pdf_field_set_text_color(const PdfObj& field, const PdfObj& col);

	/** Class-aware wrapper for `::pdf_field_type()`.  */
	FZ_FUNCTION int pdf_field_type(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_type_string()`.  */
	FZ_FUNCTION const char *pdf_field_type_string(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_field_value()`.  */
	FZ_FUNCTION const char *pdf_field_value(const PdfObj& field);

	/** Class-aware wrapper for `::pdf_filter_annot_contents()`.  */
	FZ_FUNCTION void pdf_filter_annot_contents(const PdfDocument& doc, const PdfAnnot& annot, PdfFilterOptions& options);

	/** Class-aware wrapper for `::pdf_filter_page_contents()`.  */
	FZ_FUNCTION void pdf_filter_page_contents(const PdfDocument& doc, const PdfPage& page, PdfFilterOptions& options);

	/** Class-aware wrapper for `::pdf_filter_xobject_instance()`.  */
	FZ_FUNCTION PdfObj pdf_filter_xobject_instance(const PdfObj& old_xobj, const PdfObj& page_res, const FzMatrix& ctm, PdfFilterOptions& options, const PdfCycleList& cycle_up);

	/** Class-aware wrapper for `::pdf_find_colorspace_resource()`.  */
	FZ_FUNCTION PdfObj pdf_find_colorspace_resource(const PdfDocument& doc, const FzColorspace& item, const PdfColorspaceResourceKey& key);

	/** Class-aware wrapper for `::pdf_find_font_resource()`.  */
	FZ_FUNCTION PdfObj pdf_find_font_resource(const PdfDocument& doc, int type, int encoding, const FzFont& item, const PdfFontResourceKey& key);

	/** Class-aware wrapper for `::pdf_find_item()`.  */
	FZ_FUNCTION void *pdf_find_item(::fz_store_drop_fn *drop, const PdfObj& key);

	/* Class-aware wrapper for `pdf_find_locked_fields()`
	is not available because returned wrapper class for `pdf_locked_fields`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_find_locked_fields_for_sig()`
	is not available because returned wrapper class for `pdf_locked_fields`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_find_version_for_obj()`.  */
	FZ_FUNCTION int pdf_find_version_for_obj(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_first_annot()`.  */
	FZ_FUNCTION PdfAnnot pdf_first_annot(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_first_widget()`.  */
	FZ_FUNCTION PdfAnnot pdf_first_widget(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_flatten_inheritable_page_items()`.  */
	FZ_FUNCTION void pdf_flatten_inheritable_page_items(const PdfObj& page);

	/** Class-aware wrapper for `::pdf_font_cid_to_gid()`.  */
	FZ_FUNCTION int pdf_font_cid_to_gid(const PdfFontDesc& fontdesc, int cid);

	/** Class-aware wrapper for `::pdf_font_writing_supported()`.  */
	FZ_FUNCTION int pdf_font_writing_supported(const FzFont& font);

	/** Class-aware wrapper for `::pdf_forget_xref()`.  */
	FZ_FUNCTION void pdf_forget_xref(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_format_date()`.  */
	FZ_FUNCTION char *pdf_format_date(int64_t time, char *s, size_t n);

	/** Class-aware wrapper for `::pdf_format_write_options()`.  */
	FZ_FUNCTION char *pdf_format_write_options(char *buffer, size_t buffer_len, PdfWriteOptions& opts);

	/** Class-aware wrapper for `::pdf_function_size()`.  */
	FZ_FUNCTION size_t pdf_function_size(const PdfFunction& func);

	/** Class-aware wrapper for `::pdf_get_bound_document()`.  */
	FZ_FUNCTION PdfDocument pdf_get_bound_document(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_get_doc_event_callback_data()`.  */
	FZ_FUNCTION void *pdf_get_doc_event_callback_data(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_get_filespec_params()`.  */
	FZ_FUNCTION void pdf_get_filespec_params(const PdfObj& fs, const PdfFilespecParams& out);

	/* Class-aware wrapper for `pdf_get_incremental_xref_entry()`
	is not available because returned wrapper class for `pdf_xref_entry`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_get_indirect_document()`.  */
	FZ_FUNCTION PdfDocument pdf_get_indirect_document(const PdfObj& obj);

	/* Class-aware wrapper for `pdf_get_populating_xref_entry()`
	is not available because returned wrapper class for `pdf_xref_entry`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_get_widget_editing_state()`.  */
	FZ_FUNCTION int pdf_get_widget_editing_state(const PdfAnnot& widget);

	/* Class-aware wrapper for `pdf_get_xref_entry()`
	is not available because returned wrapper class for `pdf_xref_entry`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_get_xref_entry_no_change()`
	is not available because returned wrapper class for `pdf_xref_entry`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_get_xref_entry_no_null()`
	is not available because returned wrapper class for `pdf_xref_entry`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_graft_mapped_object()`.  */
	FZ_FUNCTION PdfObj pdf_graft_mapped_object(const PdfGraftMap& map, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_graft_mapped_page()`.  */
	FZ_FUNCTION void pdf_graft_mapped_page(const PdfGraftMap& map, int page_to, const PdfDocument& src, int page_from);

	/** Class-aware wrapper for `::pdf_graft_object()`.  */
	FZ_FUNCTION PdfObj pdf_graft_object(const PdfDocument& dst, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_graft_page()`.  */
	FZ_FUNCTION void pdf_graft_page(const PdfDocument& dst, int page_to, const PdfDocument& src, int page_from);

	/** Class-aware wrapper for `::pdf_has_permission()`.  */
	FZ_FUNCTION int pdf_has_permission(const PdfDocument& doc, ::fz_permission p);

	/** Class-aware wrapper for `::pdf_has_unsaved_changes()`.  */
	FZ_FUNCTION int pdf_has_unsaved_changes(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_has_unsaved_sigs()`.  */
	FZ_FUNCTION int pdf_has_unsaved_sigs(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_incremental_change_since_signing_widget()`.  */
	FZ_FUNCTION int pdf_incremental_change_since_signing_widget(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_insert_colorspace_resource()`.  */
	FZ_FUNCTION PdfObj pdf_insert_colorspace_resource(const PdfDocument& doc, const PdfColorspaceResourceKey& key, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_insert_font_resource()`.  */
	FZ_FUNCTION PdfObj pdf_insert_font_resource(const PdfDocument& doc, const PdfFontResourceKey& key, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_insert_page()`.  */
	FZ_FUNCTION void pdf_insert_page(const PdfDocument& doc, int at, const PdfObj& page);

	/** Class-aware wrapper for `::pdf_intent_from_name()`.  */
	FZ_FUNCTION enum pdf_intent pdf_intent_from_name(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_intent_from_string()`.  */
	FZ_FUNCTION enum pdf_intent pdf_intent_from_string(const char *str);

	/** Class-aware wrapper for `::pdf_invalidate_xfa()`.  */
	FZ_FUNCTION void pdf_invalidate_xfa(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_is_array()`.  */
	FZ_FUNCTION int pdf_is_array(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_bool()`.  */
	FZ_FUNCTION int pdf_is_bool(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_dict()`.  */
	FZ_FUNCTION int pdf_is_dict(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_embedded_file()`.  */
	FZ_FUNCTION int pdf_is_embedded_file(const PdfObj& fs);

	/** Class-aware wrapper for `::pdf_is_field_locked()`.  */
	FZ_FUNCTION int pdf_is_field_locked(const PdfLockedFields& locked, const char *name);

	/** Class-aware wrapper for `::pdf_is_filespec()`.  */
	FZ_FUNCTION int pdf_is_filespec(const PdfObj& fs);

	/** Class-aware wrapper for `::pdf_is_image_stream()`.  */
	FZ_FUNCTION int pdf_is_image_stream(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_indirect()`.  */
	FZ_FUNCTION int pdf_is_indirect(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_int()`.  */
	FZ_FUNCTION int pdf_is_int(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_jpx_image()`.  */
	FZ_FUNCTION int pdf_is_jpx_image(const PdfObj& dict);

	/** Class-aware wrapper for `::pdf_is_local_object()`.  */
	FZ_FUNCTION int pdf_is_local_object(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_name()`.  */
	FZ_FUNCTION int pdf_is_name(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_null()`.  */
	FZ_FUNCTION int pdf_is_null(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_number()`.  */
	FZ_FUNCTION int pdf_is_number(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_ocg_hidden()`.  */
	FZ_FUNCTION int pdf_is_ocg_hidden(const PdfDocument& doc, const PdfObj& rdb, const char *usage, const PdfObj& ocg);

	/** Class-aware wrapper for `::pdf_is_real()`.  */
	FZ_FUNCTION int pdf_is_real(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_stream()`.  */
	FZ_FUNCTION int pdf_is_stream(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_string()`.  */
	FZ_FUNCTION int pdf_is_string(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_is_tint_colorspace()`.  */
	FZ_FUNCTION int pdf_is_tint_colorspace(const FzColorspace& cs);

	/** Class-aware wrapper for `::pdf_js_event_init()`.  */
	FZ_FUNCTION void pdf_js_event_init(const PdfJs& js, const PdfObj& target, const char *value, int willCommit);

	/** Class-aware wrapper for `::pdf_js_event_init_keystroke()`.  */
	FZ_FUNCTION void pdf_js_event_init_keystroke(const PdfJs& js, const PdfObj& target, const PdfKeystrokeEvent& evt);

	/** Class-aware wrapper for `::pdf_js_event_result()`.  */
	FZ_FUNCTION int pdf_js_event_result(const PdfJs& js);

	/** Class-aware wrapper for `::pdf_js_event_result_keystroke()`.  */
	FZ_FUNCTION int pdf_js_event_result_keystroke(const PdfJs& js, const PdfKeystrokeEvent& evt);

	/** Class-aware wrapper for `::pdf_js_event_result_validate()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_js_event_result_validate(::pdf_js *js)` => `(int, char *newvalue)`
	 */
	FZ_FUNCTION int pdf_js_event_result_validate(const PdfJs& js, char **newvalue);

	/** Class-aware wrapper for `::pdf_js_event_value()`.  */
	FZ_FUNCTION char *pdf_js_event_value(const PdfJs& js);

	/** Class-aware wrapper for `::pdf_js_execute()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_js_execute(::pdf_js *js, const char *name, const char *code)` => char *result
	 */
	FZ_FUNCTION void pdf_js_execute(const PdfJs& js, const char *name, const char *code, char **result);

	/* Class-aware wrapper for `pdf_js_get_console()`
	is not available because returned wrapper class for `pdf_js_console`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_js_set_console()`.  */
	FZ_FUNCTION void pdf_js_set_console(const PdfDocument& doc, const PdfJsConsole& console, void *user);

	/** Class-aware wrapper for `::pdf_js_supported()`.  */
	FZ_FUNCTION int pdf_js_supported(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_label_object()`.  */
	FZ_FUNCTION void pdf_label_object(const PdfObjectLabels& g, int num, ::pdf_label_object_fn *callback, void *arg);

	/** Class-aware wrapper for `::pdf_layer_config_info()`.  */
	FZ_FUNCTION void pdf_layer_config_info(const PdfDocument& doc, int config_num, PdfLayerConfig& info);

	/** Class-aware wrapper for `::pdf_layer_config_ui_info()`.  */
	FZ_FUNCTION void pdf_layer_config_ui_info(const PdfDocument& doc, int ui, PdfLayerConfigUi& info);

	/** Class-aware wrapper for `::pdf_layer_is_enabled()`.  */
	FZ_FUNCTION int pdf_layer_is_enabled(const PdfDocument& doc, int layer);

	/** Class-aware wrapper for `::pdf_layer_name()`.  */
	FZ_FUNCTION const char *pdf_layer_name(const PdfDocument& doc, int layer);

	/** Class-aware wrapper for `::pdf_layout_fit_text()`.  */
	FZ_FUNCTION FzText pdf_layout_fit_text(const FzFont& font, ::fz_text_language lang, const char *str, const FzRect& bounds);

	/* Class-aware wrapper for `pdf_layout_text_widget()`
	is not available because returned wrapper class for `fz_layout_block`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_lex()`.  */
	FZ_FUNCTION ::pdf_token pdf_lex(const FzStream& f, const PdfLexbuf& lexbuf);

	/** Class-aware wrapper for `::pdf_lex_no_string()`.  */
	FZ_FUNCTION ::pdf_token pdf_lex_no_string(const FzStream& f, const PdfLexbuf& lexbuf);

	/** Class-aware wrapper for `::pdf_lexbuf_fin()`.  */
	FZ_FUNCTION void pdf_lexbuf_fin(const PdfLexbuf& lexbuf);

	/** Class-aware wrapper for `::pdf_lexbuf_grow()`.  */
	FZ_FUNCTION ptrdiff_t pdf_lexbuf_grow(const PdfLexbuf& lexbuf);

	/** Class-aware wrapper for `::pdf_lexbuf_init()`.  */
	FZ_FUNCTION void pdf_lexbuf_init(const PdfLexbuf& lexbuf, int size);

	/** Class-aware wrapper for `::pdf_line_ending_from_name()`.  */
	FZ_FUNCTION enum pdf_line_ending pdf_line_ending_from_name(const PdfObj& end);

	/** Class-aware wrapper for `::pdf_line_ending_from_string()`.  */
	FZ_FUNCTION enum pdf_line_ending pdf_line_ending_from_string(const char *end);

	/** Class-aware wrapper for `::pdf_load_builtin_cmap()`.  */
	FZ_FUNCTION PdfCmap pdf_load_builtin_cmap(const char *name);

	/** Class-aware wrapper for `::pdf_load_cmap()`.  */
	FZ_FUNCTION PdfCmap pdf_load_cmap(const FzStream& file);

	/** Class-aware wrapper for `::pdf_load_colorspace()`.  */
	FZ_FUNCTION FzColorspace pdf_load_colorspace(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_load_compressed_inline_image()`.  */
	FZ_FUNCTION void pdf_load_compressed_inline_image(const PdfDocument& doc, const PdfObj& dict, int length, const FzStream& cstm, int indexed, const FzCompressedImage& image);

	/** Class-aware wrapper for `::pdf_load_compressed_stream()`.  */
	FZ_FUNCTION FzCompressedBuffer pdf_load_compressed_stream(const PdfDocument& doc, int num, size_t worst_case);

	/** Class-aware wrapper for `::pdf_load_default_colorspaces()`.  */
	FZ_FUNCTION FzDefaultColorspaces pdf_load_default_colorspaces(const PdfDocument& doc, const PdfPage& page);

	/** Class-aware wrapper for `::pdf_load_embedded_cmap()`.  */
	FZ_FUNCTION PdfCmap pdf_load_embedded_cmap(const PdfDocument& doc, const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_load_embedded_file_contents()`.  */
	FZ_FUNCTION FzBuffer pdf_load_embedded_file_contents(const PdfObj& fs);

	/** Class-aware wrapper for `::pdf_load_encoding()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_load_encoding(const char *encoding)` => const char *estrings
	 */
	FZ_FUNCTION void pdf_load_encoding(const char **estrings, const char *encoding);

	/** Class-aware wrapper for `::pdf_load_field_name()`.  */
	FZ_FUNCTION char *pdf_load_field_name(const PdfObj& field);

	/** Alternative to `pdf_load_field_name()` that returns a std::string. */
	FZ_FUNCTION std::string pdf_load_field_name2(PdfObj& field);

	/* Class-aware wrapper for `pdf_load_font()`
	is not available because returned wrapper class for `pdf_font_desc`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_load_function()`.  */
	FZ_FUNCTION PdfFunction pdf_load_function(const PdfObj& ref, int in, int out);

	/* Class-aware wrapper for `pdf_load_hail_mary_font()`
	is not available because returned wrapper class for `pdf_font_desc`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_load_image()`.  */
	FZ_FUNCTION FzImage pdf_load_image(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_load_inline_image()`.  */
	FZ_FUNCTION FzImage pdf_load_inline_image(const PdfDocument& doc, const PdfObj& rdb, const PdfObj& dict, const FzStream& file);

	/** Class-aware wrapper for `::pdf_load_journal()`.  */
	FZ_FUNCTION void pdf_load_journal(const PdfDocument& doc, const char *filename);

	/** Class-aware wrapper for `::pdf_load_link_annots()`.  */
	FZ_FUNCTION FzLink pdf_load_link_annots(const PdfDocument& arg_0, const PdfPage& arg_1, const PdfObj& annots, int pagenum, const FzMatrix& page_ctm);

	/** Class-aware wrapper for `::pdf_load_links()`.  */
	FZ_FUNCTION FzLink pdf_load_links(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_load_name_tree()`.  */
	FZ_FUNCTION PdfObj pdf_load_name_tree(const PdfDocument& doc, const PdfObj& which);

	/** Class-aware wrapper for `::pdf_load_object()`.  */
	/**
		Load a given object.
	
		This can cause xref reorganisations (solidifications etc) due to
		repairs, so all held pdf_xref_entries should be considered
		invalid after this call (other than the returned one).
	*/
	FZ_FUNCTION PdfObj pdf_load_object(const PdfDocument& doc, int num);

	/* Class-aware wrapper for `pdf_load_object_labels()`
	is not available because returned wrapper class for `pdf_object_labels`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_load_outline()`.  */
	FZ_FUNCTION FzOutline pdf_load_outline(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_load_page()`.  */
	FZ_FUNCTION PdfPage pdf_load_page(const PdfDocument& doc, int number);

	/** Class-aware wrapper for `::pdf_load_page_imp()`.  */
	FZ_FUNCTION FzPage pdf_load_page_imp(const FzDocument& doc, int chapter, int number);

	/** Class-aware wrapper for `::pdf_load_page_tree()`.  */
	FZ_FUNCTION void pdf_load_page_tree(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_load_pattern()`.  */
	FZ_FUNCTION PdfPattern pdf_load_pattern(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_load_raw_stream()`.  */
	FZ_FUNCTION FzBuffer pdf_load_raw_stream(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_load_raw_stream_number()`.  */
	FZ_FUNCTION FzBuffer pdf_load_raw_stream_number(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_load_shading()`.  */
	FZ_FUNCTION FzShade pdf_load_shading(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_load_stream()`.  */
	FZ_FUNCTION FzBuffer pdf_load_stream(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_load_stream_number()`.  */
	FZ_FUNCTION FzBuffer pdf_load_stream_number(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_load_stream_or_string_as_utf8()`.  */
	FZ_FUNCTION char *pdf_load_stream_or_string_as_utf8(const PdfObj& src);

	/** Class-aware wrapper for `::pdf_load_system_cmap()`.  */
	FZ_FUNCTION PdfCmap pdf_load_system_cmap(const char *name);

	/** Class-aware wrapper for `::pdf_load_to_unicode()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_load_to_unicode(::pdf_document *doc, ::pdf_font_desc *font, char *collection, ::pdf_obj *cmapstm)` => const char *strings
	 */
	FZ_FUNCTION void pdf_load_to_unicode(const PdfDocument& doc, const PdfFontDesc& font, const char **strings, char *collection, const PdfObj& cmapstm);

	/* Class-aware wrapper for `pdf_load_type3_font()`
	is not available because returned wrapper class for `pdf_font_desc`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_load_type3_glyphs()`.  */
	FZ_FUNCTION void pdf_load_type3_glyphs(const PdfDocument& doc, const PdfFontDesc& fontdesc);

	/** Class-aware wrapper for `::pdf_load_unencrypted_object()`.  */
	FZ_FUNCTION PdfObj pdf_load_unencrypted_object(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_lookup_cmap()`.  */
	FZ_FUNCTION int pdf_lookup_cmap(const PdfCmap& cmap, unsigned int cpt);

	/** Class-aware wrapper for `::pdf_lookup_cmap_full()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_lookup_cmap_full(::pdf_cmap *cmap, unsigned int cpt)` => `(int, int out)`
	 */
	FZ_FUNCTION int pdf_lookup_cmap_full(const PdfCmap& cmap, unsigned int cpt, int *out);

	/** Class-aware wrapper for `::pdf_lookup_dest()`.  */
	FZ_FUNCTION PdfObj pdf_lookup_dest(const PdfDocument& doc, const PdfObj& needle);

	/** Class-aware wrapper for `::pdf_lookup_field()`.  */
	FZ_FUNCTION PdfObj pdf_lookup_field(const PdfObj& form, const char *name);

	/* Class-aware wrapper for `pdf_lookup_hmtx()`
	is not available because returned wrapper class for `pdf_hmtx`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_lookup_metadata()`.  */
	FZ_FUNCTION int pdf_lookup_metadata(const PdfDocument& doc, const char *key, char *ptr, size_t size);

	/** Class-aware wrapper for `::pdf_lookup_metadata2()`.  */
	/**
	C++ alternative to `pdf_lookup_metadata()` that returns a `std::string`
	or calls `fz_throw()` if not found.
	*/
	FZ_FUNCTION std::string pdf_lookup_metadata2(const PdfDocument& doc, const char *key);

	/** Class-aware wrapper for `::pdf_lookup_name()`.  */
	FZ_FUNCTION PdfObj pdf_lookup_name(const PdfDocument& doc, const PdfObj& which, const PdfObj& needle);

	/** Class-aware wrapper for `::pdf_lookup_number()`.  */
	FZ_FUNCTION PdfObj pdf_lookup_number(const PdfObj& root, int needle);

	/** Class-aware wrapper for `::pdf_lookup_page_loc()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_lookup_page_loc(::pdf_document *doc, int needle, ::pdf_obj **parentp)` => `(pdf_obj *, int indexp)`
	 */
	FZ_FUNCTION PdfObj pdf_lookup_page_loc(const PdfDocument& doc, int needle, PdfObj& parentp, int *indexp);

	/** Class-aware wrapper for `::pdf_lookup_page_number()`.  */
	FZ_FUNCTION int pdf_lookup_page_number(const PdfDocument& doc, const PdfObj& pageobj);

	/** Class-aware wrapper for `::pdf_lookup_page_obj()`.  */
	FZ_FUNCTION PdfObj pdf_lookup_page_obj(const PdfDocument& doc, int needle);

	/** Class-aware wrapper for `::pdf_lookup_substitute_font()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_lookup_substitute_font(int mono, int serif, int bold, int italic)` => `(const unsigned char *, int len)`
	 */
	FZ_FUNCTION const unsigned char *pdf_lookup_substitute_font(int mono, int serif, int bold, int italic, int *len);

	/* Class-aware wrapper for `pdf_lookup_vmtx()`
	is not available because returned wrapper class for `pdf_vmtx`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_map_one_to_many()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_map_one_to_many(::pdf_cmap *cmap, unsigned int one, size_t len)` => int many
	 */
	FZ_FUNCTION void pdf_map_one_to_many(const PdfCmap& cmap, unsigned int one, int *many, size_t len);

	/** Class-aware wrapper for `::pdf_map_range_to_range()`.  */
	FZ_FUNCTION void pdf_map_range_to_range(const PdfCmap& cmap, unsigned int srclo, unsigned int srchi, int dstlo);

	/** Class-aware wrapper for `::pdf_mark_bits_reset()`.  */
	FZ_FUNCTION void pdf_mark_bits_reset(const PdfMarkBits& marks);

	/** Class-aware wrapper for `::pdf_mark_bits_set()`.  */
	FZ_FUNCTION int pdf_mark_bits_set(const PdfMarkBits& marks, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_mark_list_check()`.  */
	FZ_FUNCTION int pdf_mark_list_check(const PdfMarkList& list, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_mark_list_free()`.  */
	FZ_FUNCTION void pdf_mark_list_free(const PdfMarkList& list);

	/** Class-aware wrapper for `::pdf_mark_list_init()`.  */
	FZ_FUNCTION void pdf_mark_list_init(const PdfMarkList& list);

	/** Class-aware wrapper for `::pdf_mark_list_pop()`.  */
	FZ_FUNCTION void pdf_mark_list_pop(const PdfMarkList& list);

	/** Class-aware wrapper for `::pdf_mark_list_push()`.  */
	FZ_FUNCTION int pdf_mark_list_push(const PdfMarkList& list, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_mark_obj()`.  */
	FZ_FUNCTION int pdf_mark_obj(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_mark_xref()`.  */
	FZ_FUNCTION void pdf_mark_xref(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_metadata()`.  */
	FZ_FUNCTION PdfObj pdf_metadata(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_minimize_document()`.  */
	FZ_FUNCTION void pdf_minimize_document(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_name_eq()`.  */
	FZ_FUNCTION int pdf_name_eq(const PdfObj& a, const PdfObj& b);

	/** Class-aware wrapper for `::pdf_name_from_intent()`.  */
	FZ_FUNCTION PdfObj pdf_name_from_intent(enum pdf_intent intent);

	/** Class-aware wrapper for `::pdf_name_from_line_ending()`.  */
	FZ_FUNCTION PdfObj pdf_name_from_line_ending(enum pdf_line_ending end);

	/** Class-aware wrapper for `::pdf_needs_password()`.  */
	FZ_FUNCTION int pdf_needs_password(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_new_action_from_link()`.  */
	FZ_FUNCTION PdfObj pdf_new_action_from_link(const PdfDocument& doc, const char *uri);

	/** Class-aware wrapper for `::pdf_new_array()`.  */
	FZ_FUNCTION PdfObj pdf_new_array(const PdfDocument& doc, int initialcap);

	/** Class-aware wrapper for `::pdf_new_buffer_processor()`.  */
	FZ_FUNCTION PdfProcessor pdf_new_buffer_processor(const FzBuffer& buffer, int ahxencode, int newlines);

	/** Class-aware wrapper for `::pdf_new_cmap()`.  */
	FZ_FUNCTION PdfCmap pdf_new_cmap();

	/** Class-aware wrapper for `::pdf_new_color_filter()`.  */
	FZ_FUNCTION PdfProcessor pdf_new_color_filter(const PdfDocument& doc, const PdfProcessor& chain, int struct_parents, const FzMatrix& transform, PdfFilterOptions& options, void *copts);

	/* Class-aware wrapper for `pdf_new_crypt()`
	is not available because returned wrapper class for `pdf_crypt`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_new_date()`.  */
	FZ_FUNCTION PdfObj pdf_new_date(const PdfDocument& doc, int64_t time);

	/** Class-aware wrapper for `::pdf_new_dest_from_link()`.  */
	FZ_FUNCTION PdfObj pdf_new_dest_from_link(const PdfDocument& doc, const char *uri, int is_remote);

	/** Class-aware wrapper for `::pdf_new_dict()`.  */
	FZ_FUNCTION PdfObj pdf_new_dict(const PdfDocument& doc, int initialcap);

	/** Class-aware wrapper for `::pdf_new_display_list_from_annot()`.  */
	FZ_FUNCTION FzDisplayList pdf_new_display_list_from_annot(const PdfAnnot& annot);

	/* Class-aware wrapper for `pdf_new_encrypt()`
	is not available because returned wrapper class for `pdf_crypt`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_new_font_desc()`
	is not available because returned wrapper class for `pdf_font_desc`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_new_graft_map()`.  */
	FZ_FUNCTION PdfGraftMap pdf_new_graft_map(const PdfDocument& dst);

	/** Class-aware wrapper for `::pdf_new_identity_cmap()`.  */
	FZ_FUNCTION PdfCmap pdf_new_identity_cmap(int wmode, int bytes);

	/** Class-aware wrapper for `::pdf_new_indirect()`.  */
	FZ_FUNCTION PdfObj pdf_new_indirect(const PdfDocument& doc, int num, int gen);

	/** Class-aware wrapper for `::pdf_new_int()`.  */
	FZ_FUNCTION PdfObj pdf_new_int(int64_t i);

	/** Class-aware wrapper for `::pdf_new_link()`.  */
	FZ_FUNCTION FzLink pdf_new_link(const PdfPage& page, const FzRect& rect, const char *uri, const PdfObj& obj);

	/* Class-aware wrapper for `pdf_new_local_xref()`
	is not available because returned wrapper class for `pdf_xref`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_new_mark_bits()`
	is not available because returned wrapper class for `pdf_mark_bits`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_new_matrix()`.  */
	FZ_FUNCTION PdfObj pdf_new_matrix(const PdfDocument& doc, const FzMatrix& mtx);

	/** Class-aware wrapper for `::pdf_new_name()`.  */
	FZ_FUNCTION PdfObj pdf_new_name(const char *str);

	/* Class-aware wrapper for `pdf_new_outline_iterator()`
	is not available because returned wrapper class for `fz_outline_iterator`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_new_output_processor()`.  */
	FZ_FUNCTION PdfProcessor pdf_new_output_processor(const FzOutput& out, int ahxencode, int newlines);

	/** Class-aware wrapper for `::pdf_new_pdf_device()`.  */
	FZ_FUNCTION FzDevice pdf_new_pdf_device(const PdfDocument& doc, const FzMatrix& topctm, const PdfObj& resources, const FzBuffer& contents);

	/** Class-aware wrapper for `::pdf_new_pixmap_from_annot()`.  */
	FZ_FUNCTION FzPixmap pdf_new_pixmap_from_annot(const PdfAnnot& annot, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha);

	/** Class-aware wrapper for `::pdf_new_pixmap_from_page_contents_with_separations_and_usage()`.  */
	FZ_FUNCTION FzPixmap pdf_new_pixmap_from_page_contents_with_separations_and_usage(const PdfPage& page, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha, const char *usage, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_new_pixmap_from_page_contents_with_usage()`.  */
	FZ_FUNCTION FzPixmap pdf_new_pixmap_from_page_contents_with_usage(const PdfPage& page, const FzMatrix& ctm, const FzColorspace& cs, int alpha, const char *usage, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_new_pixmap_from_page_with_separations_and_usage()`.  */
	FZ_FUNCTION FzPixmap pdf_new_pixmap_from_page_with_separations_and_usage(const PdfPage& page, const FzMatrix& ctm, const FzColorspace& cs, const FzSeparations& seps, int alpha, const char *usage, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_new_pixmap_from_page_with_usage()`.  */
	FZ_FUNCTION FzPixmap pdf_new_pixmap_from_page_with_usage(const PdfPage& page, const FzMatrix& ctm, const FzColorspace& cs, int alpha, const char *usage, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_new_point()`.  */
	FZ_FUNCTION PdfObj pdf_new_point(const PdfDocument& doc, const FzPoint& point);

	/** Class-aware wrapper for `::pdf_new_processor()`.  */
	FZ_FUNCTION void *pdf_new_processor(int size);

	/** Class-aware wrapper for `::pdf_new_real()`.  */
	FZ_FUNCTION PdfObj pdf_new_real(float f);

	/** Class-aware wrapper for `::pdf_new_rect()`.  */
	FZ_FUNCTION PdfObj pdf_new_rect(const PdfDocument& doc, const FzRect& rect);

	/** Class-aware wrapper for `::pdf_new_run_processor()`.  */
	FZ_FUNCTION PdfProcessor pdf_new_run_processor(const PdfDocument& doc, const FzDevice& dev, const FzMatrix& ctm, int struct_parent, const char *usage, const PdfGstate& gstate, const FzDefaultColorspaces& default_cs, FzCookie& cookie, const PdfGstate& fill_gstate, const PdfGstate& stroke_gstate);

	/** Class-aware wrapper for `::pdf_new_sanitize_filter()`.  */
	FZ_FUNCTION PdfProcessor pdf_new_sanitize_filter(const PdfDocument& doc, const PdfProcessor& chain, int struct_parents, const FzMatrix& transform, PdfFilterOptions& options, void *sopts);

	/* Class-aware wrapper for `pdf_new_stext_page_from_annot()`
	is not available because returned wrapper class for `fz_stext_page`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_new_string()`.  */
	FZ_FUNCTION PdfObj pdf_new_string(const char *str, size_t len);

	/** Class-aware wrapper for `::pdf_new_text_string()`.  */
	FZ_FUNCTION PdfObj pdf_new_text_string(const char *s);

	/** Class-aware wrapper for `::pdf_new_uri_from_explicit_dest()`.  */
	FZ_FUNCTION char *pdf_new_uri_from_explicit_dest(const FzLinkDest& dest);

	/** Class-aware wrapper for `::pdf_new_uri_from_path_and_explicit_dest()`.  */
	FZ_FUNCTION char *pdf_new_uri_from_path_and_explicit_dest(const char *path, const FzLinkDest& dest);

	/** Class-aware wrapper for `::pdf_new_uri_from_path_and_named_dest()`.  */
	FZ_FUNCTION char *pdf_new_uri_from_path_and_named_dest(const char *path, const char *name);

	/** Class-aware wrapper for `::pdf_new_utf8_from_pdf_stream_obj()`.  */
	FZ_FUNCTION char *pdf_new_utf8_from_pdf_stream_obj(const PdfObj& src);

	/** Class-aware wrapper for `::pdf_new_utf8_from_pdf_string()`.  */
	FZ_FUNCTION char *pdf_new_utf8_from_pdf_string(const char *srcptr, size_t srclen);

	/** Class-aware wrapper for `::pdf_new_utf8_from_pdf_string_obj()`.  */
	FZ_FUNCTION char *pdf_new_utf8_from_pdf_string_obj(const PdfObj& src);

	/** Class-aware wrapper for `::pdf_new_xobject()`.  */
	FZ_FUNCTION PdfObj pdf_new_xobject(const PdfDocument& doc, const FzRect& bbox, const FzMatrix& matrix, const PdfObj& res, const FzBuffer& buffer);

	/** Class-aware wrapper for `::pdf_next_annot()`.  */
	FZ_FUNCTION PdfAnnot pdf_next_annot(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_next_widget()`.  */
	FZ_FUNCTION PdfAnnot pdf_next_widget(const PdfAnnot& previous);

	/** Class-aware wrapper for `::pdf_nuke_annots()`.  */
	FZ_FUNCTION void pdf_nuke_annots(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_nuke_links()`.  */
	FZ_FUNCTION void pdf_nuke_links(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_nuke_page()`.  */
	FZ_FUNCTION void pdf_nuke_page(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_obj_is_dirty()`.  */
	FZ_FUNCTION int pdf_obj_is_dirty(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_obj_is_incremental()`.  */
	FZ_FUNCTION int pdf_obj_is_incremental(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_obj_marked()`.  */
	FZ_FUNCTION int pdf_obj_marked(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_obj_memo()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_obj_memo(::pdf_obj *obj, int bit)` => `(int, int memo)`
	 */
	FZ_FUNCTION int pdf_obj_memo(const PdfObj& obj, int bit, int *memo);

	/** Class-aware wrapper for `::pdf_obj_num_is_stream()`.  */
	FZ_FUNCTION int pdf_obj_num_is_stream(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_obj_parent_num()`.  */
	FZ_FUNCTION int pdf_obj_parent_num(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_obj_refs()`.  */
	FZ_FUNCTION int pdf_obj_refs(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_objcmp()`.  */
	FZ_FUNCTION int pdf_objcmp(const PdfObj& a, const PdfObj& b);

	/** Class-aware wrapper for `::pdf_objcmp_deep()`.  */
	FZ_FUNCTION int pdf_objcmp_deep(const PdfObj& a, const PdfObj& b);

	/** Class-aware wrapper for `::pdf_objcmp_resolve()`.  */
	FZ_FUNCTION int pdf_objcmp_resolve(const PdfObj& a, const PdfObj& b);

	/** Class-aware wrapper for `::pdf_object_exists()`.  */
	FZ_FUNCTION int pdf_object_exists(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_open_contents_stream()`.  */
	FZ_FUNCTION FzStream pdf_open_contents_stream(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_open_crypt()`.  */
	FZ_FUNCTION FzStream pdf_open_crypt(const FzStream& chain, const PdfCrypt& crypt, int num, int gen);

	/** Class-aware wrapper for `::pdf_open_crypt_with_filter()`.  */
	FZ_FUNCTION FzStream pdf_open_crypt_with_filter(const FzStream& chain, const PdfCrypt& crypt, const PdfObj& name, int num, int gen);

	/** Class-aware wrapper for `::pdf_open_document()`.  */
	FZ_FUNCTION PdfDocument pdf_open_document(const char *filename);

	/** Class-aware wrapper for `::pdf_open_document_with_stream()`.  */
	FZ_FUNCTION PdfDocument pdf_open_document_with_stream(const FzStream& file);

	/** Class-aware wrapper for `::pdf_open_inline_stream()`.  */
	FZ_FUNCTION FzStream pdf_open_inline_stream(const PdfDocument& doc, const PdfObj& stmobj, int length, const FzStream& chain, const FzCompressionParams& params);

	/** Class-aware wrapper for `::pdf_open_raw_stream()`.  */
	FZ_FUNCTION FzStream pdf_open_raw_stream(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_open_raw_stream_number()`.  */
	FZ_FUNCTION FzStream pdf_open_raw_stream_number(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_open_stream()`.  */
	FZ_FUNCTION FzStream pdf_open_stream(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_open_stream_number()`.  */
	FZ_FUNCTION FzStream pdf_open_stream_number(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_open_stream_with_offset()`.  */
	FZ_FUNCTION FzStream pdf_open_stream_with_offset(const PdfDocument& doc, int num, const PdfObj& dict, int64_t stm_ofs);

	/** Class-aware wrapper for `::pdf_page_associated_file()`.  */
	FZ_FUNCTION PdfObj pdf_page_associated_file(const PdfPage& page, int idx);

	/** Class-aware wrapper for `::pdf_page_contents()`.  */
	FZ_FUNCTION PdfObj pdf_page_contents(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_event_close()`.  */
	FZ_FUNCTION void pdf_page_event_close(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_event_open()`.  */
	FZ_FUNCTION void pdf_page_event_open(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_from_fz_page()`.  */
	FZ_FUNCTION PdfPage pdf_page_from_fz_page(const FzPage& ptr);

	/** Class-aware wrapper for `::pdf_page_group()`.  */
	FZ_FUNCTION PdfObj pdf_page_group(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_has_transparency()`.  */
	FZ_FUNCTION int pdf_page_has_transparency(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_label()`.  */
	FZ_FUNCTION void pdf_page_label(const PdfDocument& doc, int page, char *buf, size_t size);

	/** Class-aware wrapper for `::pdf_page_label_imp()`.  */
	FZ_FUNCTION void pdf_page_label_imp(const FzDocument& doc, int chapter, int page, char *buf, size_t size);

	/** Class-aware wrapper for `::pdf_page_obj_transform()`.  */
	FZ_FUNCTION void pdf_page_obj_transform(const PdfObj& pageobj, FzRect& outbox, FzMatrix& outctm);

	/** Class-aware wrapper for `::pdf_page_obj_transform_box()`.  */
	FZ_FUNCTION void pdf_page_obj_transform_box(const PdfObj& pageobj, FzRect& outbox, FzMatrix& out, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_page_presentation()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_page_presentation(::pdf_page *page, ::fz_transition *transition)` => `(fz_transition *, float duration)`
	 */
	FZ_FUNCTION FzTransition pdf_page_presentation(const PdfPage& page, FzTransition& transition, float *duration);

	/** Class-aware wrapper for `::pdf_page_resources()`.  */
	FZ_FUNCTION PdfObj pdf_page_resources(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_separations()`.  */
	FZ_FUNCTION FzSeparations pdf_page_separations(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_page_transform()`.  */
	FZ_FUNCTION void pdf_page_transform(const PdfPage& page, FzRect& mediabox, FzMatrix& ctm);

	/** Class-aware wrapper for `::pdf_page_transform_box()`.  */
	FZ_FUNCTION void pdf_page_transform_box(const PdfPage& page, FzRect& mediabox, FzMatrix& ctm, ::fz_box_type box);

	/** Class-aware wrapper for `::pdf_page_write()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_page_write(::pdf_document *doc, ::fz_rect mediabox, ::pdf_obj **presources, ::fz_buffer **pcontents)` => `(fz_device *)`
	 */
	FZ_FUNCTION FzDevice pdf_page_write(const PdfDocument& doc, const FzRect& mediabox, PdfObj& presources, FzBuffer& pcontents);

	/** Class-aware wrapper for `::pdf_parse_array()`.  */
	FZ_FUNCTION PdfObj pdf_parse_array(const PdfDocument& doc, const FzStream& f, const PdfLexbuf& buf);

	/** Class-aware wrapper for `::pdf_parse_date()`.  */
	FZ_FUNCTION int64_t pdf_parse_date(const char *s);

	/** Class-aware wrapper for `::pdf_parse_default_appearance()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_parse_default_appearance(const char *da, float color[4])` => `(const char *font, float size, int n)`
	 */
	FZ_FUNCTION void pdf_parse_default_appearance(const char *da, const char **font, float *size, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_parse_default_appearance_unmapped()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_parse_default_appearance_unmapped(const char *da, char *font_name, int font_name_len, float color[4])` => `(float size, int n)`
	 */
	FZ_FUNCTION void pdf_parse_default_appearance_unmapped(const char *da, char *font_name, int font_name_len, float *size, int *n, float color[4]);

	/** Class-aware wrapper for `::pdf_parse_dict()`.  */
	FZ_FUNCTION PdfObj pdf_parse_dict(const PdfDocument& doc, const FzStream& f, const PdfLexbuf& buf);

	/** Class-aware wrapper for `::pdf_parse_ind_obj()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_parse_ind_obj(::pdf_document *doc, ::fz_stream *f)` => `(pdf_obj *, int num, int gen, int64_t stm_ofs, int try_repair)`
	 */
	FZ_FUNCTION PdfObj pdf_parse_ind_obj(const PdfDocument& doc, const FzStream& f, int *num, int *gen, int64_t *stm_ofs, int *try_repair);

	/** Class-aware wrapper for `::pdf_parse_journal_obj()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_parse_journal_obj(::pdf_document *doc, ::fz_stream *stm, ::fz_buffer **ostm)` => `(pdf_obj *, int onum, int newobj)`
	 */
	FZ_FUNCTION PdfObj pdf_parse_journal_obj(const PdfDocument& doc, const FzStream& stm, int *onum, FzBuffer& ostm, int *newobj);

	/** Class-aware wrapper for `::pdf_parse_stm_obj()`.  */
	FZ_FUNCTION PdfObj pdf_parse_stm_obj(const PdfDocument& doc, const FzStream& f, const PdfLexbuf& buf);

	/** Class-aware wrapper for `::pdf_parse_write_options()`.  */
	FZ_FUNCTION PdfWriteOptions pdf_parse_write_options(PdfWriteOptions& opts, const char *args);

	/** Class-aware wrapper for `::pdf_pin_document()`.  */
	FZ_FUNCTION PdfDocument pdf_pin_document(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_preview_signature_as_display_list()`.  */
	FZ_FUNCTION FzDisplayList pdf_preview_signature_as_display_list(float w, float h, ::fz_text_language lang, const PdfPkcs7Signer& signer, int appearance_flags, const FzImage& graphic, const char *reason, const char *location);

	/** Class-aware wrapper for `::pdf_preview_signature_as_pixmap()`.  */
	FZ_FUNCTION FzPixmap pdf_preview_signature_as_pixmap(int w, int h, ::fz_text_language lang, const PdfPkcs7Signer& signer, int appearance_flags, const FzImage& graphic, const char *reason, const char *location);

	/** Class-aware wrapper for `::pdf_print_crypt()`.  */
	FZ_FUNCTION void pdf_print_crypt(const FzOutput& out, const PdfCrypt& crypt);

	/** Class-aware wrapper for `::pdf_print_default_appearance()`.  */
	FZ_FUNCTION void pdf_print_default_appearance(char *buf, int nbuf, const char *font, float size, int n, const float *color);

	/** Class-aware wrapper for `::pdf_print_encrypted_obj()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_print_encrypted_obj(::fz_output *out, ::pdf_obj *obj, int tight, int ascii, ::pdf_crypt *crypt, int num, int gen)` => int sep
	 */
	FZ_FUNCTION void pdf_print_encrypted_obj(const FzOutput& out, const PdfObj& obj, int tight, int ascii, const PdfCrypt& crypt, int num, int gen, int *sep);

	/** Class-aware wrapper for `::pdf_print_font()`.  */
	FZ_FUNCTION void pdf_print_font(const FzOutput& out, const PdfFontDesc& fontdesc);

	/** Class-aware wrapper for `::pdf_print_obj()`.  */
	FZ_FUNCTION void pdf_print_obj(const FzOutput& out, const PdfObj& obj, int tight, int ascii);

	/** Class-aware wrapper for `::pdf_process_annot()`.  */
	FZ_FUNCTION void pdf_process_annot(const PdfProcessor& proc, const PdfAnnot& annot, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_process_contents()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_process_contents(::pdf_processor *proc, ::pdf_document *doc, ::pdf_obj *res, ::pdf_obj *stm, ::fz_cookie *cookie, ::pdf_obj **out_res)` => 
	 */
	FZ_FUNCTION void pdf_process_contents(const PdfProcessor& proc, const PdfDocument& doc, const PdfObj& res, const PdfObj& stm, FzCookie& cookie, PdfObj& out_res);

	/** Class-aware wrapper for `::pdf_process_glyph()`.  */
	FZ_FUNCTION void pdf_process_glyph(const PdfProcessor& proc, const PdfDocument& doc, const PdfObj& resources, const FzBuffer& contents);

	/** Class-aware wrapper for `::pdf_process_raw_contents()`.  */
	FZ_FUNCTION void pdf_process_raw_contents(const PdfProcessor& proc, const PdfDocument& doc, const PdfObj& rdb, const PdfObj& stmobj, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_processor_pop_resources()`.  */
	FZ_FUNCTION PdfObj pdf_processor_pop_resources(const PdfProcessor& proc);

	/** Class-aware wrapper for `::pdf_processor_push_resources()`.  */
	FZ_FUNCTION void pdf_processor_push_resources(const PdfProcessor& proc, const PdfObj& res);

	/** Class-aware wrapper for `::pdf_progressive_advance()`.  */
	FZ_FUNCTION PdfObj pdf_progressive_advance(const PdfDocument& doc, int pagenum);

	/** Class-aware wrapper for `::pdf_purge_local_resources()`.  */
	FZ_FUNCTION void pdf_purge_local_resources(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_purge_locals_from_store()`.  */
	FZ_FUNCTION void pdf_purge_locals_from_store(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_purge_object_from_store()`.  */
	FZ_FUNCTION void pdf_purge_object_from_store(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_read_journal()`.  */
	FZ_FUNCTION void pdf_read_journal(const PdfDocument& doc, const FzStream& stm);

	/* Class-aware wrapper for `pdf_read_ocg()`
	is not available because returned wrapper class for `pdf_ocg_descriptor`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_rearrange_pages()`.  */
	FZ_FUNCTION void pdf_rearrange_pages(const PdfDocument& doc, int count, const int *pages, ::pdf_clean_options_structure structure);

	/** Class-aware wrapper for `::pdf_rearrange_pages2()`.  */
	/** Swig-friendly wrapper for pdf_rearrange_pages(). */
	FZ_FUNCTION void pdf_rearrange_pages2(const PdfDocument& doc, const std::vector<int> &pages, ::pdf_clean_options_structure structure);

	/** Class-aware wrapper for `::pdf_recolor_page()`.  */
	FZ_FUNCTION void pdf_recolor_page(const PdfDocument& doc, int pagenum, PdfRecolorOptions& opts);

	/** Class-aware wrapper for `::pdf_recolor_shade()`.  */
	/**
		Recolor a shade.
	*/
	FZ_FUNCTION PdfObj pdf_recolor_shade(const PdfObj& shade, ::pdf_shade_recolorer *reshade, void *opaque);

	/** Class-aware wrapper for `::pdf_redact_page()`.  */
	FZ_FUNCTION int pdf_redact_page(const PdfDocument& doc, const PdfPage& page, PdfRedactOptions& opts);

	/** Class-aware wrapper for `::pdf_redo()`.  */
	FZ_FUNCTION void pdf_redo(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_remove_item()`.  */
	FZ_FUNCTION void pdf_remove_item(::fz_store_drop_fn *drop, const PdfObj& key);

	/** Class-aware wrapper for `::pdf_remove_output_intents()`.  */
	FZ_FUNCTION void pdf_remove_output_intents(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_repair_obj()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_repair_obj(::pdf_document *doc, ::pdf_lexbuf *buf, ::pdf_obj **encrypt, ::pdf_obj **id, ::pdf_obj **page, ::pdf_obj **root)` => `(int, int64_t stmofsp, int64_t stmlenp, int64_t tmpofs)`
	 */
	FZ_FUNCTION int pdf_repair_obj(const PdfDocument& doc, const PdfLexbuf& buf, int64_t *stmofsp, int64_t *stmlenp, PdfObj& encrypt, PdfObj& id, PdfObj& page, int64_t *tmpofs, PdfObj& root);

	/** Class-aware wrapper for `::pdf_repair_xref()`.  */
	FZ_FUNCTION void pdf_repair_xref(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_replace_xref()`.  */
	FZ_FUNCTION void pdf_replace_xref(const PdfDocument& doc, const PdfXrefEntry& entries, int n);

	/** Class-aware wrapper for `::pdf_reset_form()`.  */
	FZ_FUNCTION void pdf_reset_form(const PdfDocument& doc, const PdfObj& fields, int exclude);

	/** Class-aware wrapper for `::pdf_reset_processor()`.  */
	FZ_FUNCTION void pdf_reset_processor(const PdfProcessor& proc);

	/** Class-aware wrapper for `::pdf_resolve_indirect()`.  */
	/**
		Resolve an indirect object (or chain of objects).
	
		This can cause xref reorganisations (solidifications etc) due to
		repairs, so all held pdf_xref_entries should be considered
		invalid after this call (other than the returned one).
	*/
	FZ_FUNCTION PdfObj pdf_resolve_indirect(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_resolve_indirect_chain()`.  */
	FZ_FUNCTION PdfObj pdf_resolve_indirect_chain(const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_resolve_link()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_resolve_link(::pdf_document *doc, const char *uri)` => `(int, float xp, float yp)`
	 */
	FZ_FUNCTION int pdf_resolve_link(const PdfDocument& doc, const char *uri, float *xp, float *yp);

	/* Class-aware wrapper for `pdf_resolve_link_dest()`
	is not available because returned wrapper class for `fz_link_dest`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_rewrite_images()`.  */
	FZ_FUNCTION void pdf_rewrite_images(const PdfDocument& doc, PdfImageRewriterOptions& opts);

	/** Class-aware wrapper for `::pdf_run_annot()`.  */
	FZ_FUNCTION void pdf_run_annot(const PdfAnnot& annot, const FzDevice& dev, const FzMatrix& ctm, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_document_structure()`.  */
	FZ_FUNCTION void pdf_run_document_structure(const PdfDocument& doc, const FzDevice& dev, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_glyph()`.  */
	FZ_FUNCTION void pdf_run_glyph(const PdfDocument& doc, const PdfObj& resources, const FzBuffer& contents, const FzDevice& dev, const FzMatrix& ctm, void *gstate, const FzDefaultColorspaces& default_cs, void *fill_gstate, void *stroke_gstate);

	/** Class-aware wrapper for `::pdf_run_page()`.  */
	FZ_FUNCTION void pdf_run_page(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_annots()`.  */
	FZ_FUNCTION void pdf_run_page_annots(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_annots_with_usage()`.  */
	FZ_FUNCTION void pdf_run_page_annots_with_usage(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, const char *usage, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_contents()`.  */
	FZ_FUNCTION void pdf_run_page_contents(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_contents_with_usage()`.  */
	FZ_FUNCTION void pdf_run_page_contents_with_usage(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, const char *usage, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_widgets()`.  */
	FZ_FUNCTION void pdf_run_page_widgets(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_widgets_with_usage()`.  */
	FZ_FUNCTION void pdf_run_page_widgets_with_usage(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, const char *usage, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_run_page_with_usage()`.  */
	FZ_FUNCTION void pdf_run_page_with_usage(const PdfPage& page, const FzDevice& dev, const FzMatrix& ctm, const char *usage, FzCookie& cookie);

	/** Class-aware wrapper for `::pdf_sample_shade_function()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_sample_shade_function(int n, int funcs, ::pdf_function **func, float t0, float t1)` => `(float samples)`
	 */
	FZ_FUNCTION void pdf_sample_shade_function(float *samples, int n, int funcs, PdfFunction& func, float t0, float t1);

	/** Class-aware wrapper for `::pdf_save_document()`.  */
	FZ_FUNCTION void pdf_save_document(const PdfDocument& doc, const char *filename, PdfWriteOptions& opts);

	/** Class-aware wrapper for `::pdf_save_journal()`.  */
	FZ_FUNCTION void pdf_save_journal(const PdfDocument& doc, const char *filename);

	/** Class-aware wrapper for `::pdf_save_snapshot()`.  */
	FZ_FUNCTION void pdf_save_snapshot(const PdfDocument& doc, const char *filename);

	/** Class-aware wrapper for `::pdf_select_layer_config()`.  */
	FZ_FUNCTION void pdf_select_layer_config(const PdfDocument& doc, int config_num);

	/** Class-aware wrapper for `::pdf_select_layer_config_ui()`.  */
	FZ_FUNCTION void pdf_select_layer_config_ui(const PdfDocument& doc, int ui);

	/** Class-aware wrapper for `::pdf_serialise_journal()`.  */
	FZ_FUNCTION void pdf_serialise_journal(const PdfDocument& doc, const FzOutput& out);

	/** Class-aware wrapper for `::pdf_set_annot_active()`.  */
	FZ_FUNCTION void pdf_set_annot_active(const PdfAnnot& annot, int active);

	/** Class-aware wrapper for `::pdf_set_annot_appearance()`.  */
	FZ_FUNCTION void pdf_set_annot_appearance(const PdfAnnot& annot, const char *appearance, const char *state, const FzMatrix& ctm, const FzRect& bbox, const PdfObj& res, const FzBuffer& contents);

	/** Class-aware wrapper for `::pdf_set_annot_appearance_from_display_list()`.  */
	FZ_FUNCTION void pdf_set_annot_appearance_from_display_list(const PdfAnnot& annot, const char *appearance, const char *state, const FzMatrix& ctm, const FzDisplayList& list);

	/** Class-aware wrapper for `::pdf_set_annot_author()`.  */
	FZ_FUNCTION void pdf_set_annot_author(const PdfAnnot& annot, const char *author);

	/** Class-aware wrapper for `::pdf_set_annot_border()`.  */
	FZ_FUNCTION void pdf_set_annot_border(const PdfAnnot& annot, float width);

	/** Class-aware wrapper for `::pdf_set_annot_border_effect()`.  */
	FZ_FUNCTION void pdf_set_annot_border_effect(const PdfAnnot& annot, enum pdf_border_effect effect);

	/** Class-aware wrapper for `::pdf_set_annot_border_effect_intensity()`.  */
	FZ_FUNCTION void pdf_set_annot_border_effect_intensity(const PdfAnnot& annot, float intensity);

	/** Class-aware wrapper for `::pdf_set_annot_border_style()`.  */
	FZ_FUNCTION void pdf_set_annot_border_style(const PdfAnnot& annot, enum pdf_border_style style);

	/** Class-aware wrapper for `::pdf_set_annot_border_width()`.  */
	FZ_FUNCTION void pdf_set_annot_border_width(const PdfAnnot& annot, float width);

	/** Class-aware wrapper for `::pdf_set_annot_callout_line()`.  */
	FZ_FUNCTION void pdf_set_annot_callout_line(const PdfAnnot& annot, ::fz_point callout[3], int n);

	/** Class-aware wrapper for `::pdf_set_annot_callout_line2()`.  */
	/** SWIG-friendly wrapper for pdf_set_annot_callout_line(). */
	FZ_FUNCTION void pdf_set_annot_callout_line2(const PdfAnnot& annot, std::vector<fz_point> &callout);

	/** Class-aware wrapper for `::pdf_set_annot_callout_point()`.  */
	FZ_FUNCTION void pdf_set_annot_callout_point(const PdfAnnot& annot, const FzPoint& p);

	/** Class-aware wrapper for `::pdf_set_annot_callout_style()`.  */
	FZ_FUNCTION void pdf_set_annot_callout_style(const PdfAnnot& annot, enum pdf_line_ending style);

	/** Class-aware wrapper for `::pdf_set_annot_color()`.  */
	FZ_FUNCTION void pdf_set_annot_color(const PdfAnnot& annot, int n, const float *color);

	/** Class-aware wrapper for `::pdf_set_annot_contents()`.  */
	FZ_FUNCTION void pdf_set_annot_contents(const PdfAnnot& annot, const char *text);

	/** Class-aware wrapper for `::pdf_set_annot_creation_date()`.  */
	FZ_FUNCTION void pdf_set_annot_creation_date(const PdfAnnot& annot, int64_t time);

	/** Class-aware wrapper for `::pdf_set_annot_default_appearance()`.  */
	FZ_FUNCTION void pdf_set_annot_default_appearance(const PdfAnnot& annot, const char *font, float size, int n, const float *color);

	/** Class-aware wrapper for `::pdf_set_annot_field_value()`.  */
	FZ_FUNCTION int pdf_set_annot_field_value(const PdfDocument& doc, const PdfAnnot& widget, const char *text, int ignore_trigger_events);

	/** Class-aware wrapper for `::pdf_set_annot_filespec()`.  */
	FZ_FUNCTION void pdf_set_annot_filespec(const PdfAnnot& annot, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_set_annot_flags()`.  */
	FZ_FUNCTION void pdf_set_annot_flags(const PdfAnnot& annot, int flags);

	/** Class-aware wrapper for `::pdf_set_annot_hidden_for_editing()`.  */
	FZ_FUNCTION void pdf_set_annot_hidden_for_editing(const PdfAnnot& annot, int hidden);

	/** Class-aware wrapper for `::pdf_set_annot_hot()`.  */
	FZ_FUNCTION void pdf_set_annot_hot(const PdfAnnot& annot, int hot);

	/** Class-aware wrapper for `::pdf_set_annot_icon_name()`.  */
	FZ_FUNCTION void pdf_set_annot_icon_name(const PdfAnnot& annot, const char *name);

	/** Class-aware wrapper for `::pdf_set_annot_ink_list()`.  */
	FZ_FUNCTION void pdf_set_annot_ink_list(const PdfAnnot& annot, int n, const int *count, FzPoint& v);

	/** Class-aware wrapper for `::pdf_set_annot_intent()`.  */
	FZ_FUNCTION void pdf_set_annot_intent(const PdfAnnot& annot, enum pdf_intent it);

	/** Class-aware wrapper for `::pdf_set_annot_interior_color()`.  */
	FZ_FUNCTION void pdf_set_annot_interior_color(const PdfAnnot& annot, int n, const float *color);

	/** Class-aware wrapper for `::pdf_set_annot_is_open()`.  */
	FZ_FUNCTION void pdf_set_annot_is_open(const PdfAnnot& annot, int is_open);

	/** Class-aware wrapper for `::pdf_set_annot_language()`.  */
	FZ_FUNCTION void pdf_set_annot_language(const PdfAnnot& annot, ::fz_text_language lang);

	/** Class-aware wrapper for `::pdf_set_annot_line()`.  */
	FZ_FUNCTION void pdf_set_annot_line(const PdfAnnot& annot, const FzPoint& a, const FzPoint& b);

	/** Class-aware wrapper for `::pdf_set_annot_line_caption()`.  */
	FZ_FUNCTION void pdf_set_annot_line_caption(const PdfAnnot& annot, int cap);

	/** Class-aware wrapper for `::pdf_set_annot_line_caption_offset()`.  */
	FZ_FUNCTION void pdf_set_annot_line_caption_offset(const PdfAnnot& annot, const FzPoint& offset);

	/** Class-aware wrapper for `::pdf_set_annot_line_end_style()`.  */
	FZ_FUNCTION void pdf_set_annot_line_end_style(const PdfAnnot& annot, enum pdf_line_ending e);

	/** Class-aware wrapper for `::pdf_set_annot_line_ending_styles()`.  */
	FZ_FUNCTION void pdf_set_annot_line_ending_styles(const PdfAnnot& annot, enum pdf_line_ending start_style, enum pdf_line_ending end_style);

	/** Class-aware wrapper for `::pdf_set_annot_line_leader()`.  */
	FZ_FUNCTION void pdf_set_annot_line_leader(const PdfAnnot& annot, float ll);

	/** Class-aware wrapper for `::pdf_set_annot_line_leader_extension()`.  */
	FZ_FUNCTION void pdf_set_annot_line_leader_extension(const PdfAnnot& annot, float lle);

	/** Class-aware wrapper for `::pdf_set_annot_line_leader_offset()`.  */
	FZ_FUNCTION void pdf_set_annot_line_leader_offset(const PdfAnnot& annot, float llo);

	/** Class-aware wrapper for `::pdf_set_annot_line_start_style()`.  */
	FZ_FUNCTION void pdf_set_annot_line_start_style(const PdfAnnot& annot, enum pdf_line_ending s);

	/** Class-aware wrapper for `::pdf_set_annot_modification_date()`.  */
	FZ_FUNCTION void pdf_set_annot_modification_date(const PdfAnnot& annot, int64_t time);

	/** Class-aware wrapper for `::pdf_set_annot_opacity()`.  */
	FZ_FUNCTION void pdf_set_annot_opacity(const PdfAnnot& annot, float opacity);

	/** Class-aware wrapper for `::pdf_set_annot_popup()`.  */
	FZ_FUNCTION void pdf_set_annot_popup(const PdfAnnot& annot, const FzRect& rect);

	/** Class-aware wrapper for `::pdf_set_annot_quad_points()`.  */
	FZ_FUNCTION void pdf_set_annot_quad_points(const PdfAnnot& annot, int n, FzQuad& qv);

	/** Class-aware wrapper for `::pdf_set_annot_quadding()`.  */
	FZ_FUNCTION void pdf_set_annot_quadding(const PdfAnnot& annot, int q);

	/** Class-aware wrapper for `::pdf_set_annot_rect()`.  */
	FZ_FUNCTION void pdf_set_annot_rect(const PdfAnnot& annot, const FzRect& rect);

	/** Class-aware wrapper for `::pdf_set_annot_resynthesised()`.  */
	FZ_FUNCTION void pdf_set_annot_resynthesised(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_set_annot_rich_contents()`.  */
	FZ_FUNCTION void pdf_set_annot_rich_contents(const PdfAnnot& annot, const char *plain, const char *rich);

	/** Class-aware wrapper for `::pdf_set_annot_rich_defaults()`.  */
	FZ_FUNCTION void pdf_set_annot_rich_defaults(const PdfAnnot& annot, const char *style);

	/** Class-aware wrapper for `::pdf_set_annot_stamp_image()`.  */
	FZ_FUNCTION void pdf_set_annot_stamp_image(const PdfAnnot& annot, const FzImage& image);

	/** Class-aware wrapper for `::pdf_set_annot_stamp_image_obj()`.  */
	FZ_FUNCTION void pdf_set_annot_stamp_image_obj(const PdfAnnot& annot, const PdfObj& ref);

	/** Class-aware wrapper for `::pdf_set_annot_vertex()`.  */
	FZ_FUNCTION void pdf_set_annot_vertex(const PdfAnnot& annot, int i, const FzPoint& p);

	/** Class-aware wrapper for `::pdf_set_annot_vertices()`.  */
	FZ_FUNCTION void pdf_set_annot_vertices(const PdfAnnot& annot, int n, FzPoint& v);

	/** Class-aware wrapper for `::pdf_set_choice_field_value()`.  */
	FZ_FUNCTION int pdf_set_choice_field_value(const PdfAnnot& widget, const char *value);

	/** Class-aware wrapper for `::pdf_set_cmap_wmode()`.  */
	FZ_FUNCTION void pdf_set_cmap_wmode(const PdfCmap& cmap, int wmode);

	/** Class-aware wrapper for `::pdf_set_default_hmtx()`.  */
	FZ_FUNCTION void pdf_set_default_hmtx(const PdfFontDesc& font, int w);

	/** Class-aware wrapper for `::pdf_set_default_vmtx()`.  */
	FZ_FUNCTION void pdf_set_default_vmtx(const PdfFontDesc& font, int y, int w);

	/** Class-aware wrapper for `::pdf_set_doc_event_callback()`.  */
	FZ_FUNCTION void pdf_set_doc_event_callback(const PdfDocument& doc, ::pdf_doc_event_cb *event_cb, ::pdf_free_doc_event_data_cb *free_event_data_cb, void *data);

	/** Class-aware wrapper for `::pdf_set_document_language()`.  */
	FZ_FUNCTION void pdf_set_document_language(const PdfDocument& doc, ::fz_text_language lang);

	/** Class-aware wrapper for `::pdf_set_field_value()`.  */
	FZ_FUNCTION int pdf_set_field_value(const PdfDocument& doc, const PdfObj& field, const char *text, int ignore_trigger_events);

	/** Class-aware wrapper for `::pdf_set_font_wmode()`.  */
	FZ_FUNCTION void pdf_set_font_wmode(const PdfFontDesc& font, int wmode);

	/** Class-aware wrapper for `::pdf_set_int()`.  */
	FZ_FUNCTION void pdf_set_int(const PdfObj& obj, int64_t i);

	/** Class-aware wrapper for `::pdf_set_layer_config_as_default()`.  */
	FZ_FUNCTION void pdf_set_layer_config_as_default(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_set_obj_memo()`.  */
	FZ_FUNCTION void pdf_set_obj_memo(const PdfObj& obj, int bit, int memo);

	/** Class-aware wrapper for `::pdf_set_obj_parent()`.  */
	FZ_FUNCTION void pdf_set_obj_parent(const PdfObj& obj, int num);

	/** Class-aware wrapper for `::pdf_set_page_box()`.  */
	FZ_FUNCTION void pdf_set_page_box(const PdfPage& page, ::fz_box_type box, const FzRect& rect);

	/** Class-aware wrapper for `::pdf_set_page_labels()`.  */
	FZ_FUNCTION void pdf_set_page_labels(const PdfDocument& doc, int index, ::pdf_page_label_style style, const char *prefix, int start);

	/** Class-aware wrapper for `::pdf_set_populating_xref_trailer()`.  */
	FZ_FUNCTION void pdf_set_populating_xref_trailer(const PdfDocument& doc, const PdfObj& trailer);

	/** Class-aware wrapper for `::pdf_set_str_len()`.  */
	FZ_FUNCTION void pdf_set_str_len(const PdfObj& obj, size_t newlen);

	/** Class-aware wrapper for `::pdf_set_text_field_value()`.  */
	FZ_FUNCTION int pdf_set_text_field_value(const PdfAnnot& widget, const char *value);

	/** Class-aware wrapper for `::pdf_set_usecmap()`.  */
	FZ_FUNCTION void pdf_set_usecmap(const PdfCmap& cmap, const PdfCmap& usecmap);

	/** Class-aware wrapper for `::pdf_set_widget_editing_state()`.  */
	FZ_FUNCTION void pdf_set_widget_editing_state(const PdfAnnot& widget, int editing);

	/** Class-aware wrapper for `::pdf_sign_signature()`.  */
	FZ_FUNCTION void pdf_sign_signature(const PdfAnnot& widget, const PdfPkcs7Signer& signer, int appearance_flags, const FzImage& graphic, const char *reason, const char *location);

	/** Class-aware wrapper for `::pdf_sign_signature_with_appearance()`.  */
	FZ_FUNCTION void pdf_sign_signature_with_appearance(const PdfAnnot& widget, const PdfPkcs7Signer& signer, int64_t date, const FzDisplayList& disp_list);

	/** Class-aware wrapper for `::pdf_signature_appearance_signed()`.  */
	FZ_FUNCTION FzDisplayList pdf_signature_appearance_signed(const FzRect& rect, ::fz_text_language lang, const FzImage& img, const char *left_text, const char *right_text, int include_logo);

	/** Class-aware wrapper for `::pdf_signature_appearance_unsigned()`.  */
	FZ_FUNCTION FzDisplayList pdf_signature_appearance_unsigned(const FzRect& rect, ::fz_text_language lang);

	/** Class-aware wrapper for `::pdf_signature_byte_range()`.  */
	FZ_FUNCTION int pdf_signature_byte_range(const PdfDocument& doc, const PdfObj& signature, const FzRange& byte_range);

	/** Class-aware wrapper for `::pdf_signature_contents()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_signature_contents(::pdf_document *doc, ::pdf_obj *signature)` => `(size_t, char *contents)`
	 */
	FZ_FUNCTION size_t pdf_signature_contents(const PdfDocument& doc, const PdfObj& signature, char **contents);

	/** Class-aware wrapper for `::pdf_signature_drop_distinguished_name()`.  */
	FZ_FUNCTION void pdf_signature_drop_distinguished_name(const PdfPkcs7DistinguishedName& name);

	/** Class-aware wrapper for `::pdf_signature_error_description()`.  */
	FZ_FUNCTION char *pdf_signature_error_description(::pdf_signature_error err);

	/** Class-aware wrapper for `::pdf_signature_format_distinguished_name()`.  */
	FZ_FUNCTION char *pdf_signature_format_distinguished_name(const PdfPkcs7DistinguishedName& name);

	/* Class-aware wrapper for `pdf_signature_get_signatory()`
	is not available because returned wrapper class for `pdf_pkcs7_distinguished_name`
	is non-copyable. */

	/* Class-aware wrapper for `pdf_signature_get_widget_signatory()`
	is not available because returned wrapper class for `pdf_pkcs7_distinguished_name`
	is non-copyable. */

	/** Class-aware wrapper for `::pdf_signature_hash_bytes()`.  */
	FZ_FUNCTION FzStream pdf_signature_hash_bytes(const PdfDocument& doc, const PdfObj& signature);

	/** Class-aware wrapper for `::pdf_signature_incremental_change_since_signing()`.  */
	FZ_FUNCTION int pdf_signature_incremental_change_since_signing(const PdfDocument& doc, const PdfObj& signature);

	/** Class-aware wrapper for `::pdf_signature_info()`.  */
	FZ_FUNCTION char *pdf_signature_info(const char *name, const PdfPkcs7DistinguishedName& dn, const char *reason, const char *location, int64_t date, int include_labels);

	/** Class-aware wrapper for `::pdf_signature_is_signed()`.  */
	FZ_FUNCTION int pdf_signature_is_signed(const PdfDocument& doc, const PdfObj& field);

	/** Class-aware wrapper for `::pdf_signature_set_value()`.  */
	FZ_FUNCTION void pdf_signature_set_value(const PdfDocument& doc, const PdfObj& field, const PdfPkcs7Signer& signer, int64_t stime);

	/** Class-aware wrapper for `::pdf_sort_cmap()`.  */
	FZ_FUNCTION void pdf_sort_cmap(const PdfCmap& cmap);

	/** Class-aware wrapper for `::pdf_sort_dict()`.  */
	FZ_FUNCTION void pdf_sort_dict(const PdfObj& dict);

	/** Class-aware wrapper for `::pdf_specifics()`.  */
	FZ_FUNCTION PdfDocument pdf_specifics(const FzDocument& doc);

	/** Class-aware wrapper for `::pdf_sprint_obj()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_sprint_obj(char *buf, size_t cap, ::pdf_obj *obj, int tight, int ascii)` => `(char *, size_t len)`
	 */
	FZ_FUNCTION char *pdf_sprint_obj(char *buf, size_t cap, size_t *len, const PdfObj& obj, int tight, int ascii);

	/** Class-aware wrapper for `::pdf_store_item()`.  */
	FZ_FUNCTION void pdf_store_item(const PdfObj& key, void *val, size_t itemsize);

	/** Class-aware wrapper for `::pdf_string_from_annot_type()`.  */
	FZ_FUNCTION const char *pdf_string_from_annot_type(enum pdf_annot_type type);

	/** Class-aware wrapper for `::pdf_string_from_intent()`.  */
	FZ_FUNCTION const char *pdf_string_from_intent(enum pdf_intent intent);

	/** Class-aware wrapper for `::pdf_string_from_line_ending()`.  */
	FZ_FUNCTION const char *pdf_string_from_line_ending(enum pdf_line_ending end);

	/** Class-aware wrapper for `::pdf_structure_type()`.  */
	FZ_FUNCTION ::fz_structure pdf_structure_type(const PdfObj& role_map, const PdfObj& tag);

	/** Class-aware wrapper for `::pdf_subset_fonts()`.  */
	FZ_FUNCTION void pdf_subset_fonts(const PdfDocument& doc, int pages_len, const int *pages);

	/** Class-aware wrapper for `::pdf_subset_fonts2()`.  */
	/** Swig-friendly wrapper for pdf_subset_fonts(). */
	FZ_FUNCTION void pdf_subset_fonts2(const PdfDocument& doc, const std::vector<int> &pages);

	/** Class-aware wrapper for `::pdf_sync_annots()`.  */
	FZ_FUNCTION void pdf_sync_annots(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_sync_links()`.  */
	FZ_FUNCTION void pdf_sync_links(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_sync_open_pages()`.  */
	FZ_FUNCTION void pdf_sync_open_pages(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_sync_page()`.  */
	FZ_FUNCTION void pdf_sync_page(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_text_widget_format()`.  */
	FZ_FUNCTION int pdf_text_widget_format(const PdfAnnot& tw);

	/** Class-aware wrapper for `::pdf_text_widget_max_len()`.  */
	FZ_FUNCTION int pdf_text_widget_max_len(const PdfAnnot& tw);

	/** Class-aware wrapper for `::pdf_to_bool()`.  */
	FZ_FUNCTION int pdf_to_bool(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_bool_default()`.  */
	FZ_FUNCTION int pdf_to_bool_default(const PdfObj& obj, int def);

	/** Class-aware wrapper for `::pdf_to_date()`.  */
	FZ_FUNCTION int64_t pdf_to_date(const PdfObj& time);

	/** Class-aware wrapper for `::pdf_to_gen()`.  */
	FZ_FUNCTION int pdf_to_gen(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_int()`.  */
	FZ_FUNCTION int pdf_to_int(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_int64()`.  */
	FZ_FUNCTION int64_t pdf_to_int64(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_int_default()`.  */
	FZ_FUNCTION int pdf_to_int_default(const PdfObj& obj, int def);

	/** Class-aware wrapper for `::pdf_to_matrix()`.  */
	FZ_FUNCTION FzMatrix pdf_to_matrix(const PdfObj& array);

	/** Class-aware wrapper for `::pdf_to_name()`.  */
	FZ_FUNCTION const char *pdf_to_name(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_num()`.  */
	FZ_FUNCTION int pdf_to_num(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_point()`.  */
	FZ_FUNCTION FzPoint pdf_to_point(const PdfObj& array, int offset);

	/** Class-aware wrapper for `::pdf_to_quad()`.  */
	FZ_FUNCTION FzQuad pdf_to_quad(const PdfObj& array, int offset);

	/** Class-aware wrapper for `::pdf_to_real()`.  */
	FZ_FUNCTION float pdf_to_real(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_real_default()`.  */
	FZ_FUNCTION float pdf_to_real_default(const PdfObj& obj, float def);

	/** Class-aware wrapper for `::pdf_to_rect()`.  */
	FZ_FUNCTION FzRect pdf_to_rect(const PdfObj& array);

	/** Class-aware wrapper for `::pdf_to_str_buf()`.  */
	FZ_FUNCTION char *pdf_to_str_buf(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_str_len()`.  */
	FZ_FUNCTION size_t pdf_to_str_len(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_to_string()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_to_string(::pdf_obj *obj)` => `(const char *, size_t sizep)`
	 */
	FZ_FUNCTION const char *pdf_to_string(const PdfObj& obj, size_t *sizep);

	/** Class-aware wrapper for `::pdf_to_text_string()`.  */
	FZ_FUNCTION const char *pdf_to_text_string(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_toggle_layer_config_ui()`.  */
	FZ_FUNCTION void pdf_toggle_layer_config_ui(const PdfDocument& doc, int ui);

	/** Class-aware wrapper for `::pdf_toggle_widget()`.  */
	FZ_FUNCTION int pdf_toggle_widget(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_tos_get_text()`.  */
	FZ_FUNCTION FzText pdf_tos_get_text(const PdfTextObjectState& tos);

	/** Class-aware wrapper for `::pdf_tos_make_trm()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_tos_make_trm(::pdf_text_object_state *tos, ::pdf_text_state *text, ::pdf_font_desc *fontdesc, int cid, ::fz_matrix *trm)` => `(int, float adv)`
	 */
	FZ_FUNCTION int pdf_tos_make_trm(const PdfTextObjectState& tos, const PdfTextState& text, const PdfFontDesc& fontdesc, int cid, FzMatrix& trm, float *adv);

	/** Class-aware wrapper for `::pdf_tos_move_after_char()`.  */
	FZ_FUNCTION void pdf_tos_move_after_char(const PdfTextObjectState& tos);

	/** Class-aware wrapper for `::pdf_tos_newline()`.  */
	FZ_FUNCTION void pdf_tos_newline(const PdfTextObjectState& tos, float leading);

	/** Class-aware wrapper for `::pdf_tos_reset()`.  */
	FZ_FUNCTION void pdf_tos_reset(const PdfTextObjectState& tos, int render);

	/** Class-aware wrapper for `::pdf_tos_restore()`.  */
	FZ_FUNCTION void pdf_tos_restore(const PdfTextObjectState& tos, ::fz_matrix save[2]);

	/** Class-aware wrapper for `::pdf_tos_save()`.  */
	FZ_FUNCTION void pdf_tos_save(const PdfTextObjectState& tos, ::fz_matrix save[2]);

	/** Class-aware wrapper for `::pdf_tos_set_matrix()`.  */
	FZ_FUNCTION void pdf_tos_set_matrix(const PdfTextObjectState& tos, float a, float b, float c, float d, float e, float f);

	/** Class-aware wrapper for `::pdf_tos_translate()`.  */
	FZ_FUNCTION void pdf_tos_translate(const PdfTextObjectState& tos, float tx, float ty);

	/** Class-aware wrapper for `::pdf_trailer()`.  */
	FZ_FUNCTION PdfObj pdf_trailer(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_undo()`.  */
	FZ_FUNCTION void pdf_undo(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_undoredo_state()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_undoredo_state(::pdf_document *doc)` => `(int, int steps)`
	 */
	FZ_FUNCTION int pdf_undoredo_state(const PdfDocument& doc, int *steps);

	/** Class-aware wrapper for `::pdf_undoredo_step()`.  */
	FZ_FUNCTION const char *pdf_undoredo_step(const PdfDocument& doc, int step);

	/** Class-aware wrapper for `::pdf_unmark_obj()`.  */
	FZ_FUNCTION void pdf_unmark_obj(const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_update_annot()`.  */
	FZ_FUNCTION int pdf_update_annot(const PdfAnnot& annot);

	/** Class-aware wrapper for `::pdf_update_default_colorspaces()`.  */
	FZ_FUNCTION FzDefaultColorspaces pdf_update_default_colorspaces(const FzDefaultColorspaces& old_cs, const PdfObj& res);

	/** Class-aware wrapper for `::pdf_update_object()`.  */
	FZ_FUNCTION void pdf_update_object(const PdfDocument& doc, int num, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_update_page()`.  */
	FZ_FUNCTION int pdf_update_page(const PdfPage& page);

	/** Class-aware wrapper for `::pdf_update_stream()`.  */
	FZ_FUNCTION void pdf_update_stream(const PdfDocument& doc, const PdfObj& ref, const FzBuffer& buf, int compressed);

	/** Class-aware wrapper for `::pdf_update_widget()`.  */
	FZ_FUNCTION int pdf_update_widget(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_update_xobject()`.  */
	FZ_FUNCTION void pdf_update_xobject(const PdfDocument& doc, const PdfObj& xobj, const FzRect& bbox, const FzMatrix& mat, const PdfObj& res, const FzBuffer& buffer);

	/** Class-aware wrapper for `::pdf_validate_change_history()`.  */
	FZ_FUNCTION int pdf_validate_change_history(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_validate_changes()`.  */
	FZ_FUNCTION int pdf_validate_changes(const PdfDocument& doc, int version);

	/** Class-aware wrapper for `::pdf_validate_signature()`.  */
	FZ_FUNCTION int pdf_validate_signature(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_verify_embedded_file_checksum()`.  */
	FZ_FUNCTION int pdf_verify_embedded_file_checksum(const PdfObj& fs);

	/** Class-aware wrapper for `::pdf_version()`.  */
	FZ_FUNCTION int pdf_version(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_walk_tree()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_walk_tree(::pdf_obj *tree, ::pdf_obj *kid_name, void (*arrive)(::fz_context *, ::pdf_obj *, void *, ::pdf_obj **), void (*leave)(::fz_context *, ::pdf_obj *, void *), void *arg, ::pdf_obj **names, ::pdf_obj **values)` => `()`
	 */
	FZ_FUNCTION void pdf_walk_tree(const PdfObj& tree, const PdfObj& kid_name, void (*arrive)(::fz_context *, ::pdf_obj *, void *, ::pdf_obj **), void (*leave)(::fz_context *, ::pdf_obj *, void *), void *arg, PdfObj& names, PdfObj& values);

	/** Class-aware wrapper for `::pdf_was_pure_xfa()`.  */
	FZ_FUNCTION int pdf_was_pure_xfa(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_was_repaired()`.  */
	FZ_FUNCTION int pdf_was_repaired(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_widget_is_readonly()`.  */
	FZ_FUNCTION int pdf_widget_is_readonly(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_widget_is_signed()`.  */
	FZ_FUNCTION int pdf_widget_is_signed(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_widget_type()`.  */
	FZ_FUNCTION enum pdf_widget_type pdf_widget_type(const PdfAnnot& widget);

	/** Class-aware wrapper for `::pdf_write_digest()`.  */
	FZ_FUNCTION void pdf_write_digest(const FzOutput& out, const PdfObj& byte_range, const PdfObj& field, size_t digest_offset, size_t digest_length, const PdfPkcs7Signer& signer);

	/** Class-aware wrapper for `::pdf_write_document()`.  */
	FZ_FUNCTION void pdf_write_document(const PdfDocument& doc, const FzOutput& out, PdfWriteOptions& opts);

	/** Class-aware wrapper for `::pdf_write_journal()`.  */
	FZ_FUNCTION void pdf_write_journal(const PdfDocument& doc, const FzOutput& out);

	/** Class-aware wrapper for `::pdf_write_snapshot()`.  */
	FZ_FUNCTION void pdf_write_snapshot(const PdfDocument& doc, const FzOutput& out);

	/** Class-aware wrapper for `::pdf_xobject_bbox()`.  */
	FZ_FUNCTION FzRect pdf_xobject_bbox(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xobject_colorspace()`.  */
	FZ_FUNCTION FzColorspace pdf_xobject_colorspace(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xobject_isolated()`.  */
	FZ_FUNCTION int pdf_xobject_isolated(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xobject_knockout()`.  */
	FZ_FUNCTION int pdf_xobject_knockout(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xobject_matrix()`.  */
	FZ_FUNCTION FzMatrix pdf_xobject_matrix(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xobject_resources()`.  */
	FZ_FUNCTION PdfObj pdf_xobject_resources(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xobject_transparency()`.  */
	FZ_FUNCTION int pdf_xobject_transparency(const PdfObj& xobj);

	/** Class-aware wrapper for `::pdf_xref_ensure_incremental_object()`.  */
	FZ_FUNCTION int pdf_xref_ensure_incremental_object(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_xref_ensure_local_object()`.  */
	FZ_FUNCTION void pdf_xref_ensure_local_object(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_xref_entry_map()`.  */
	FZ_FUNCTION void pdf_xref_entry_map(const PdfDocument& doc, void (*fn)(::fz_context *, ::pdf_xref_entry *, int , ::pdf_document *, void *), void *arg);

	/** Class-aware wrapper for `::pdf_xref_is_incremental()`.  */
	FZ_FUNCTION int pdf_xref_is_incremental(const PdfDocument& doc, int num);

	/** Class-aware wrapper for `::pdf_xref_len()`.  */
	FZ_FUNCTION int pdf_xref_len(const PdfDocument& doc);

	/** Class-aware wrapper for `::pdf_xref_obj_is_unsaved_signature()`.  */
	FZ_FUNCTION int pdf_xref_obj_is_unsaved_signature(const PdfDocument& doc, const PdfObj& obj);

	/** Class-aware wrapper for `::pdf_xref_remove_unsaved_signature()`.  */
	FZ_FUNCTION void pdf_xref_remove_unsaved_signature(const PdfDocument& doc, const PdfObj& field);

	/** Class-aware wrapper for `::pdf_xref_store_unsaved_signature()`.  */
	FZ_FUNCTION void pdf_xref_store_unsaved_signature(const PdfDocument& doc, const PdfObj& field, const PdfPkcs7Signer& signer);

	/** Class-aware wrapper for `::pdf_zugferd_profile()`.
	
	This function has out-params. Python/C# wrappers look like:
		`pdf_zugferd_profile(::pdf_document *doc)` => `(enum pdf_zugferd_profile, float version)`
	 */
	FZ_FUNCTION enum pdf_zugferd_profile pdf_zugferd_profile(const PdfDocument& doc, float *version);

	/** Class-aware wrapper for `::pdf_zugferd_profile_to_string()`.  */
	FZ_FUNCTION const char *pdf_zugferd_profile_to_string(enum pdf_zugferd_profile profile);

	/** Class-aware wrapper for `::pdf_zugferd_xml()`.  */
	FZ_FUNCTION FzBuffer pdf_zugferd_xml(const PdfDocument& doc);

	/* Class-aware wrapper for `pdf_dict_getl()`. `keys` must be null-terminated list of
	`pdf_obj*`'s, not `PdfObj*`'s, so that conventional
	use with `PDF_NAME()` works. */
	FZ_FUNCTION PdfObj pdf_dict_getlv( PdfObj& dict, va_list keys);

	/* Class-aware wrapper for `pdf_dict_getl()`. `...` must be null-terminated list of
	`pdf_obj*`'s, not `PdfObj*`'s, so that conventional
	use with `PDF_NAME()` works. [We use pointer `dict` arg because variadic
	args do not with with reference args.] */
	FZ_FUNCTION PdfObj pdf_dict_getl( PdfObj* dict, ...);

} /* End of namespace mupdf. */

#endif
