# 🚀 ENHANCED OCR TABLE EXTRACTION - ADVANCED EDITION

## Description
This is the most advanced OCR table extraction tool available, featuring cutting-edge AI and image processing
capabilities for superior table recognition and mathematical content handling.

## 🌟 ADVANCED FEATURES:

### 🖼️ Advanced Image Processing
- **OpenCV Integration**: Professional-grade image preprocessing
- **Noise Reduction**: Advanced denoising algorithms
- **Adaptive Enhancement**: CLAHE histogram equalization
- **Morphological Operations**: Text cleanup and sharpening
- **Multi-format Support**: JPG, PNG, BMP, TIFF, GIF

### 📄 PDF Document Support
- **PDF to Image Conversion**: High-DPI extraction (300 DPI)
- **Multi-page Processing**: Batch process entire PDF documents
- **Page-by-page Output**: Individual Word files per page
- **PyMuPDF Integration**: Fast and reliable PDF handling

### 🧮 Mathematical Content AI
- **LaTeX OCR**: State-of-the-art mathematical expression recognition
- **Deep Learning Models**: PyTorch-based AI enhancement
- **Pattern Recognition**: Advanced mathematical content detection
- **LaTeX Formatting**: Professional mathematical notation output
- **Greek Letters & Symbols**: Full Unicode mathematical support

### 🔧 Performance & Monitoring
- **System Monitoring**: Real-time CPU and memory tracking
- **Multi-configuration OCR**: Tests multiple settings for best results
- **Coordinate-based Detection**: Precise table structure recognition
- **Confidence Filtering**: Only high-quality OCR results used

### 📊 Professional Output
- **Word Table Creation**: Properly formatted tables with borders
- **Bold Headers**: Automatic header row formatting
- **LaTeX Integration**: Mathematical expressions in proper format
- **Batch Processing**: Handle multiple files simultaneously

## 📋 System Requirements
- **Python**: 3.7+ (3.9+ recommended for best performance)
- **Tesseract OCR**: Latest version from official repository
- **Memory**: 4GB+ RAM (8GB+ recommended for large documents)
- **Storage**: 2GB+ free space (for ML models and processing)

## 🛠️ Installation Options

### Option 1: Automated Installation (Recommended)
```bash
# Run the enhanced installation script
install_dependencies.bat
```

### Option 2: Manual Installation
```bash
# Core dependencies
pip install -r requirements.txt

# Or install categories individually:
pip install Pillow opencv-python numpy                    # Image processing
pip install PyMuPDF pdf2image                            # PDF support
pip install pytesseract pix2tex                          # OCR & LaTeX
pip install torch torchvision transformers               # Deep learning
pip install python-docx lxml psutil                      # Document & system
```

### Option 3: Minimal Installation (Basic Features Only)
```bash
pip install pytesseract Pillow python-docx
```

## 🎯 Usage Options

### Advanced Mode (Recommended)
```bash
python enhanced_main.py
```
**Features**: All advanced capabilities including OpenCV, PDF support, LaTeX OCR

### Standard Mode
```bash
python main.py
```
**Features**: Basic OCR with LaTeX enhancement

### Single Image Testing
```bash
python test_single_image.py
```
**Features**: Test single image with full capabilities

### Comprehensive Testing
```bash
python test_advanced_features.py
```
**Features**: Test all installed features and dependencies

## 📁 Supported Input Formats
- **Images**: JPG, JPEG, PNG, BMP, TIFF, GIF
- **Documents**: PDF (multi-page support)
- **Batch Processing**: Entire folders of mixed formats

## 🧮 Mathematical Content AI
The system uses advanced AI to enhance mathematical content:

### Detection Patterns
- **Equations**: `x + 2y = 5`, `3x - y = 0`
- **Fractions**: `1/2 + 3/4`, `a/b = c/d`
- **Greek Letters**: `α + β = γ`, `θ = 45°`
- **Complex Expressions**: `2x² + 3x - 1 = 0`
- **Operators**: `≤`, `≥`, `≠`, `∫`, `∑`

### LaTeX Output Examples
- Input: `x + 2y = 5` → Output: `$x + 2y = 5$`
- Input: `1/2` → Output: `$\frac{1}{2}$`
- Input: `α + β` → Output: `$\alpha + \beta$`

## 📊 Output Features
### Professional Word Tables
- ✅ **Structured Tables**: Proper rows and columns
- ✅ **Bold Headers**: Automatic first-row formatting
- ✅ **Grid Borders**: Professional table appearance
- ✅ **Mathematical Formatting**: LaTeX-enhanced expressions
- ✅ **Multi-column Support**: Complex table structures
- ✅ **Batch Output**: Multiple files processed automatically
