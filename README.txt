# Batch Image Table to Word Generator (Offline)

## Description
This offline tool converts multiple images of tables into Word files using OCR (Tesseract).

## Requirements

- Python 3.x
- Tesseract OCR (install from: https://github.com/tesseract-ocr/tesseract)
- Python libraries:
  - pytesseract
  - pillow
  - python-docx
  - tkinter

## Setup

Install the required Python libraries using:

    pip install pytesseract pillow python-docx

Ensure Tesseract is installed and available in system PATH.

## How to Use

1. Run the app:

    python main.py

2. Select a folder with image files.
3. Select a folder to save the Word documents.
4. The app will convert each image to a `.docx` file.

This version assumes simple tables or structured text images. For complex table extraction, OpenCV/grid detection can be added later.
