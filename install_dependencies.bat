@echo off
echo ============================================================================
echo ENHANCED OCR TABLE EXTRACTION - ADVANCED INSTALLATION
echo ============================================================================
echo This will install all advanced features including:
echo   - Advanced OpenCV image processing
echo   - PDF support (PyMuPDF, pdf2image)
echo   - LaTeX OCR for mathematical content
echo   - Deep learning enhancements
echo   - Performance monitoring
echo ============================================================================
echo.

echo [1/6] Installing core image processing...
pip install Pillow>=9.0.0 opencv-python>=4.5.0 numpy>=1.21.0

echo.
echo [2/6] Installing PDF support...
pip install PyMuPDF>=1.20.0 pdf2image>=1.16.0

echo.
echo [3/6] Installing OCR components...
pip install pytesseract>=0.3.10

echo.
echo [4/6] Installing LaTeX OCR (this may take several minutes)...
pip install pix2tex>=0.1.2

echo.
echo [5/6] Installing deep learning enhancements...
pip install torch>=1.9.0 torchvision>=0.10.0 transformers>=4.37.0
pip install accelerate>=0.20.0 protobuf>=3.20.0

echo.
echo [6/6] Installing document processing and utilities...
pip install python-docx>=0.8.11 lxml>=4.6.0
pip install psutil>=5.9.0 typing-extensions>=4.0.0

echo.
echo ============================================================================
echo INSTALLATION COMPLETE!
echo ============================================================================
echo Advanced features now available:
echo   ✅ OpenCV image preprocessing
echo   ✅ PDF document support
echo   ✅ LaTeX OCR for mathematical content
echo   ✅ Deep learning enhancements
echo   ✅ Performance monitoring
echo   ✅ Enhanced Word document creation
echo.
echo IMPORTANT: Make sure you have Tesseract OCR installed on your system.
echo Download from: https://github.com/UB-Mannheim/tesseract/wiki
echo.
echo Ready to use:
echo   - Run: python enhanced_main.py (for advanced features)
echo   - Run: python main.py (for standard version)
echo   - Run: python test_single_image.py (for single image testing)
echo ============================================================================
pause
