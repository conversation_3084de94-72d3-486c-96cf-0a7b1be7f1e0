"""
Test the enhanced OCR without LaTeX OCR to show improvements
"""

import os
import pytesseract
from docx import Document
from PIL import Image, ImageEnhance, ImageFilter
from docx.shared import Inches
from docx.enum.table import WD_TABLE_ALIGNMENT
import re

def preprocess_image_for_table(img):
    """Enhance image for better table OCR"""
    # Convert to grayscale if needed
    if img.mode != 'L':
        img = img.convert('L')
    
    # Enhance contrast
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.5)
    
    # Enhance sharpness
    enhancer = ImageEnhance.Sharpness(img)
    img = enhancer.enhance(2.0)
    
    # Apply slight blur to reduce noise
    img = img.filter(ImageFilter.MedianFilter(size=3))
    
    return img

def is_mathematical_content(text):
    """Detect if text contains mathematical expressions"""
    math_patterns = [
        r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
        r'\d+[xy]',  # Coefficients with variables
        r'[=<>≤≥]',  # Mathematical operators
        r'\d+/\d+',  # Fractions
        r'[αβγδεζηθικλμνξοπρστυφχψω]',  # Greek letters
        r'\^|\_{|}',  # Superscripts/subscripts
        r'\\[a-zA-Z]+',  # LaTeX commands
    ]
    
    return any(re.search(pattern, text) for pattern in math_patterns)

def extract_table_from_ocr_data(data, original_img=None):
    """Extract table structure from OCR data with enhanced coordinate detection"""
    try:
        # Get text, coordinates, and confidence
        texts = data['text']
        lefts = data['left']
        tops = data['top']
        widths = data['width']
        heights = data['height']
        confs = data['conf']
        
        # Filter out low confidence and empty text
        valid_data = []
        for i, text in enumerate(texts):
            if text.strip() and confs[i] > 30:  # Only keep confident text
                valid_data.append({
                    'text': text.strip(),
                    'left': lefts[i],
                    'top': tops[i],
                    'right': lefts[i] + widths[i],
                    'bottom': tops[i] + heights[i],
                    'width': widths[i],
                    'height': heights[i]
                })
        
        if not valid_data:
            return None
        
        # Sort by vertical position (top coordinate)
        valid_data.sort(key=lambda x: x['top'])
        
        # Group into rows based on vertical proximity
        rows = []
        current_row = []
        row_threshold = 20  # pixels
        
        for item in valid_data:
            if not current_row:
                current_row.append(item)
            else:
                # Check if this item is on the same row as the current row
                avg_top = sum(x['top'] for x in current_row) / len(current_row)
                if abs(item['top'] - avg_top) <= row_threshold:
                    current_row.append(item)
                else:
                    # Start new row
                    if current_row:
                        rows.append(current_row)
                    current_row = [item]
        
        if current_row:
            rows.append(current_row)
        
        # Sort each row by horizontal position and mark mathematical content
        table_data = []
        for row in rows:
            row.sort(key=lambda x: x['left'])
            row_texts = []
            
            for item in row:
                text = item['text']
                
                # Mark mathematical content for future LaTeX OCR enhancement
                if is_mathematical_content(text):
                    print(f"  - Detected mathematical content: '{text}'")
                    # For now, just keep the original text
                    # When LaTeX OCR is available, this will be enhanced
                
                row_texts.append(text)
            
            if len(row_texts) > 1:  # Only keep rows with multiple columns
                table_data.append(row_texts)
        
        return table_data if len(table_data) > 1 else None
    except Exception as e:
        print(f"Table extraction error: {e}")
        return None

def parse_text_to_table(text):
    """Try to parse plain text into table format with enhanced patterns"""
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    if not lines:
        return None

    # Look for common table patterns
    table_data = []

    for line in lines:
        # Try to split by multiple spaces, tabs, or common separators
        # First try splitting by multiple spaces (2 or more)
        cells = re.split(r'\s{2,}|\t', line)

        # If that doesn't work well, try other separators
        if len(cells) < 2:
            cells = re.split(r'\s+', line)

        # Clean up cells
        cells = [cell.strip() for cell in cells if cell.strip()]

        if len(cells) > 1:  # Only add rows with multiple columns
            table_data.append(cells)

    return table_data if len(table_data) > 1 else None

def create_word_table(doc, table_data):
    """Create a Word table from table data"""
    if not table_data:
        return
    
    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)
    
    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Fill table with data
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                cell = row.cells[j]
                cell.text = str(cell_data)
                
                # Make first row bold (headers)
                if i == 0:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True

def process_image_to_docx(image_path, output_path):
    try:
        print(f"Processing {image_path}...")
        img = Image.open(image_path)
        
        # Preprocess image for better OCR
        img_processed = preprocess_image_for_table(img)
        print("  - Image preprocessed (contrast, sharpness, noise reduction)")
        
        # Try multiple OCR configurations for better table detection
        configs = [
            '--psm 6 -c preserve_interword_spaces=1',  # Single uniform block
            '--psm 4 -c preserve_interword_spaces=1',  # Single column of text
            '--psm 3 -c preserve_interword_spaces=1',  # Fully automatic page segmentation
        ]
        
        best_table_data = None
        best_text = ""
        
        print("  - Testing multiple OCR configurations...")
        for i, config in enumerate(configs):
            try:
                text = pytesseract.image_to_string(img_processed, config=config)
                data = pytesseract.image_to_data(img_processed, output_type=pytesseract.Output.DICT, config=config)
                table_data = extract_table_from_ocr_data(data, img_processed)
                
                if table_data and len(table_data) > 1:
                    # Check if this result looks better (more columns, more structured)
                    avg_cols = sum(len(row) for row in table_data) / len(table_data)
                    if best_table_data is None or avg_cols > sum(len(row) for row in best_table_data) / len(best_table_data):
                        best_table_data = table_data
                        best_text = text
                        print(f"    - Config {i+1}: Found better table structure ({len(table_data)} rows, avg {avg_cols:.1f} cols)")
                        break
            except Exception as e:
                print(f"    - Config {i+1}: Failed ({e})")
                continue
        
        # Use the best result or fallback
        if not best_table_data:
            text = pytesseract.image_to_string(img_processed, config='--psm 6 -c preserve_interword_spaces=1')
            best_text = text
        
        print(f"  - Found {len(best_table_data) if best_table_data else 0} table rows")
        
        doc = Document()
        doc.add_heading(f'Enhanced OCR Table: {os.path.basename(image_path)}', level=1)
        
        # Use the best table data found
        final_table_data = best_table_data
        final_text = best_text if best_table_data else text
        
        if final_table_data and len(final_table_data) > 1:  # If we have table data with multiple rows
            print("  - Creating Word table with proper formatting")
            create_word_table(doc, final_table_data)
        else:
            # Fallback: try to parse text into table format
            fallback_table_data = parse_text_to_table(final_text)
            if fallback_table_data and len(fallback_table_data) > 1:
                print("  - Creating Word table from text parsing")
                create_word_table(doc, fallback_table_data)
            else:
                # If no table structure detected, add as paragraphs
                print("  - No table structure detected, adding as formatted text")
                doc.add_paragraph("Raw extracted text:")
                for line in final_text.splitlines():
                    if line.strip():
                        doc.add_paragraph(line)
        
        doc.save(output_path)
        print(f"✅ Successfully created {output_path}")
        return True
    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        return False

# Test with any available image
if __name__ == "__main__":
    # Look for image files
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_path = None
    
    # Find the first image file in the current directory
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_path = file
            break
    
    if image_path:
        output_path = f"enhanced_without_latex_{os.path.splitext(image_path)[0]}.docx"
        print("🚀 Enhanced OCR Table Extraction (without LaTeX OCR)")
        print("=" * 60)
        success = process_image_to_docx(image_path, output_path)
        if success:
            print("\n🎉 Processing complete!")
            print("Enhanced features demonstrated:")
            print("  ✅ Image preprocessing (contrast, sharpness, noise reduction)")
            print("  ✅ Multiple OCR configuration testing")
            print("  ✅ Coordinate-based table structure detection")
            print("  ✅ Mathematical content detection (ready for LaTeX OCR)")
            print("  ✅ Professional Word table formatting")
            print("  ✅ Bold headers and grid borders")
            print("\n💡 When LaTeX OCR is installed, mathematical expressions will be further enhanced!")
        else:
            print("\n❌ Processing failed")
    else:
        print("No image files found in the current directory.")
        print("Please place your table image in this folder and run again.")
        print(f"Supported formats: {', '.join(image_extensions)}")
