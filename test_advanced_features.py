"""
Test script for advanced OCR features
"""

import os
import sys
import time

def check_all_dependencies():
    """Comprehensive dependency check"""
    print("🔍 CHECKING ALL ADVANCED DEPENDENCIES")
    print("=" * 60)
    
    dependencies = {
        'Core Image Processing': [
            ('Pillow', 'PIL'),
            ('OpenCV', 'cv2'),
            ('NumPy', 'numpy')
        ],
        'PDF Support': [
            ('PyMuPDF', 'fitz'),
            ('pdf2image', 'pdf2image')
        ],
        'OCR & LaTeX': [
            ('pytesseract', 'pytesseract'),
            ('LaTeX OCR', 'pix2tex.cli')
        ],
        'Deep Learning': [
            ('PyTorch', 'torch'),
            ('TorchVision', 'torchvision'),
            ('Transformers', 'transformers')
        ],
        'AI Enhancement': [
            ('Accelerate', 'accelerate'),
            ('Protobuf', 'google.protobuf')
        ],
        'Document Processing': [
            ('python-docx', 'docx'),
            ('lxml', 'lxml')
        ],
        'System Monitoring': [
            ('psutil', 'psutil'),
        ]
    }
    
    all_available = True
    feature_status = {}
    
    for category, deps in dependencies.items():
        print(f"\n📦 {category}:")
        category_available = True
        
        for name, module in deps:
            try:
                __import__(module)
                print(f"  ✅ {name}")
            except ImportError:
                print(f"  ❌ {name} - Missing")
                category_available = False
                all_available = False
        
        feature_status[category] = category_available
    
    print("\n" + "=" * 60)
    print("📊 FEATURE AVAILABILITY SUMMARY")
    print("=" * 60)
    
    for category, available in feature_status.items():
        status = "✅ AVAILABLE" if available else "❌ MISSING"
        print(f"{category:<25} {status}")
    
    return all_available, feature_status

def test_image_processing():
    """Test advanced image processing capabilities"""
    print("\n🖼️  TESTING ADVANCED IMAGE PROCESSING")
    print("=" * 60)
    
    try:
        import cv2
        import numpy as np
        from PIL import Image
        
        # Create a test image with text
        test_img = np.ones((200, 400, 3), dtype=np.uint8) * 255
        cv2.putText(test_img, 'x + 2y = 5', (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_img, '3x - y = 0', (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Convert to PIL
        pil_img = Image.fromarray(cv2.cvtColor(test_img, cv2.COLOR_BGR2RGB))
        
        # Test preprocessing
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        denoised = cv2.fastNlMeansDenoising(gray)
        
        print("  ✅ OpenCV image processing working")
        print("  ✅ Noise reduction applied")
        print("  ✅ PIL integration working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Image processing test failed: {e}")
        return False

def test_latex_ocr():
    """Test LaTeX OCR functionality"""
    print("\n🧮 TESTING LATEX OCR")
    print("=" * 60)
    
    try:
        from pix2tex.cli import LatexOCR
        
        print("  ✅ LaTeX OCR import successful")
        print("  ⏳ Initializing LaTeX OCR model (this may take a moment)...")
        
        # Initialize model
        model = LatexOCR()
        print("  ✅ LaTeX OCR model initialized")
        
        return True
        
    except Exception as e:
        print(f"  ❌ LaTeX OCR test failed: {e}")
        return False

def test_mathematical_detection():
    """Test mathematical content detection"""
    print("\n🔢 TESTING MATHEMATICAL CONTENT DETECTION")
    print("=" * 60)
    
    # Import the detection function
    sys.path.append('.')
    try:
        from enhanced_main import is_mathematical_content
        
        test_cases = [
            ("x + 2y = 5", True),
            ("3x - y = 0", True),
            ("α + β = γ", True),
            ("1/2 + 3/4", True),
            ("Regular text", False),
            ("2x² + 3x - 1 = 0", True),
            ("Name Age City", False),
            ("∫ f(x) dx", False),  # This might not be detected by current patterns
            ("a₁ + a₂ = b", True),
        ]
        
        correct = 0
        for text, expected in test_cases:
            result = is_mathematical_content(text)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{text}' → {'MATH' if result else 'TEXT'}")
            if result == expected:
                correct += 1
        
        accuracy = (correct / len(test_cases)) * 100
        print(f"\n  📊 Detection accuracy: {accuracy:.1f}% ({correct}/{len(test_cases)})")
        
        return accuracy > 70  # Consider successful if >70% accurate
        
    except Exception as e:
        print(f"  ❌ Mathematical detection test failed: {e}")
        return False

def test_pdf_support():
    """Test PDF processing capabilities"""
    print("\n📄 TESTING PDF SUPPORT")
    print("=" * 60)
    
    try:
        import fitz  # PyMuPDF
        from pdf2image import convert_from_path
        
        print("  ✅ PyMuPDF import successful")
        print("  ✅ pdf2image import successful")
        print("  ✅ PDF processing capabilities available")
        
        return True
        
    except Exception as e:
        print(f"  ❌ PDF support test failed: {e}")
        return False

def test_performance_monitoring():
    """Test system performance monitoring"""
    print("\n📊 TESTING PERFORMANCE MONITORING")
    print("=" * 60)
    
    try:
        import psutil
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        print(f"  ✅ CPU Usage: {cpu_percent}%")
        print(f"  ✅ Memory Usage: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")
        print(f"  ✅ Disk Usage: {disk.percent}%")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Performance monitoring test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🚀 ENHANCED OCR TABLE EXTRACTION - COMPREHENSIVE TEST")
    print("=" * 80)
    
    start_time = time.time()
    
    # Check dependencies
    all_deps, feature_status = check_all_dependencies()
    
    # Run feature tests
    test_results = {}
    
    if feature_status.get('Core Image Processing', False):
        test_results['Image Processing'] = test_image_processing()
    
    if feature_status.get('OCR & LaTeX', False):
        test_results['LaTeX OCR'] = test_latex_ocr()
        test_results['Math Detection'] = test_mathematical_detection()
    
    if feature_status.get('PDF Support', False):
        test_results['PDF Support'] = test_pdf_support()
    
    if feature_status.get('System Monitoring', False):
        test_results['Performance Monitoring'] = test_performance_monitoring()
    
    # Summary
    end_time = time.time()
    test_duration = end_time - start_time
    
    print("\n" + "=" * 80)
    print("🎯 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
    
    print(f"\n📊 Overall Results: {passed_tests}/{total_tests} tests passed")
    print(f"⏱️  Test Duration: {test_duration:.2f} seconds")
    
    if all_deps and passed_tests == total_tests:
        print("\n🎉 ALL SYSTEMS GO! Enhanced OCR is ready for production use.")
        print("\nRecommended usage:")
        print("  • For advanced features: python enhanced_main.py")
        print("  • For standard features: python main.py")
        print("  • For single image test: python test_single_image.py")
    else:
        print("\n⚠️  Some features are missing or failed tests.")
        print("   Install missing dependencies for full functionality.")
        print("   The system will still work with available features.")
    
    print("=" * 80)

if __name__ == "__main__":
    run_comprehensive_test()
