# Input/Output/Name
data: "dataset/data/dataset.pkl"
valdata: "dataset/data/val.pkl"
tokenizer: "dataset/tokenizer.json"
output_path: "outputs"
model_path: "checkpoints"
load_chkpt: null
save_freq: 5 # save every nth epoch
name: "pix2tex"

# Training parameters
epochs: 10
batchsize: 8

# Testing parameters
testbatchsize: 20
valbatches: 100
temperature: 0.2

# Optimizer configurations
optimizer: "Adam"
scheduler: "StepLR"
lr: 0.001
gamma: 0.9995
lr_step: 30
betas:
- 0.9
- 0.999

# Parameters for model architectures
max_width: 128
max_height: 128
min_width: 32
min_height: 32
channels: 1
patch_size: 32
# Encoder / Decoder
dim: 128
backbone_layers: 
  - 3
  - 4
  - 9
encoder_depth: 4
num_layers: 4
decoder_args: 
  cross_attend: true
  ff_glu: true
  attn_on_attn: false
  use_scalenorm: true
  rel_pos_bias: false
heads: 8
num_tokens: 8000
max_seq_len: 1024
encoder_structure: hybrid

# Other
seed: 42
id: null
sample_freq: 50
test_samples: 5
debug: True
pad: False

# Token ids
pad_token: 0
bos_token: 1
eos_token: 2
