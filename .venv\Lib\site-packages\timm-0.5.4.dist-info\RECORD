timm-0.5.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
timm-0.5.4.dist-info/LICENSE,sha256=cbERYg-jLBeoDM1tstp1nTGlkeSX2LXzghdPWdG1nUk,11343
timm-0.5.4.dist-info/METADATA,sha256=_o4k9R4FYZ1msA33NowGB-awL2oEA8zUsuZjK6xgB4c,36181
timm-0.5.4.dist-info/RECORD,,
timm-0.5.4.dist-info/WHEEL,sha256=ewwEueio1C2XeHTvT17n8dZUJgOvyCWCt0WVNLClP9o,92
timm-0.5.4.dist-info/top_level.txt,sha256=Mi21FFh17x9WpGQnfmIkFMK_kFw_m3eb_G3J8-UJ5SY,5
timm/__init__.py,sha256=9mTvNS2J6SoMaBrYEv6Xcmc9EGMytuFwkJgUYCXSArg,286
timm/__pycache__/__init__.cpython-313.pyc,,
timm/__pycache__/version.cpython-313.pyc,,
timm/data/__init__.py,sha256=UY7Kh_mkF-oCUoBc9hZUF_3PUlqmjm7kGhSHu9RuHoQ,553
timm/data/__pycache__/__init__.cpython-313.pyc,,
timm/data/__pycache__/auto_augment.cpython-313.pyc,,
timm/data/__pycache__/config.cpython-313.pyc,,
timm/data/__pycache__/constants.cpython-313.pyc,,
timm/data/__pycache__/dataset.cpython-313.pyc,,
timm/data/__pycache__/dataset_factory.cpython-313.pyc,,
timm/data/__pycache__/distributed_sampler.cpython-313.pyc,,
timm/data/__pycache__/loader.cpython-313.pyc,,
timm/data/__pycache__/mixup.cpython-313.pyc,,
timm/data/__pycache__/random_erasing.cpython-313.pyc,,
timm/data/__pycache__/real_labels.cpython-313.pyc,,
timm/data/__pycache__/tf_preprocessing.cpython-313.pyc,,
timm/data/__pycache__/transforms.cpython-313.pyc,,
timm/data/__pycache__/transforms_factory.cpython-313.pyc,,
timm/data/auto_augment.py,sha256=C4rrMeP0oAAhqPLzdEc_MBimKGnmaq-J1e6ik0XO3Pg,31686
timm/data/config.py,sha256=grh9sGvu3YNQJm0pOd09ZVRvz8dnMdbbqoFJDLT_TJw,2915
timm/data/constants.py,sha256=xc_A1oVqqTSSr9fNPijM3eZPm8bgbh8OEhwdsS-XwlQ,303
timm/data/dataset.py,sha256=XUBknhmSG8iuGJrZKF47emzVTX1vgxxBN3uc0E3p40Q,4805
timm/data/dataset_factory.py,sha256=FR5gDs5vofvjrdDrs-SaHI-5jb56BVqp4ChrJT-TreI,5533
timm/data/distributed_sampler.py,sha256=ZQ7KA3xEyWDLumq2aHjjQy9o_SpZgkdk8dmF-eP9BsQ,5125
timm/data/loader.py,sha256=h087Nqq-5Ct2AN8g_uqDWESDgMlA9hYwT03Qlv4xBQU,9924
timm/data/mixup.py,sha256=xA8ZMdVoVIfwZMA9vMdLDkcosJYInaZnVsQW1QX3ano,14722
timm/data/parsers/__init__.py,sha256=f1ffuO9Pj74PBI2y3h1iweooYJlTJlADGs8wdQuaZmw,42
timm/data/parsers/__pycache__/__init__.cpython-313.pyc,,
timm/data/parsers/__pycache__/class_map.cpython-313.pyc,,
timm/data/parsers/__pycache__/constants.cpython-313.pyc,,
timm/data/parsers/__pycache__/parser.cpython-313.pyc,,
timm/data/parsers/__pycache__/parser_factory.cpython-313.pyc,,
timm/data/parsers/__pycache__/parser_image_folder.cpython-313.pyc,,
timm/data/parsers/__pycache__/parser_image_in_tar.cpython-313.pyc,,
timm/data/parsers/__pycache__/parser_image_tar.cpython-313.pyc,,
timm/data/parsers/__pycache__/parser_tfds.cpython-313.pyc,,
timm/data/parsers/class_map.py,sha256=rYqhlPYplTRj86FH1CZRf76NB5zUECX4-D93T-u-SjQ,759
timm/data/parsers/constants.py,sha256=X4eulfViOcrn8_k-Awi6LTGHFK5vZKXeS8exbD_KtT4,43
timm/data/parsers/parser.py,sha256=BoIya5V--BuyXWEeYzi-rg8wsXI0UiEiuxQxkho0dCY,487
timm/data/parsers/parser_factory.py,sha256=Z6Vku9nKq1lcKgqwjuYCdid4lJyJH7Pyf9QnoVvHbGU,1078
timm/data/parsers/parser_image_folder.py,sha256=Up4Yv5NPvE5xkXv2AtoBc2FQY9Xt5AqUOkDD5Qr0OMs,2508
timm/data/parsers/parser_image_in_tar.py,sha256=VNRtMtJX8WkcNPxfQ0THVVb2fFwAH8f7m9-RskaK0Ak,8987
timm/data/parsers/parser_image_tar.py,sha256=E48D2RfbCftAty7mXtdZIX8d0yIPCEQIagLVkliEJ1o,2589
timm/data/parsers/parser_tfds.py,sha256=iZ6F8GGw-YbqN8DYOLtqZLTwQwjOLG5So9Rpt614lhY,15819
timm/data/random_erasing.py,sha256=zCv0vGv32QCHUIamd_HC_GnjLlODoyJBDoRuV7S2XCI,4767
timm/data/real_labels.py,sha256=D9pgNrsyiPIZTDVYRqFmkIYyJi-Dplql4RONZ3NNFTM,1590
timm/data/tf_preprocessing.py,sha256=vuJSsleBnS41C9upL4JTuO399nrojHV_WCSwtQrv694,9120
timm/data/transforms.py,sha256=0mPf22INsd3XPuBwdAAuhUptWjC-j-b_hFija_Irw2k,6194
timm/data/transforms_factory.py,sha256=8UuHo_2DG8eYnYMAHDv2BY4uSob6PokqEJ-CmC5Rk4k,8351
timm/loss/__init__.py,sha256=iCNB9bUAf69neNe1_XO0eeg1QXuxu6jRTAuy4V9yFL8,245
timm/loss/__pycache__/__init__.cpython-313.pyc,,
timm/loss/__pycache__/asymmetric_loss.cpython-313.pyc,,
timm/loss/__pycache__/binary_cross_entropy.cpython-313.pyc,,
timm/loss/__pycache__/cross_entropy.cpython-313.pyc,,
timm/loss/__pycache__/jsd.cpython-313.pyc,,
timm/loss/asymmetric_loss.py,sha256=YkMktzxiXncKK_GF5yBGDONOUENSdpzb7FJluCxuSlw,3322
timm/loss/binary_cross_entropy.py,sha256=gs6iNMKB2clMixxFnIhYKOxZ7LwFWWiHGQhIzjqgAA4,2030
timm/loss/cross_entropy.py,sha256=XDE19FnhYjeudAerb6UulIID34AmZoXQ1CPEAjEkCQM,1145
timm/loss/jsd.py,sha256=MFe8H_JC1srFE_FKinF7jMVIQYgNWgeT7kZL9WeIXGI,1595
timm/models/__init__.py,sha256=YvxGghp2a4O-xIlWtOFEJFMM1mCElnuZtNG525WiTtU,1784
timm/models/__pycache__/__init__.cpython-313.pyc,,
timm/models/__pycache__/beit.cpython-313.pyc,,
timm/models/__pycache__/byoanet.cpython-313.pyc,,
timm/models/__pycache__/byobnet.cpython-313.pyc,,
timm/models/__pycache__/cait.cpython-313.pyc,,
timm/models/__pycache__/coat.cpython-313.pyc,,
timm/models/__pycache__/convit.cpython-313.pyc,,
timm/models/__pycache__/convmixer.cpython-313.pyc,,
timm/models/__pycache__/convnext.cpython-313.pyc,,
timm/models/__pycache__/crossvit.cpython-313.pyc,,
timm/models/__pycache__/cspnet.cpython-313.pyc,,
timm/models/__pycache__/densenet.cpython-313.pyc,,
timm/models/__pycache__/dla.cpython-313.pyc,,
timm/models/__pycache__/dpn.cpython-313.pyc,,
timm/models/__pycache__/efficientnet.cpython-313.pyc,,
timm/models/__pycache__/efficientnet_blocks.cpython-313.pyc,,
timm/models/__pycache__/efficientnet_builder.cpython-313.pyc,,
timm/models/__pycache__/factory.cpython-313.pyc,,
timm/models/__pycache__/features.cpython-313.pyc,,
timm/models/__pycache__/fx_features.cpython-313.pyc,,
timm/models/__pycache__/ghostnet.cpython-313.pyc,,
timm/models/__pycache__/gluon_resnet.cpython-313.pyc,,
timm/models/__pycache__/gluon_xception.cpython-313.pyc,,
timm/models/__pycache__/hardcorenas.cpython-313.pyc,,
timm/models/__pycache__/helpers.cpython-313.pyc,,
timm/models/__pycache__/hrnet.cpython-313.pyc,,
timm/models/__pycache__/hub.cpython-313.pyc,,
timm/models/__pycache__/inception_resnet_v2.cpython-313.pyc,,
timm/models/__pycache__/inception_v3.cpython-313.pyc,,
timm/models/__pycache__/inception_v4.cpython-313.pyc,,
timm/models/__pycache__/levit.cpython-313.pyc,,
timm/models/__pycache__/mlp_mixer.cpython-313.pyc,,
timm/models/__pycache__/mobilenetv3.cpython-313.pyc,,
timm/models/__pycache__/nasnet.cpython-313.pyc,,
timm/models/__pycache__/nest.cpython-313.pyc,,
timm/models/__pycache__/nfnet.cpython-313.pyc,,
timm/models/__pycache__/pit.cpython-313.pyc,,
timm/models/__pycache__/pnasnet.cpython-313.pyc,,
timm/models/__pycache__/registry.cpython-313.pyc,,
timm/models/__pycache__/regnet.cpython-313.pyc,,
timm/models/__pycache__/res2net.cpython-313.pyc,,
timm/models/__pycache__/resnest.cpython-313.pyc,,
timm/models/__pycache__/resnet.cpython-313.pyc,,
timm/models/__pycache__/resnetv2.cpython-313.pyc,,
timm/models/__pycache__/rexnet.cpython-313.pyc,,
timm/models/__pycache__/selecsls.cpython-313.pyc,,
timm/models/__pycache__/senet.cpython-313.pyc,,
timm/models/__pycache__/sknet.cpython-313.pyc,,
timm/models/__pycache__/swin_transformer.cpython-313.pyc,,
timm/models/__pycache__/tnt.cpython-313.pyc,,
timm/models/__pycache__/tresnet.cpython-313.pyc,,
timm/models/__pycache__/twins.cpython-313.pyc,,
timm/models/__pycache__/vgg.cpython-313.pyc,,
timm/models/__pycache__/visformer.cpython-313.pyc,,
timm/models/__pycache__/vision_transformer.cpython-313.pyc,,
timm/models/__pycache__/vision_transformer_hybrid.cpython-313.pyc,,
timm/models/__pycache__/vovnet.cpython-313.pyc,,
timm/models/__pycache__/xception.cpython-313.pyc,,
timm/models/__pycache__/xception_aligned.cpython-313.pyc,,
timm/models/__pycache__/xcit.cpython-313.pyc,,
timm/models/beit.py,sha256=xUTeREocSxQizBpZh9suEzSr6T7J0wxTCV-LU1AhVRw,18558
timm/models/byoanet.py,sha256=Oq7z6zIGNUAYtrhalg1v-PM1GUY60rTJgtZFHZESc4o,18350
timm/models/byobnet.py,sha256=Ztjh8PPTWtiI9c6qDmPXj_tXAzqGGe1gCTAkLtxnRcc,62040
timm/models/cait.py,sha256=AT5n5q3rzZQL8qdcPg27Q2YlHxHXJ-tUbEpbhwuKEV0,14940
timm/models/coat.py,sha256=Mf7HSiTcilmiJn5u07QUbhz713G28CCiD12Be8mRuAs,26936
timm/models/convit.py,sha256=CpsyDS0P-PlrAs-hHsXMJhLXR9YPLGYfGCsDk3t42S4,13952
timm/models/convmixer.py,sha256=YT1IrJaUb2HFyvVYsGJN9Fb4i4eYVGxPPjJMfffvG0Q,3631
timm/models/convnext.py,sha256=jTdEBzMG9ZH_zsRXKN-2VeGfpodIWUQlphf6imnFPSg,17427
timm/models/crossvit.py,sha256=CP52LuFfiGTMs3iQIFJYmZSkDl2pYQaVARddYl9kOtI,22472
timm/models/cspnet.py,sha256=sqHI7IU9sqo1NuwGEhoHvUIzMp95MWFQHA9xDgsXMWg,18221
timm/models/densenet.py,sha256=7BW80_eMdBd54qOygP0jQGDedk9JSy1YPu0x2F0oBDQ,15611
timm/models/dla.py,sha256=rx7v7egYQocwGtr5TIeslP0AaJ88oFr0MdK4xzr2XlY,17205
timm/models/dpn.py,sha256=tFcBpES6yFM8jeyvI-giarXTz9YEZy2fex2vSQ7Lnhg,12448
timm/models/efficientnet.py,sha256=xKOm9HrD3AqF1In6ahRNPGP8KF7NuhL7W7W7PmcqPT8,97919
timm/models/efficientnet_blocks.py,sha256=i32acRskLshWDVKxkCL8T728baoD80FQLb-6zl-wi00,12442
timm/models/efficientnet_builder.py,sha256=ovUDS8FzdJqqZhI7hgay85kFMPhN87VQXvwEL0TRGkA,19459
timm/models/factory.py,sha256=EaYqVnAFXXGfTCPyxe0z-kAvjz4evZx9VE_mklkYmrE,3305
timm/models/features.py,sha256=DnO84Xi2mvMWIzK4kyN2SCePsMPxehSLWFZEGc_MItc,12155
timm/models/fx_features.py,sha256=Lu8PCRUiGe0SrjxTOyDb4BKVqzG1QN7E5nRFKErgEa0,2855
timm/models/ghostnet.py,sha256=D6tvP5ClRU6vUQzZWgk2exlhCCp71w0C-wwpDV17dy4,9326
timm/models/gluon_resnet.py,sha256=jTOSW9-gS6mu1V8omQM82nHvihn3oiI_5uPnDYqsua0,11362
timm/models/gluon_xception.py,sha256=UF0JRxJ1vPaQhYuNxz0hGjlS5IPHPBgrt2HSk4r5-Sk,8705
timm/models/hardcorenas.py,sha256=rpTmpo_aHC-i64-f2txejPCaitStjlYu0_ERX_Bul6o,8036
timm/models/helpers.py,sha256=hYyaTpH_4Rcn93prm70f-E_ahqM9HQJ0Cw0ma4qnpAo,22659
timm/models/hrnet.py,sha256=8LtXT2oEF9twTHWrwJW18S9OUVXC9RqHwTCnckIXDOM,29402
timm/models/hub.py,sha256=AdmCNspFlzLLohb3tFPiHv6R3ZFyXM12CxpjNM5gyts,5988
timm/models/inception_resnet_v2.py,sha256=Wg19hoczgHbWqHP5Uanf7zyhSUIYxs6P6RS6U3yB4uY,12464
timm/models/inception_v3.py,sha256=Cd7W-J6tao7jkQ9-sDvy0Guv1PvWFI81kxe5pYrSFbM,17469
timm/models/inception_v4.py,sha256=k2Yqzw-Ak3YxhCmIHXOfBgulBDiOugdxMLNmROTOgpk,10804
timm/models/layers/__init__.py,sha256=CI57cbXYovHX-pFO8O42eQwAd8D_XAnaXPp0IEezKgU,2213
timm/models/layers/__pycache__/__init__.cpython-313.pyc,,
timm/models/layers/__pycache__/activations.cpython-313.pyc,,
timm/models/layers/__pycache__/activations_jit.cpython-313.pyc,,
timm/models/layers/__pycache__/activations_me.cpython-313.pyc,,
timm/models/layers/__pycache__/adaptive_avgmax_pool.cpython-313.pyc,,
timm/models/layers/__pycache__/attention_pool2d.cpython-313.pyc,,
timm/models/layers/__pycache__/blur_pool.cpython-313.pyc,,
timm/models/layers/__pycache__/bottleneck_attn.cpython-313.pyc,,
timm/models/layers/__pycache__/cbam.cpython-313.pyc,,
timm/models/layers/__pycache__/classifier.cpython-313.pyc,,
timm/models/layers/__pycache__/cond_conv2d.cpython-313.pyc,,
timm/models/layers/__pycache__/config.cpython-313.pyc,,
timm/models/layers/__pycache__/conv2d_same.cpython-313.pyc,,
timm/models/layers/__pycache__/conv_bn_act.cpython-313.pyc,,
timm/models/layers/__pycache__/create_act.cpython-313.pyc,,
timm/models/layers/__pycache__/create_attn.cpython-313.pyc,,
timm/models/layers/__pycache__/create_conv2d.cpython-313.pyc,,
timm/models/layers/__pycache__/create_norm_act.cpython-313.pyc,,
timm/models/layers/__pycache__/drop.cpython-313.pyc,,
timm/models/layers/__pycache__/eca.cpython-313.pyc,,
timm/models/layers/__pycache__/evo_norm.cpython-313.pyc,,
timm/models/layers/__pycache__/gather_excite.cpython-313.pyc,,
timm/models/layers/__pycache__/global_context.cpython-313.pyc,,
timm/models/layers/__pycache__/halo_attn.cpython-313.pyc,,
timm/models/layers/__pycache__/helpers.cpython-313.pyc,,
timm/models/layers/__pycache__/inplace_abn.cpython-313.pyc,,
timm/models/layers/__pycache__/lambda_layer.cpython-313.pyc,,
timm/models/layers/__pycache__/linear.cpython-313.pyc,,
timm/models/layers/__pycache__/median_pool.cpython-313.pyc,,
timm/models/layers/__pycache__/mixed_conv2d.cpython-313.pyc,,
timm/models/layers/__pycache__/mlp.cpython-313.pyc,,
timm/models/layers/__pycache__/non_local_attn.cpython-313.pyc,,
timm/models/layers/__pycache__/norm.cpython-313.pyc,,
timm/models/layers/__pycache__/norm_act.cpython-313.pyc,,
timm/models/layers/__pycache__/padding.cpython-313.pyc,,
timm/models/layers/__pycache__/patch_embed.cpython-313.pyc,,
timm/models/layers/__pycache__/pool2d_same.cpython-313.pyc,,
timm/models/layers/__pycache__/selective_kernel.cpython-313.pyc,,
timm/models/layers/__pycache__/separable_conv.cpython-313.pyc,,
timm/models/layers/__pycache__/space_to_depth.cpython-313.pyc,,
timm/models/layers/__pycache__/split_attn.cpython-313.pyc,,
timm/models/layers/__pycache__/split_batchnorm.cpython-313.pyc,,
timm/models/layers/__pycache__/squeeze_excite.cpython-313.pyc,,
timm/models/layers/__pycache__/std_conv.cpython-313.pyc,,
timm/models/layers/__pycache__/test_time_pool.cpython-313.pyc,,
timm/models/layers/__pycache__/trace_utils.cpython-313.pyc,,
timm/models/layers/__pycache__/weight_init.cpython-313.pyc,,
timm/models/layers/activations.py,sha256=iT_WSweK1B14fAamfJOFcgqQ5DoBI6Fvt-X4fRuYsSM,4040
timm/models/layers/activations_jit.py,sha256=BQI8MYjZhJx0w6yTUpsl_x_tg-XFdbYDkYy5EEBQYIQ,2529
timm/models/layers/activations_me.py,sha256=Qlrh-NWXxC6OsxI1wppeBdsd8UGWXT8ECH95tFaXGEQ,5886
timm/models/layers/adaptive_avgmax_pool.py,sha256=I-lZ-AdvlTYguuxyTBgDWcquqhFf00uVujfVqZfLv_A,3890
timm/models/layers/attention_pool2d.py,sha256=wYx25PT4KZNVPdHW07mDkT19SIEcEtz3SNzh88Q98tw,6866
timm/models/layers/blur_pool.py,sha256=gVZRqXFUpOuH_ui98sTSsXsmnWXBmy9PoPlXgaMV8Q4,1591
timm/models/layers/bottleneck_attn.py,sha256=HLuZbyep1Nf9Qq9Aei81kCzQMs6U1aQBQRLrOnjnkHo,6895
timm/models/layers/cbam.py,sha256=5A0MAd2BaBBYKzWjbN_t81olp1tDMAoun816OyT9bVA,4418
timm/models/layers/classifier.py,sha256=GHJ80KXZu8sXOLLAB5S8zODJUFNxcZ-iIlHrxO63ymU,2231
timm/models/layers/cond_conv2d.py,sha256=YMnfZ9MSQyqqPQ1VxZsZsWRG1FAgWNWl9MHzXWZ1mWE,5129
timm/models/layers/config.py,sha256=Nna27P_B1cy4obs4Ma5_sd5VlXe_sCEYjv9ttyNABcE,3069
timm/models/layers/conv2d_same.py,sha256=2fv1zNaZJZgFJ1P5quM9pikQV-Pf620HRLX8ygQFHGU,1490
timm/models/layers/conv_bn_act.py,sha256=gTJJYA4-QOpL4prtWHc5aPfWbqrR10SAvaUwfnRIPN4,1404
timm/models/layers/create_act.py,sha256=CmKQZ1vu3bT7EVwmfLjSSEY6qLwR3Dea7ZR8mul5xdI,5359
timm/models/layers/create_attn.py,sha256=Z7uwbr07LDSBEz8kUaDwCuw5JX3xM-xTkzvsCXV4Duw,3526
timm/models/layers/create_conv2d.py,sha256=UH4RvUhNCz1ohkM3ayC6kXwBLuO1ro8go4jpgLgyjLs,1500
timm/models/layers/create_norm_act.py,sha256=Ln0UOFtMIWVJeAePJWx5qsLNSDl-b7Br3Z7XSzEVBqc,3450
timm/models/layers/drop.py,sha256=TVGBZLvuSEQrpNLH_FZOdUqanFRuY9a9Qq6yN8JmTgs,6732
timm/models/layers/eca.py,sha256=MiVhboDUqLUfeubpypWfaR3LMLHwgLCNsWO3iemcQFs,6386
timm/models/layers/evo_norm.py,sha256=HiGnhaOUYKIhBnVG6d9bwCGHO_BhsG7e75hDRQfu0E4,3519
timm/models/layers/gather_excite.py,sha256=53DHt6cySjPqd9NW3voZuhw8b9nUzvsG9NVl_D-9NAo,3824
timm/models/layers/global_context.py,sha256=aZWvij4J-T5I1rdTK725D6R0fDuJyYPDaXvl36QMmkw,2445
timm/models/layers/halo_attn.py,sha256=zMJkf9S-ocCvrfvWOe0I97UHTpEQIkP381DON3OXm-c,10662
timm/models/layers/helpers.py,sha256=pHZa-j8xR-BWLgflFyzvtwn9o1m52t5V__KauMkOutA,748
timm/models/layers/inplace_abn.py,sha256=4-8ZyftTMoNaU59NvUaQH8qpqBYeQDTIjjvYEwo1Lzg,3353
timm/models/layers/lambda_layer.py,sha256=WSlH2buUBDxRd5RFou5IN7iTFGS4nZL--j-XhrdOci8,5941
timm/models/layers/linear.py,sha256=baS2Wpl0vYELnvhnQ6Lw65jVotaJz5iGbroJJ9JmIRM,743
timm/models/layers/median_pool.py,sha256=b02v36VGvs_gCY9NhVwU7-mglcXJHzrJVzcEpEUuHBI,1737
timm/models/layers/mixed_conv2d.py,sha256=mRSmAUtpgHya_RdnUq4j85K5QS7JFTdSPUXOUTKgpmA,1843
timm/models/layers/mlp.py,sha256=hOai0-VxpEFGV_Lf4sb82Ko2vU5gJK-2U2F2GvnIFtU,4097
timm/models/layers/non_local_attn.py,sha256=58GuC8xjOFedZjWClPO_Bc9UJRl3J7giVm60F6bcsYo,6209
timm/models/layers/norm.py,sha256=qC3u_ncT88rnhc_Z3c6KP0JRMveDgHfoVQuVDV8sPjg,876
timm/models/layers/norm_act.py,sha256=WPUkzeBWPw-IHF-FoqYTyeoALxVXxc0bq5gW4PJnRBA,3545
timm/models/layers/padding.py,sha256=BjbtxJmui-DyeroZohYMdRAj5mR4o6pWgW04imi3hDI,2167
timm/models/layers/patch_embed.py,sha256=GXjHXzEvmDAOndeBuO3z7C4pkON1hzZeGi7NovFt1A4,1490
timm/models/layers/pool2d_same.py,sha256=UsmtWna5k5kfVTP25T1-OKJOgtcfBQCqSy0FmaZbjRw,3045
timm/models/layers/selective_kernel.py,sha256=Ltlwuw3gFRlmAUpxt0i44PGGaY1b1nNcfDO5AbqaK5U,5349
timm/models/layers/separable_conv.py,sha256=WrbdyyBteT3eqe5rN7TIrkqlWXfChP8f_0AMJS1sNDM,2528
timm/models/layers/space_to_depth.py,sha256=P-9czBYvbbPR7Ji-fB0dVFA_yABJQoO9C3IA4sL9ttI,1750
timm/models/layers/split_attn.py,sha256=HHpEHYCiBk0ecrnZpTVbb-4r4q1065WiCR6aC3AGfMU,3085
timm/models/layers/split_batchnorm.py,sha256=4ghGtliK5z0ZnzR29zJB_rN9BJPiGuy1PSltmVyF5Ww,3441
timm/models/layers/squeeze_excite.py,sha256=krSwp4li7AWhSP4zJLMZUwl_XIdHJk_Eb81iQ_s8FnA,3018
timm/models/layers/std_conv.py,sha256=zYhcKCbE0_Rqn422gEM9gr3LeBewu0CXKqvlsa9-M2Q,5887
timm/models/layers/test_time_pool.py,sha256=oQbw-agOC6sc3MjvbvsxrBtDa62r9gYiTEW01ICqjDY,1995
timm/models/layers/trace_utils.py,sha256=cbZufOaGKmhTGEMc52QAnqzGRTfn4vvzqsAOJaLKJQ8,335
timm/models/layers/weight_init.py,sha256=EbhK0ecja64ZJ4eXLpDVs7kLseumJBlSzWgbG9v5HL4,3324
timm/models/levit.py,sha256=2jBuY_jBe-orwUyhs7xfAs4sV0_aTSkZgPdWQh0-yi8,21163
timm/models/mlp_mixer.py,sha256=TC0HsK--Bxrp0aZddfCoO9bq5wpkAYtaJbElLmXbgCw,26042
timm/models/mobilenetv3.py,sha256=XJimeE7IFicHJtwqEd3SBOPhA-HJwgNUHF7oox2O_AE,26586
timm/models/nasnet.py,sha256=dZq20FEUebcRddTkpPVWpzL2HcATVttE4Ma6VsXIAzw,25944
timm/models/nest.py,sha256=dyR4nEKmtBdIp0nH1Fz6VcuQeAxYDF-8qQa8ROXTETc,19521
timm/models/nfnet.py,sha256=4A7W_-2Q-e2x5V1ovRYt0zzq7PNMvzG9RAlCeMPmtjU,41006
timm/models/pit.py,sha256=LpXaTnzYZ5WCfCtDnXjG2CJTh-A_lLEiqoRa2PrisME,13037
timm/models/pnasnet.py,sha256=8FISd7eO2N7flUeSsQwSyVm2DzhJiQWbm-ECtxwjh9w,14961
timm/models/pruned/ecaresnet101d_pruned.txt,sha256=1zA7XaxsTnFJxZ9PMbfMVST7wPSQcAV-UzSgdFfGgYY,8734
timm/models/pruned/ecaresnet50d_pruned.txt,sha256=J4AlTwabaSB6-XrINCPCDWMiM_FrdNjuJN_JJRb89WE,4520
timm/models/pruned/efficientnet_b1_pruned.txt,sha256=pNDm1EENJYMT8-GjXZ3kXWCXADLDun-4jfigh74RELE,18596
timm/models/pruned/efficientnet_b2_pruned.txt,sha256=e_oaSVM-Ux3NMVARynJ74YwjzxuBAX_w7kzOw9Ml3gM,18676
timm/models/pruned/efficientnet_b3_pruned.txt,sha256=A1DJEwjEmrg8oUr0QwzwBkdAJV3dVeUFjnO9pNC_0Pg,21133
timm/models/registry.py,sha256=YRSPZojqIfVIQ4z5yLK-LnNZZ2FXJ__cP3bNFVVbVck,5680
timm/models/regnet.py,sha256=Pk5Ybc-GP-6yHQ2uSn5nDQ3jkBl9C2rkCUW8wyluDfs,20998
timm/models/res2net.py,sha256=Przr9hV-FJQS7Ez0z_exGsPslnLefynBN4mAPGAtFH8,7865
timm/models/resnest.py,sha256=rOwUAV4vQHb89q54qVIra8eOXlpklqr8Aep7rd1Axig,10092
timm/models/resnet.py,sha256=zkPFrwSd2KXqCSMT7B0Zc_mXQ_D4mFKDkaigEF4XoW4,65986
timm/models/resnetv2.py,sha256=-4BvS5XIMFccvmaPRDWU6z7geIfaidix5-SwrVuvhug,28274
timm/models/rexnet.py,sha256=ee5vM9NPLRA-Ilo6ifJp39lGtXdlenAN1DALeVLHff8,9228
timm/models/selecsls.py,sha256=YrNit4pigYrqKuQPsBi8nzfXn4lVU8hva17bAcCB1a4,13124
timm/models/senet.py,sha256=MEvKLC_Msa1nK-_fzCDSjTgk9ysS4hHu7qmGBi4n7iU,17642
timm/models/sknet.py,sha256=3_MNhX4Jg3A8TAV7vJxcVaMKYbUXO-rF9JbNRliPD-w,8742
timm/models/swin_transformer.py,sha256=5So8rK82wHSu2ZkfHCptDWzrg4yZOGsLnM_iBd5PK3c,27372
timm/models/tnt.py,sha256=J28c3t1go0DPkVLf2Om6fwEES10cvCSxP9Ailp7dDt8,11209
timm/models/tresnet.py,sha256=2dUbOytAqAgFxNog9qFtM8M4wwep9-LOaOsgl7RanDs,11596
timm/models/twins.py,sha256=wL3gkk_Gwe220k_Td_VIHDcA1AiLBouGV4HRZJubjnE,17346
timm/models/vgg.py,sha256=ZXmmA7kUrvkwZJtAjjx_oDPzIY3S5yLWhGImCA0v1NE,10455
timm/models/visformer.py,sha256=iN7DjQ1aI3EQCil8g9Jv_S6a7IR2639BVObnVFXKwk0,16086
timm/models/vision_transformer.py,sha256=XINVbQd-cQTX0xBlnMSS1ZGy6rLUGYej-4iVs16rGNs,46475
timm/models/vision_transformer_hybrid.py,sha256=XGcP5sUmw-zixwi7UKDS3jx6PvAY0lUWnOc3DmSAX5s,16099
timm/models/vovnet.py,sha256=fnzwB_ciOog2Hbt-340QAT_UQq96HqIPRGNF09IuCcg,13845
timm/models/xception.py,sha256=lDcnUwDMVScwXzrplIeKPpSx_-Lr2ItfI0VPhP1KpjY,7388
timm/models/xception_aligned.py,sha256=BdbW2j5ql4mD0-TAqV6FtlnxSXSaK7Oe6TR-5TegzCQ,8948
timm/models/xcit.py,sha256=LMcM5J8aaKN119DYYZeisaaYNwgdMSlxYQujld4dMJE,35892
timm/optim/__init__.py,sha256=z1mMVKZ9loGnBRnS8SeE_F259As2DbXSXzHcRpuWy2E,484
timm/optim/__pycache__/__init__.cpython-313.pyc,,
timm/optim/__pycache__/adabelief.cpython-313.pyc,,
timm/optim/__pycache__/adafactor.cpython-313.pyc,,
timm/optim/__pycache__/adahessian.cpython-313.pyc,,
timm/optim/__pycache__/adamp.cpython-313.pyc,,
timm/optim/__pycache__/adamw.cpython-313.pyc,,
timm/optim/__pycache__/lamb.cpython-313.pyc,,
timm/optim/__pycache__/lars.cpython-313.pyc,,
timm/optim/__pycache__/lookahead.cpython-313.pyc,,
timm/optim/__pycache__/madgrad.cpython-313.pyc,,
timm/optim/__pycache__/nadam.cpython-313.pyc,,
timm/optim/__pycache__/nvnovograd.cpython-313.pyc,,
timm/optim/__pycache__/optim_factory.cpython-313.pyc,,
timm/optim/__pycache__/radam.cpython-313.pyc,,
timm/optim/__pycache__/rmsprop_tf.cpython-313.pyc,,
timm/optim/__pycache__/sgdp.cpython-313.pyc,,
timm/optim/adabelief.py,sha256=n8nVbFX0TrCgkI98s7sV9D1l_rwPoqgVdfUW1KxGMPY,9827
timm/optim/adafactor.py,sha256=UOYdbisCGOXJJF4sklBa4XEb3m68IyV6IkzcEopGack,7459
timm/optim/adahessian.py,sha256=vJtQ8bZTGLrkMYuGPOJdgO-5V8hjVvM2Il-HSqg59Ao,6535
timm/optim/adamp.py,sha256=PSJYfobQvxy9K0tdU6-mjaiF4BqhIXY9sHV2vposx5I,3574
timm/optim/adamw.py,sha256=OKSBGfaWs6DJC1aXJHadAp4FADAnDDwb-ZRKuPao7zk,5147
timm/optim/lamb.py,sha256=II9zTpcxWzNqgk4K-bs5VGKlQPabUolSAmHkcSjsqSU,9184
timm/optim/lars.py,sha256=8Ytu-q4FvXQWTEcP7R-8xSKdb72c2s1XhTvMzIshBME,5255
timm/optim/lookahead.py,sha256=nd42FXVedX6qlnyBXGMcxkj1IsUUOtwbVFa4dQYy83M,2463
timm/optim/madgrad.py,sha256=V3LJuPjGwiO7RdHAZFF0Qqa8JT8a9DJJLSEO2PCG7Ho,6893
timm/optim/nadam.py,sha256=ASEISt72rXnpfqVkKfgotJXBYpsyG9Pr17I8VFO6Eac,3871
timm/optim/nvnovograd.py,sha256=NkRLq007qqiRDrhqiZK1KP_kfCcFcDSYCWRcoYvddOQ,4856
timm/optim/optim_factory.py,sha256=te26CtKS1wOh1gwEeB6glHdMGxOyKH7xhtbC_V91upQ,8415
timm/optim/radam.py,sha256=dCeFJGKo5WC8w7Ad8tuldM6QFz41nYXJIYI5HkH6uxk,3468
timm/optim/rmsprop_tf.py,sha256=SX47YRaLPNB-YpJpLUbXqx21ZFoDPeqvpJX2kin4wCc,6143
timm/optim/sgdp.py,sha256=7f4ZMVHbjCTDTgPOZfE06S4lmdUBnIBCDr_Yzy1RFhY,2296
timm/scheduler/__init__.py,sha256=WhoyJyfj6SE2YIE06C1eMmnqr7tm5m17YG5s4uL9lXU,291
timm/scheduler/__pycache__/__init__.cpython-313.pyc,,
timm/scheduler/__pycache__/cosine_lr.cpython-313.pyc,,
timm/scheduler/__pycache__/multistep_lr.cpython-313.pyc,,
timm/scheduler/__pycache__/plateau_lr.cpython-313.pyc,,
timm/scheduler/__pycache__/poly_lr.cpython-313.pyc,,
timm/scheduler/__pycache__/scheduler.cpython-313.pyc,,
timm/scheduler/__pycache__/scheduler_factory.cpython-313.pyc,,
timm/scheduler/__pycache__/step_lr.cpython-313.pyc,,
timm/scheduler/__pycache__/tanh_lr.cpython-313.pyc,,
timm/scheduler/cosine_lr.py,sha256=VNWO_gQoFM436vnse7dgTZQtFbfx1PT3S-lwS1l4MLw,4161
timm/scheduler/multistep_lr.py,sha256=je8uCJHIlnyhvgFKhkrpSyQtCYl2G5QWWnZNVwPo8YQ,2098
timm/scheduler/plateau_lr.py,sha256=831LB8XE2nGCSmXSeWhSN8bDr_S4rS9mAR1TZNl-ttA,4140
timm/scheduler/poly_lr.py,sha256=k-XGB63zQdCYtUVP6WiUocaKgAV4dqwOHna9gw0p0tQ,4003
timm/scheduler/scheduler.py,sha256=t1F7sPeaMzTio6xeyxS9z2nLKMYt08c0xOPSEtCVUig,4750
timm/scheduler/scheduler_factory.py,sha256=hXi1jEFNLYuFqHPOOa1oW00g-dsqJ99ExhvjBAjSy0w,3682
timm/scheduler/step_lr.py,sha256=2L8uA_mq5_25jL0WASNnaQ3IkX9q5cZgE789PBNXryg,1902
timm/scheduler/tanh_lr.py,sha256=ApHa_ziKBwqZpcSm1xen_siQmFl-ja3yRJyck08F_04,3936
timm/utils/__init__.py,sha256=5Xoo-5kP6dZ5f6LV9u1DCRIqQrcX4OciHLs29DNcuEU,587
timm/utils/__pycache__/__init__.cpython-313.pyc,,
timm/utils/__pycache__/agc.cpython-313.pyc,,
timm/utils/__pycache__/checkpoint_saver.cpython-313.pyc,,
timm/utils/__pycache__/clip_grad.cpython-313.pyc,,
timm/utils/__pycache__/cuda.cpython-313.pyc,,
timm/utils/__pycache__/distributed.cpython-313.pyc,,
timm/utils/__pycache__/jit.cpython-313.pyc,,
timm/utils/__pycache__/log.cpython-313.pyc,,
timm/utils/__pycache__/metrics.cpython-313.pyc,,
timm/utils/__pycache__/misc.cpython-313.pyc,,
timm/utils/__pycache__/model.cpython-313.pyc,,
timm/utils/__pycache__/model_ema.cpython-313.pyc,,
timm/utils/__pycache__/random.cpython-313.pyc,,
timm/utils/__pycache__/summary.cpython-313.pyc,,
timm/utils/agc.py,sha256=6lZCChfbW0KGNMfkzztWD_NP87ESopjk24Xtb3WbBqU,1624
timm/utils/checkpoint_saver.py,sha256=RljigPicMAHnk48K2Qbl17cWnQepgO4QMZQ0FCjd8xw,6133
timm/utils/clip_grad.py,sha256=iYFEf7fvPbpyh5K1SI-EKey5Gqs2gztR9VUUGja0GB0,796
timm/utils/cuda.py,sha256=nerhMCalMMv1QHZXW1brcIvtzpWwalWYLTb6vJz2bnY,1703
timm/utils/distributed.py,sha256=MD1xa17GKPyPoUJ4lIkxucmD635GbKNuVvcsVCQlhsc,896
timm/utils/jit.py,sha256=OpEbtA3TgJTNDfKWX2oOsd1p4Sm5RQqZnZz5N78t2lk,648
timm/utils/log.py,sha256=BdZ2OqWo3v8d7wsDRJ-uACcoeNUhS8TJSwI3CYvq3Ss,1015
timm/utils/metrics.py,sha256=RSHpbbkyW6FsbxT6TzcBL7MZh4sv4A_GG1Bo8aN5qKc,901
timm/utils/misc.py,sha256=o6ZbvZJB-M6NX73E3ZFKzpKpbnV2bU-ZEj-RDlz-P58,644
timm/utils/model.py,sha256=IMc8JCt89gmCjlb68k-6RNb7wR4ZawWjG74Y4PMiSdo,12085
timm/utils/model_ema.py,sha256=PG0B6k198xf36sK1PrT32LaMe6zKlzUPPZmaPoSRbiQ,5670
timm/utils/random.py,sha256=Ysv6F3nIO8JYE8j6UrDxGyJDp3uNpq5v8U0KqL_8dic,178
timm/utils/summary.py,sha256=pdvBnXsLAS4CPGlMhB0lkUyizKGY4XP9M9f-VWlaJA0,1184
timm/version.py,sha256=YYYpMsC3ActrKnohyOQTZhA6i4ZGdIcgCekNZ1_03lo,22
